const jwt = require('jsonwebtoken');
const path = require('path');

// List of pages that don't require authentication
const publicPages = [
  'login.html',
  'forgot-password.html',
  'reset-password.html',
  'signup.html'
];

/**
 * Middleware to protect HTML pages from unauthorized access
 * This checks cookies for authentication rather than Authorization headers
 */
module.exports = function(req, res, next) {
  // Get the requested page from the URL
  const requestPath = req.path;
  const requestedPage = path.basename(requestPath);

  // Skip authentication for API routes
  if (requestPath.startsWith('/api/')) {
    return next();
  }

  // Skip authentication for public pages and assets
  if (
    publicPages.includes(requestedPage) ||
    requestPath.startsWith('/css/') ||
    requestPath.startsWith('/js/') ||
    requestPath.startsWith('/images/') ||
    requestPath === '/' ||
    requestPath === '/favicon.ico'
  ) {
    return next();
  }

  // Get token from cookies
  const token = req.cookies?.authToken;

  // No token means not authenticated
  if (!token) {
    // If it's an HTML page request, redirect to login
    if (requestPath.endsWith('.html') || requestPath === '/') {
      return res.redirect('/login.html?reason=auth_required');
    }
    // For non-HTML requests, return 401
    return res.status(401).json({ msg: 'Authentication required' });
  }

  try {
    // Get JWT secret from environment - NEVER use fallback in production
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not set in environment variables');
      // Clear the cookie and redirect to login with proper configuration
      res.clearCookie('authToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/'
      });
      if (requestPath.endsWith('.html') || requestPath === '/') {
        return res.redirect('/login.html?reason=server_config_error');
      }
      return res.status(500).json({ msg: 'Server configuration error' });
    }

    // Verify the token
    const decoded = jwt.verify(token, jwtSecret);

    // Token is valid, add user info to request and proceed
    req.user = decoded.user;
    next();
  } catch (err) {
    // Invalid token
    // Clear the invalid cookie with proper configuration
    res.clearCookie('authToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    });

    // Redirect to login for HTML pages
    if (requestPath.endsWith('.html') || requestPath === '/') {
      return res.redirect('/login.html?reason=session_expired');
    }

    // 401 for other requests
    return res.status(401).json({ msg: 'Invalid authentication token' });
  }
};