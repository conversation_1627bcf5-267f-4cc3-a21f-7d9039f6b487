'use strict';

module.exports = (sequelize, DataTypes) => {
  const State = sequelize.define('state', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    abbreviation: {
      type: DataTypes.STRING(2),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'states',
    timestamps: true,
    underscored: true
  });

  // We'll move the associations to the index.js file
  // to ensure all models are loaded first

  return State;
};