module.exports = (sequelize, DataTypes) => {
  const IncidentDetail = sequelize.define('incidentDetail', {
    incidentId: {
      type: DataTypes.INTEGER,
      field: 'incident_id',
      allowNull: false,
      references: {
        model: 'incidents',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    structureType: {
      type: DataTypes.STRING(100),
      field: 'structure_type'
    },
    smokeType: {
      type: DataTypes.STRING(100),
      field: 'smoke_type'
    },
    fireType: {
      type: DataTypes.STRING(100),
      field: 'fire_type'
    },
    waterLevel: {
      type: DataTypes.STRING(50),
      field: 'water_level'
    },
    waterType: {
      type: DataTypes.ENUM('flood', 'main_break', 'rescue', 'other'),
      field: 'water_type'
    },
    damageExtent: {
      type: DataTypes.STRING(100),
      field: 'damage_extent'
    },
    areaAffected: {
      type: DataTypes.STRING(100),
      field: 'area_affected'
    },
    evacuationStatus: {
      type: DataTypes.ENUM('none', 'advisory', 'mandatory'),
      defaultValue: 'none',
      field: 'evacuation_status'
    },
    peopleAffected: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'people_affected'
    },
    estimatedDamage: {
      type: DataTypes.DECIMAL(10, 2),
      field: 'estimated_damage'
    },
    responderCount: {
      type: DataTypes.INTEGER,
      field: 'responder_count'
    },
    propertyOwner: {
      type: DataTypes.STRING(255),
      field: 'property_owner'
    },
    secondaryOwner: {
      type: DataTypes.STRING(255),
      field: 'secondary_owner'
    },
    ownerContact: {
      type: DataTypes.STRING(100),
      field: 'owner_contact'
    },
    ownerAddress: {
      type: DataTypes.TEXT,
      field: 'owner_address'
    },
    ownerPhone: {
      type: DataTypes.STRING(20),
      field: 'owner_phone'
    },
    ownerEmail: {
      type: DataTypes.STRING(255),
      field: 'owner_email'
    },
    propertyValue: {
      type: DataTypes.DECIMAL(12, 2),
      field: 'property_value'
    },
    dwellingType: {
      type: DataTypes.STRING(100),
      field: 'dwelling_type'
    },
    yearBuilt: {
      type: DataTypes.INTEGER,
      field: 'year_built'
    },
    squareFootage: {
      type: DataTypes.INTEGER,
      field: 'square_footage'
    },
    bedrooms: {
      type: DataTypes.INTEGER,
      field: 'bedrooms'
    },
    bathrooms: {
      type: DataTypes.DECIMAL(3, 1),
      field: 'bathrooms'
    },
    responseDetails: {
      type: DataTypes.TEXT,
      field: 'response_details'
    },
    notes: {
      type: DataTypes.TEXT
    },
    smokeSeverity: {
      type: DataTypes.STRING(50),
      field: 'smoke_severity'
    },
    homeStories: {
      type: DataTypes.INTEGER,
      field: 'home_stories',
      validate: {
        min: 1,
        max: 10
      }
    },
    conditionsOnArrival: {
      type: DataTypes.TEXT,
      field: 'conditions_on_arrival'
    }
  }, {
    tableName: 'incident_details',
    underscored: true,
    indexes: [
      {
        fields: ['incident_id'],
        unique: true
      }
    ]
  });

  return IncidentDetail;
};
