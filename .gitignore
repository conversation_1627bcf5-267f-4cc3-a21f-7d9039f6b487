# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment variables
.env
.env.*

# Build output
dist/
build/
out/

# Log files
*.log
logs/

# Operating System Files
.DS_Store
Thumbs.db

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo


# Docker files (excluded from production)
Dockerfile
**/Dockerfile
docker-compose.yml
**/docker-compose.yml
.dockerignore
**/.dockerignore
docker/
**/docker/

# Development and testing files
tests/
test/
test-*.js
test-*.html
*test*.js
*test*.html
*-test.js
*-test.html
*.test.js
*.test.html

# Development scripts (keep essential production scripts)
firealerts-node/scripts/fix-*.js
firealerts-node/scripts/inspect-*.js
firealerts-node/scripts/migrate-*.js
firealerts-node/scripts/reseed-*.js
firealerts-node/scripts/run-*.js
firealerts-node/scripts/test-*.js
firealerts-node/scripts/*-test.js
firealerts-node/scripts/*.test.js
firealerts-node/scripts/comprehensive-security-test-suite.js
firealerts-node/scripts/generate-passwords.js
firealerts-node/scripts/drop-legacy-tables.js

# Development-specific files
start.bat
firealerts-node/cleanup-test-data.js
firealerts-node/data-migration.js
firealerts-node/test-*.js
firealerts-node/docker-entrypoint.sh

# Documentation archives (keep main docs)
docs/archive/

# Deployment preparation files (generated)
.env.render.template
render.yaml
RENDER-DEPLOYMENT-CHECKLIST.md

# Docker scripts and entrypoints
start.sh
stop.sh
rebuild.sh
reset-db.sh
start.bat
setup.sh
wait-for-db.sh
docker-entrypoint.sh
**/docker-entrypoint.sh
*.dockerfile
**/*.dockerfile

# Docker configuration files
nginx.conf
**/nginx.conf
my.cnf
**/my.cnf

# Development and testing files (comprehensive patterns)
test/
tests/
testing/
__tests__/
spec/
specs/
*test*.js
*test*.html
test-*.js
test-*.html
*-test.js
*-test.html
*.test.js
*.test.html
*.spec.js
*.spec.html
test-*.md
*-test.md
*.test.md

# Keep legitimate documentation (not test scripts)
!docs/*-TEST-REPORT.md
!docs/COMPREHENSIVE-SECURITY-TEST-REPORT.md
!docs/GITIGNORE-TEST-SCRIPTS-SUMMARY.md

# Temporary test files and scripts
temp-test-*
tmp-test-*
debug-test-*
validate-test-*

# Development scripts (keep only production-necessary scripts)
firealerts-node/test-*.js
firealerts-node/cleanup-test-data.js
firealerts-node/data-migration.js

# Legacy model files (unused legacy tables)
firealerts-node/models/location.js
firealerts-node/models/logger.js
firealerts-node/models/os.js
firealerts-node/models/systemSetting.js

# Legacy scripts and migration files
firealerts-node/scripts/drop-legacy-tables.js
firealerts-node/scripts/migrate-subscribers-to-users.js
firealerts-node/scripts/fix-*.js
firealerts-node/scripts/inspect-live-database.js
firealerts-node/scripts/run-consolidation.js
firealerts-node/scripts/run-migration.js
firealerts-node/scripts/test-*.js

# Development documentation
docs/archive/
docs/DOCUMENTATION-CONSOLIDATION-SUMMARY.md

# Local development
.cache/
.local/

# SQL files (exclude Docker schema files but allow migrations)
docker/mysql-init/*.sql
*.sql
!firealerts-node/migrations/*.sql