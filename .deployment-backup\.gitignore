# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment variables
.env
.env.*

# Build output
dist/
build/
out/

# Log files
*.log
logs/

# Operating System Files
.DS_Store
Thumbs.db

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo

# Docker files (excluded from production)
Dockerfile
docker-compose.yml
.dockerignore
docker/
!docker/.env.example
!docker/mysql-init/01-schema.sql

# Development and testing files
tests/
test-*.js
test-*.html
*test*.js
*test*.html

# Development scripts (keep essential production scripts)
firealerts-node/scripts/fix-*.js
firealerts-node/scripts/inspect-*.js
firealerts-node/scripts/migrate-*.js
firealerts-node/scripts/reseed-*.js
firealerts-node/scripts/run-*.js
firealerts-node/scripts/test-*.js
firealerts-node/scripts/generate-passwords.js
firealerts-node/scripts/drop-legacy-tables.js

# Development-specific files
start.bat
firealerts-node/cleanup-test-data.js
firealerts-node/data-migration.js
firealerts-node/test-*.js
firealerts-node/docker-entrypoint.sh

# Documentation archives (keep main docs)
docs/archive/

# Deployment preparation files (generated)
.env.render.template
render.yaml
RENDER-DEPLOYMENT-CHECKLIST.md

# Local development
.cache/
.local/
