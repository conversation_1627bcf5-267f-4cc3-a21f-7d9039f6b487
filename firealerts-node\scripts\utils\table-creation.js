/**
 * Table Creation Utility for Database Seeding Scripts
 * 
 * This utility provides functions to automatically create database tables
 * before seeding data, ensuring that seeding scripts work on fresh databases
 * without requiring manual schema setup.
 * 
 * Features:
 * - Automatic table creation using Sequelize sync
 * - Table existence checking
 * - Error handling and logging
 * - Support for both development and production environments
 * - Rollback capability on errors
 */

const fs = require('fs');
const path = require('path');

/**
 * Check if we're in a Docker environment
 */
function isInDocker() {
  try {
    return fs.existsSync('/.dockerenv');
  } catch (err) {
    return false;
  }
}

/**
 * Check if a table exists in the database
 * @param {Object} sequelize - Sequelize instance
 * @param {string} tableName - Name of the table to check
 * @returns {Promise<boolean>} - True if table exists, false otherwise
 */
async function tableExists(sequelize, tableName) {
  try {
    const queryInterface = sequelize.getQueryInterface();
    const tables = await queryInterface.showAllTables();
    return tables.includes(tableName);
  } catch (error) {
    console.log(`⚠️ Could not check if table '${tableName}' exists:`, error.message);
    return false;
  }
}

/**
 * Check if multiple tables exist in the database
 * @param {Object} sequelize - Sequelize instance
 * @param {Array<string>} tableNames - Array of table names to check
 * @returns {Promise<Object>} - Object with table names as keys and existence status as values
 */
async function tablesExist(sequelize, tableNames) {
  const results = {};
  
  try {
    const queryInterface = sequelize.getQueryInterface();
    const existingTables = await queryInterface.showAllTables();
    
    for (const tableName of tableNames) {
      results[tableName] = existingTables.includes(tableName);
    }
  } catch (error) {
    console.log(`⚠️ Could not check table existence:`, error.message);
    // Default to false for all tables if check fails
    for (const tableName of tableNames) {
      results[tableName] = false;
    }
  }
  
  return results;
}

/**
 * Create tables using Sequelize models
 * @param {Object} sequelize - Sequelize instance
 * @param {Object} models - Object containing Sequelize models
 * @param {Object} options - Creation options
 * @returns {Promise<Object>} - Results of table creation
 */
async function createTables(sequelize, models, options = {}) {
  const {
    force = false,
    alter = false,
    verbose = false,
    transaction = null
  } = options;

  const results = {
    created: [],
    existed: [],
    errors: []
  };

  try {
    console.log('🔍 Checking database table structure...');
    
    // Get list of required tables from models
    const requiredTables = Object.keys(models).map(modelName => {
      const model = models[modelName];
      return model.getTableName ? model.getTableName() : model.tableName || modelName.toLowerCase() + 's';
    });

    if (verbose) {
      console.log(`📋 Required tables: ${requiredTables.join(', ')}`);
    }

    // Check which tables exist
    const tableExistence = await tablesExist(sequelize, requiredTables);
    const missingTables = requiredTables.filter(table => !tableExistence[table]);
    const existingTables = requiredTables.filter(table => tableExistence[table]);

    if (verbose) {
      console.log(`✅ Existing tables: ${existingTables.length > 0 ? existingTables.join(', ') : 'none'}`);
      console.log(`❌ Missing tables: ${missingTables.length > 0 ? missingTables.join(', ') : 'none'}`);
    }

    // If all tables exist and not forcing recreation, return early
    if (missingTables.length === 0 && !force && !alter) {
      console.log('✅ All required tables already exist');
      results.existed = existingTables;
      return results;
    }

    // Create missing tables or sync all if force/alter is true
    if (missingTables.length > 0 || force || alter) {
      console.log('🔨 Creating/updating database tables...');
      
      const syncOptions = {
        force,
        alter,
        transaction
      };

      if (verbose) {
        console.log(`📋 Sync options: force=${force}, alter=${alter}`);
      }

      // Sync all models to create/update tables
      await sequelize.sync(syncOptions);
      
      if (force) {
        console.log('✅ All tables recreated (force mode)');
        results.created = requiredTables;
      } else if (alter) {
        console.log('✅ All tables updated (alter mode)');
        results.created = requiredTables;
      } else {
        console.log(`✅ Created ${missingTables.length} missing tables`);
        results.created = missingTables;
        results.existed = existingTables;
      }
    }

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    results.errors.push({
      operation: 'table_creation',
      error: error.message
    });
    throw error;
  }

  return results;
}

/**
 * Ensure tables exist for seeding operations
 * This is the main function that seeding scripts should call
 * @param {Object} sequelize - Sequelize instance
 * @param {Object} models - Object containing Sequelize models
 * @param {Object} options - Options for table creation
 * @returns {Promise<Object>} - Results of table creation/verification
 */
async function ensureTablesExist(sequelize, models, options = {}) {
  const {
    verbose = false,
    force = false,
    alter = false,
    transaction = null
  } = options;

  try {
    if (verbose) {
      console.log('🔧 Ensuring database tables exist...');
      console.log(`🔌 Environment: ${isInDocker() ? 'Docker' : 'Local'}`);
    }

    // Test database connection first
    await sequelize.authenticate();
    if (verbose) {
      console.log('✅ Database connection verified');
    }

    // Create tables if needed
    const results = await createTables(sequelize, models, {
      force,
      alter,
      verbose,
      transaction
    });

    if (verbose) {
      console.log('✅ Table structure verification completed');
    }

    return results;

  } catch (error) {
    console.error('❌ Failed to ensure tables exist:', error.message);
    throw error;
  }
}

/**
 * Load models from the models directory
 * @param {Object} sequelize - Sequelize instance
 * @param {string} modelsPath - Path to models directory
 * @returns {Object} - Object containing loaded models
 */
function loadModels(sequelize, modelsPath = null) {
  try {
    // Default to the standard models path if not provided
    if (!modelsPath) {
      modelsPath = path.join(__dirname, '../../models');
    }

    // Use the existing models index if available
    const modelsIndexPath = path.join(modelsPath, 'index.js');
    if (fs.existsSync(modelsIndexPath)) {
      const db = require(modelsIndexPath);
      return db;
    }

    // Fallback: manually load models
    const models = {};
    const files = fs.readdirSync(modelsPath);
    
    files.forEach(file => {
      if (file.indexOf('.') !== 0 && file !== 'index.js' && file.slice(-3) === '.js') {
        const modelPath = path.join(modelsPath, file);
        const modelModule = require(modelPath);
        if (typeof modelModule === 'function') {
          const model = modelModule(sequelize, sequelize.Sequelize.DataTypes);
          models[model.name] = model;
        }
      }
    });

    return models;
  } catch (error) {
    console.error('❌ Error loading models:', error.message);
    throw error;
  }
}

module.exports = {
  isInDocker,
  tableExists,
  tablesExist,
  createTables,
  ensureTablesExist,
  loadModels
};
