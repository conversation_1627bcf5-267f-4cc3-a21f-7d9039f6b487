module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define('company', {
    name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    address: {
      type: DataTypes.STRING(255)
    },
    city: {
      type: DataTypes.STRING(100)
    },
    state: {
      type: DataTypes.STRING(2)
    },
    zip: {
      type: DataTypes.STRING(10)
    },
    phone: {
      type: DataTypes.STRING(20)
    },
    email: {
      type: DataTypes.STRING(255)
    },
    contactPerson: {
      type: DataTypes.STRING(100),
      field: 'contact_person'
    },
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    subscriptionType: {
      type: DataTypes.STRING(50),
      field: 'subscription_type'
    },
    subscriptionExpiry: {
      type: DataTypes.DATE,
      field: 'subscription_expiry'
    },
    companyTypeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'company_type_id',
      references: {
        model: 'company_types',
        key: 'id'
      },
      onDelete: 'SET NULL'
    }
  }, {
    tableName: 'companies',
    underscored: true
  });

  return Company;
};
