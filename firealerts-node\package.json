{"name": "firealerts911", "version": "1.0.0", "description": "Fire Alerts 911 Dispatch System", "main": "server.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "wait-for-db": "node scripts/wait-for-db.js", "fix-columns": "node scripts/fix-db-columns.js", "init-db": "node db-init.js", "seed": "node scripts/seed-all.js", "seed-all": "node scripts/seed-all.js", "seed-production": "node scripts/seed-production.js", "seed-core": "node scripts/seed-data.js", "seed-locations": "node scripts/seed-complete-locations.js", "seed-complete-locations": "node scripts/seed-complete-locations.js", "seed-all-states": "node scripts/seed-complete-locations.js --states-only", "seed-all-counties": "node scripts/seed-complete-locations.js --counties-only", "reseed-locations": "node scripts/seed-complete-locations.js --clear-first", "reseed-counties-proper-names": "node scripts/reseed-counties-with-proper-names.js", "seed-company-types": "node scripts/seed-company-types.js", "seed-subscription-plans": "node scripts/seed-subscription-plans.js", "validate-schema": "node scripts/validate-schema-consistency.js", "validate-production-seeding": "node scripts/validate-production-seeding.js", "migrate-subscribers": "node scripts/migrate-subscribers-to-users.js", "consolidate": "node scripts/run-consolidation.js", "validate-security": "node scripts/validate-production-security.js", "generate-jwt-secret": "node ../scripts/generate-jwt-secret.js", "prepare-render": "node ../scripts/prepare-render-deployment.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-session": "^1.18.1", "express-validator": "^6.12.1", "form-data": "^4.0.2", "jsonwebtoken": "^8.5.1", "mailgun.js": "^12.0.1", "mysql2": "^2.3.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "sequelize": "^6.6.5", "twilio": "^4.19.0", "yargs": "^17.7.2"}, "devDependencies": {"nodemon": "^2.0.12"}}