const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const adminAccess = require('../middleware/adminAccess');
const db = require('../models');

// GET /api/logs - Get recent system logs (activities) with pagination
router.get('/', [auth, adminAccess], async (req, res) => {
  try {
    const { limit = 20, offset = 0 } = req.query;

    // Get total count and paginated logs
    const { count, rows: logs } = await db.activity.findAndCountAll({
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        { model: db.user, attributes: ['id', 'username', 'email'] }
      ]
    });

    // Calculate pagination info
    const totalPages = Math.ceil(count / parseInt(limit));
    const currentPage = Math.floor(parseInt(offset) / parseInt(limit)) + 1;

    res.json({
      data: logs,
      pagination: {
        total: count,
        currentPage: currentPage,
        totalPages: totalPages,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (err) {
    console.error('Error fetching system logs:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

module.exports = router;
