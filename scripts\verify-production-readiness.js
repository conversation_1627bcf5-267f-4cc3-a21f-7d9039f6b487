#!/usr/bin/env node

/**
 * Production Readiness Verification Script
 *
 * This script verifies that the codebase is properly prepared for production deployment
 * by checking that development files are excluded and essential files are present.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 FireAlerts911 Production Readiness Verification');
console.log('=================================================\n');

let allChecksPass = true;

/**
 * Check if files/directories are properly excluded
 */
function checkExcludedFiles() {
  console.log('📋 Checking excluded development files...');

  const shouldBeExcluded = [
    'Dockerfile',
    'docker-compose.yml',
    '.dockerignore',
    'docker/',
    'tests/',
    'start.bat',
    'firealerts-node/cleanup-test-data.js',
    'firealerts-node/data-migration.js',
    'firealerts-node/docker-entrypoint.sh',
    'docs/archive/'
  ];

  let excludedCorrectly = true;

  shouldBeExcluded.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`⚠️  ${file} - EXISTS (should be excluded by .gitignore)`);
    } else {
      console.log(`✅ ${file} - Not present or properly excluded`);
    }
  });

  return excludedCorrectly;
}

/**
 * Check if essential production files are present
 */
function checkEssentialFiles() {
  console.log('\n📋 Checking essential production files...');

  const essentialFiles = [
    'firealerts-node/package.json',
    'firealerts-node/server.js',
    'firealerts-node/scripts/seed-production.js',
    'index.html',
    'js/api.js',
    '.gitignore',
    'docs/PRODUCTION-DEPLOYMENT-GUIDE.md'
  ];

  let allPresent = true;

  essentialFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - MISSING`);
      allPresent = false;
    }
  });

  return allPresent;
}

/**
 * Check legacy model files have been removed
 */
function checkLegacyModels() {
  console.log('\n📋 Checking legacy model files have been removed...');

  const legacyModels = [
    'firealerts-node/models/location.js',
    'firealerts-node/models/logger.js',
    'firealerts-node/models/os.js'
  ];

  let allRemoved = true;

  legacyModels.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`❌ ${file} - STILL EXISTS (should be removed)`);
      allRemoved = false;
    } else {
      console.log(`✅ ${file} - Successfully removed`);
    }
  });

  return allRemoved;
}

/**
 * Check package.json configuration
 */
function checkPackageJson() {
  console.log('\n📋 Checking package.json configuration...');

  try {
    const packagePath = path.join('firealerts-node', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

    // Check Node.js version
    if (packageJson.engines && packageJson.engines.node) {
      console.log(`✅ Node.js version specified: ${packageJson.engines.node}`);
    } else {
      console.log('❌ Node.js version not specified');
      return false;
    }

    // Check essential scripts
    const requiredScripts = ['start', 'seed-production'];
    let scriptsValid = true;

    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        console.log(`✅ Script "${script}": ${packageJson.scripts[script]}`);
      } else {
        console.log(`❌ Missing script: ${script}`);
        scriptsValid = false;
      }
    });

    return scriptsValid;
  } catch (error) {
    console.log('❌ Error reading package.json:', error.message);
    return false;
  }
}

/**
 * Check .gitignore configuration
 */
function checkGitignore() {
  console.log('\n📋 Checking .gitignore configuration...');

  try {
    const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');

    const requiredExclusions = [
      'Dockerfile',
      'docker-compose.yml',
      'docker/',
      'tests/',
      'start.bat',
      'firealerts-node/cleanup-test-data.js'
    ];

    let allExcluded = true;

    requiredExclusions.forEach(exclusion => {
      if (gitignoreContent.includes(exclusion)) {
        console.log(`✅ ${exclusion} - Excluded in .gitignore`);
      } else {
        console.log(`❌ ${exclusion} - NOT excluded in .gitignore`);
        allExcluded = false;
      }
    });

    return allExcluded;
  } catch (error) {
    console.log('❌ Error reading .gitignore:', error.message);
    return false;
  }
}

/**
 * Main verification
 */
function main() {
  allChecksPass &= checkEssentialFiles();
  allChecksPass &= checkExcludedFiles();
  allChecksPass &= checkLegacyModels();
  allChecksPass &= checkPackageJson();
  allChecksPass &= checkGitignore();

  console.log('\n' + '='.repeat(50));

  if (allChecksPass) {
    console.log('🎉 PRODUCTION READINESS VERIFIED!');
    console.log('✅ All checks passed');
    console.log('\n📋 Ready for deployment:');
    console.log('- Docker files excluded from production');
    console.log('- Development and test files excluded');
    console.log('- Legacy model files excluded');
    console.log('- Essential production files present');
    console.log('- Package.json properly configured');
    console.log('- .gitignore properly configured');
    console.log('\n🚀 Next steps:');
    console.log('1. Commit changes to repository');
    console.log('2. Push to production branch');
    console.log('3. Deploy to Render.com');
    console.log('4. Run production seeding');
    console.log('\n📖 See docs/PRODUCTION-DEPLOYMENT-GUIDE.md for detailed instructions');
  } else {
    console.log('❌ PRODUCTION READINESS FAILED');
    console.log('⚠️  Please fix the issues above before deploying');
  }
}

// Run the verification
main();
