/**
 * Permissions and Roles configuration
 * This file defines the role hierarchy and permission structure for the application
 */

// Define all roles in the system
const ROLES = {
  ADMIN: 'admin',
  COMPANY_ADMIN: 'company_admin',
  DISPATCHER: 'dispatcher',
  SUBSCRIBER: 'subscriber'
};

// Define role hierarchy (higher number = higher privileges)
const ROLE_HIERARCHY = {
  [ROLES.ADMIN]: 4,
  [ROLES.COMPANY_ADMIN]: 3,
  [ROLES.DISPATCHER]: 2,
  [ROLES.SUBSCRIBER]: 1
};

// Define permissions and which roles can perform them
const PERMISSIONS = {
  // Incident permissions
  CREATE_INCIDENT: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],
  UPDATE_INCIDENT: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],
  DELETE_INCIDENT: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  VIEW_ALL_INCIDENTS: [ROLES.ADMIN, ROL<PERSON>.DISPATCHER, ROLES.COMPANY_ADMIN],
  VIEW_COUNTY_INCIDENTS: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.SUBSCRIBER, ROLES.COMPANY_ADMIN],

  // User management permissions
  CREATE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  UPDATE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  DELETE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  VIEW_ALL_USERS: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],

  // Company management permissions (consolidated from subscriber permissions)
  CREATE_COMPANY: [ROLES.ADMIN],
  UPDATE_COMPANY: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  DELETE_COMPANY: [ROLES.ADMIN],
  VIEW_ALL_COMPANIES: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  VIEW_COMPANY_STATS: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],

  // User management permissions (enhanced)
  CREATE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  UPDATE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  DELETE_USER: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  VIEW_ALL_USERS: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],
  VIEW_USER_STATS: [ROLES.ADMIN, ROLES.COMPANY_ADMIN],

  // Notification permissions
  SEND_NOTIFICATIONS: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],
  VIEW_NOTIFICATIONS: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],

  // Settings permissions
  MANAGE_SETTINGS: [ROLES.ADMIN],

  // Statistics and dashboard permissions
  VIEW_DASHBOARD: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],
  VIEW_INCIDENT_STATS: [ROLES.ADMIN, ROLES.DISPATCHER, ROLES.COMPANY_ADMIN],
  VIEW_SYSTEM_STATUS: [ROLES.ADMIN]
};

/**
 * Check if a user role has a specific permission
 * @param {string} userRole - The role of the user
 * @param {string} permission - The permission to check
 * @returns {boolean} - Whether the user has the permission
 */
function hasPermission(userRole, permission) {
  if (!PERMISSIONS[permission]) return false;
  return PERMISSIONS[permission].includes(userRole);
}

/**
 * Check if a user role has minimum required role level
 * @param {string} userRole - The role of the user
 * @param {string} requiredRole - The minimum role required
 * @returns {boolean} - Whether the user meets the minimum role requirement
 */
function hasMinimumRole(userRole, requiredRole) {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
}

/**
 * Get all permissions for a specific role
 * @param {string} role - The role to check
 * @returns {Array} - Array of permission keys this role has access to
 */
function getPermissionsForRole(role) {
  return Object.keys(PERMISSIONS).filter(permission =>
    PERMISSIONS[permission].includes(role)
  );
}

module.exports = {
  ROLES,
  ROLE_HIERARCHY,
  PERMISSIONS,
  hasPermission,
  hasMinimumRole,
  getPermissionsForRole
};