<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Manage Incidents</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <link rel="stylesheet" href="css/incident-focused.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <style>

        /* Incident table row styling for consistent height */
        .data-table tr {
            height: 62px; /* Fixed height for all table rows */
            line-height: 1.5;
            transition: background-color 0.2s;
        }

        .data-table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Maximum width for cells to prevent extreme stretching */
            vertical-align: middle; /* Center content vertically */
            padding: 12px 15px; /* Consistent padding */
        }

        /* For the title column that might have long text */
        .data-table td:nth-child(3) {
            max-width: 300px; /* Allow more space for titles */
        }

        /* For the location column that might have long text */
        .data-table td:nth-child(4) {
            max-width: 300px; /* Allow more space for addresses */
        }

        /* Empty row styling for consistent table height */
        .data-table tr.empty-row {
            height: 62px; /* Match the height of rows with content */
            background-color: transparent;
        }

        .data-table tr.empty-row td {
            border-top: 1px solid rgba(230, 230, 230, 0.3);
            border-bottom: none;
        }

        /* Sort icon styling */
        .sort-icon {
            margin-left: 5px;
            cursor: pointer;
            display: inline-block;
            transition: color 0.2s;
        }

        .sort-icon:hover {
            color: var(--accent-primary);
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            animation: slideIn 0.3s ease-out forwards;
        }

        .notification.success {
            background-color: #4caf50;
        }

        .notification.error {
            background-color: #f44336;
        }

        .notification.info {
            background-color: #2196f3;
        }

        .notification i {
            margin-right: 10px;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Fix table background to prevent white flicker */
        .incidents-container {
            background-color: var(--secondary-dark) !important;
        }

        .incidents-container .card-content {
            background-color: var(--secondary-dark) !important;
        }

        .table-wrapper {
            background-color: var(--secondary-dark) !important;
        }

        .data-table {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody tr {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody td {
            background-color: transparent !important;
        }

        /* Preserve hover effects */
        .data-table tbody tr:hover {
            background-color: var(--hover-bg) !important;
        }

        /* Ensure skeleton rows don't interfere with hover */
        .data-table tbody tr.skeleton-row:hover {
            background-color: var(--secondary-dark) !important;
        }

        /* Skeleton loading styles */
        .skeleton-row {
            background-color: var(--secondary-dark) !important; /* Match table background */
            animation: skeleton-pulse 1.5s ease-in-out infinite;
        }

        .skeleton-text {
            height: 16px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.05) 25%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 75%
            );
            background-size: 200% 100%;
            animation: skeleton-shimmer 1.5s infinite;
            border-radius: 4px;
            width: 60%;
        }

        .skeleton-text-long {
            width: 80%;
        }

        @keyframes skeleton-shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        @keyframes skeleton-pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Replaced placeholder image with Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- All Incidents -->
            <div class="card incidents-container" style="background-color: #272e48 !important;">
                <div class="card-header">
                    <div class="card-title">Manage Incidents</div>
                    <div class="card-actions">
                        <a href="add-incident.html" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Incident
                        </a>
                    </div>
                </div>
                <div class="card-content" style="background-color: #272e48 !important;">
                    <!-- Filter Controls -->
                    <form id="filter-form" style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
                        <div style="flex: 1; min-width: 200px;">
                            <input type="text" class="form-control" id="search-input" placeholder="Search incidents..." style="width: 100%;">
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <select class="form-control" id="type-filter">
                                <option value="">All Types</option>
                                <option value="fire">Fire Incidents</option>
                                <option value="water">Water Incidents</option>
                            </select>
                            <select class="form-control" id="status-filter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="critical">Critical</option>
                                <option value="resolved">Resolved</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </form>

                    <!-- Incidents Table - Desktop/Tablet View -->
                    <div class="table-wrapper table-responsive" style="background-color: #272e48 !important;">
                        <table class="data-table" style="background-color: #272e48 !important;">
                            <thead>
                                <tr>
                                    <th data-sort="id" style="width: 80px">#ID <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="type" style="width: 100px">Type <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="title">Title <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="location">Location <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="date">Date <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="status" style="width: 120px">Status <i class="fas fa-sort sort-icon"></i></th>
                                    <th style="width: 150px">Actions</th>
                                </tr>
                            </thead>
                            <tbody style="background-color: #272e48 !important;">
                                <!-- Initial loading state with skeleton rows -->
                                <tr class="skeleton-row" style="background-color: #272e48 !important;">
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                </tr>
                                <tr class="skeleton-row" style="background-color: #272e48 !important;">
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                </tr>
                                <tr class="skeleton-row" style="background-color: #272e48 !important;">
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                </tr>
                                <tr class="skeleton-row" style="background-color: #272e48 !important;">
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                </tr>
                                <tr class="skeleton-row" style="background-color: #272e48 !important;">
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text skeleton-text-long"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                    <td style="background-color: transparent !important;"><div class="skeleton-text"></div></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="table-mobile-cards" id="incidents-mobile-cards">
                        <!-- Mobile cards will be populated by JavaScript -->
                    </div>

                    <!-- Pagination -->
                    <div id="incidents-pagination" class="pagination-container" style="margin-top: 15px; display: flex; justify-content: center;">
                        <!-- Pagination will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Variables for pagination and sorting
        let currentPage = 1;
        let incidentsPerPage = 10;
        let allIncidents = [];
        let currentSortColumn = 'id';
        let currentSortOrder = 'ASC';
        let totalPages = 1;
        let filterTimeout = null; // For debouncing filter changes

        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('incidents');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Initialize the page
            initializeIncidentsPage();

            // Add window resize listener for responsive table/card switching
            window.addEventListener('resize', function() {
                // Debounce resize events
                clearTimeout(window.resizeTimeout);
                window.resizeTimeout = setTimeout(function() {
                    // Force re-render of table/cards based on current window size
                    if (allIncidents && allIncidents.length > 0) {
                        updateIncidentsTable(allIncidents);
                    }
                }, 150);
            });
        });

        // Update sort indicators in the table headers
        function updateSortIndicators() {
            const headers = document.querySelectorAll('.data-table th[data-sort]');

            // Remove all existing sort indicators
            headers.forEach(header => {
                const iconElement = header.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = 'fas fa-sort sort-icon';
                }
            });

            // Add the appropriate sort indicator to the current sort column
            const activeHeader = document.querySelector(`.data-table th[data-sort="${currentSortColumn}"]`);
            if (activeHeader) {
                const iconElement = activeHeader.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = currentSortOrder === 'ASC' ? 'fas fa-sort-up sort-icon' : 'fas fa-sort-down sort-icon';
                }
            }
        }

        // Loading state management functions
        function showLoadingState() {
            const tableWrapper = document.querySelector('.table-wrapper');

            // Create loading overlay if it doesn't exist
            let loadingOverlay = document.getElementById('loading-overlay');
            if (!loadingOverlay) {
                loadingOverlay = document.createElement('div');
                loadingOverlay.id = 'loading-overlay';
                loadingOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(39, 46, 72, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10;
                    backdrop-filter: blur(1px);
                    transition: opacity 0.2s ease;
                `;
                loadingOverlay.innerHTML = `
                    <div style="text-align: center; color: #f5f5f5;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px; color: #1e88e5;"></i>
                        <div>Loading incidents...</div>
                    </div>
                `;

                // Make table wrapper relative for overlay positioning
                tableWrapper.style.position = 'relative';
                tableWrapper.appendChild(loadingOverlay);
            } else {
                loadingOverlay.style.display = 'flex';
                loadingOverlay.style.opacity = '1';
            }
        }

        function addSubtleLoadingIndicator() {
            const cardHeader = document.querySelector('.card-header .card-title');
            if (cardHeader && !cardHeader.querySelector('.loading-spinner')) {
                const spinner = document.createElement('i');
                spinner.className = 'fas fa-spinner fa-spin loading-spinner';
                spinner.style.cssText = 'margin-left: 10px; color: #666; font-size: 14px;';
                cardHeader.appendChild(spinner);
            }
        }

        function removeLoadingIndicators() {
            // Remove overlay
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    if (loadingOverlay.parentNode) {
                        loadingOverlay.style.display = 'none';
                    }
                }, 200);
            }

            // Remove subtle spinner
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.remove();
            }
        }

        // Debounced filter function to prevent rapid API calls
        function debouncedLoadIncidents(showLoadingOverlay = false, delay = 150) {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(() => {
                loadIncidents(showLoadingOverlay);
            }, delay);
        }

        function loadIncidents(showLoadingOverlay = true) {
            const searchQuery = document.getElementById('search-input').value.trim();
            const typeFilter = document.getElementById('type-filter').value;
            const statusFilter = document.getElementById('status-filter').value;

            // Show loading indicator with smooth transition
            const tableBody = document.querySelector('.data-table tbody');

            if (showLoadingOverlay) {
                // Add loading overlay instead of replacing content
                showLoadingState();
            } else {
                // For quick updates, just add a subtle loading indicator
                addSubtleLoadingIndicator();
            }

            // Prepare filter parameters
            const params = {
                page: currentPage,
                limit: incidentsPerPage,
                sortBy: currentSortColumn,
                sortOrder: currentSortOrder
            };

            if (searchQuery) params.search = searchQuery;
            if (typeFilter) params.type = typeFilter;
            if (statusFilter) params.status = statusFilter;

            API.incidents.getAll(params)
                .then(response => {
                    console.log("API Response:", response);

                    // Remove loading indicators
                    removeLoadingIndicators();

                    if (!response || response.success === false) {
                        showNotification('Failed to load incidents', 'error');
                        const tableBody = document.querySelector('.data-table tbody');

                        // Remove skeleton rows
                        const skeletonRows = tableBody.querySelectorAll('.skeleton-row');
                        skeletonRows.forEach(row => row.remove());

                        tableBody.innerHTML = '<tr style="background-color: #272e48 !important;"><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red); background-color: transparent !important;">Error loading incidents. Please try again.</td></tr>';
                        return;
                    }

                    // Handle different response formats
                    let incidents;
                    let totalCount = 0;

                    if (Array.isArray(response)) {
                        // Direct array response
                        incidents = response;
                        totalCount = response.length;
                        allIncidents = response;
                    } else if (response.incidents && Array.isArray(response.incidents)) {
                        // Response format: { incidents: [...], totalCount, totalPages, currentPage }
                        incidents = response.incidents;
                        totalCount = response.totalCount || incidents.length;
                        totalPages = response.totalPages || Math.ceil(totalCount / incidentsPerPage);
                        allIncidents = incidents;
                    } else if (response.data && Array.isArray(response.data)) {
                        // Response format: { data: [...], success, message }
                        incidents = response.data;
                        totalCount = incidents.length;
                        allIncidents = incidents;
                    } else {
                        showNotification('Unexpected response format', 'error');
                        console.error('Unexpected response format:', response);
                        tableBody.innerHTML = '<tr style="background-color: #272e48 !important;"><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red); background-color: transparent !important;">Invalid data format received.</td></tr>';
                        return;
                    }

                    // Calculate total pages if not provided by API
                    if (!totalPages) {
                        totalPages = Math.ceil(totalCount / incidentsPerPage);
                    }

                    // Sort incidents locally to ensure proper sorting regardless of API behavior
                    sortIncidentsLocally();

                    // Update sort indicators to reflect current sort
                    updateSortIndicators();

                    // Display incidents
                    updateIncidentsTable(allIncidents);

                    // Create pagination
                    createPaginationControls(totalPages);
                })
                .catch(error => {
                    console.error('Error loading incidents:', error);

                    // Remove loading indicators
                    removeLoadingIndicators();

                    showNotification('Error connecting to server', 'error');
                    const tableBody = document.querySelector('.data-table tbody');

                    // Remove skeleton rows
                    const skeletonRows = tableBody.querySelectorAll('.skeleton-row');
                    skeletonRows.forEach(row => row.remove());

                    tableBody.innerHTML = '<tr style="background-color: #272e48 !important;"><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red); background-color: transparent !important;">Could not connect to server. Please try again later.</td></tr>';
                });
        }

        // Function to sort incidents locally
        function sortIncidentsLocally() {
            // Skip sorting if we don't have incidents loaded
            if (!allIncidents || allIncidents.length === 0) return;

            // Sort the incidents array
            allIncidents.sort((a, b) => {
                let valA, valB;

                // Extract the values to compare based on the column
                switch(currentSortColumn) {
                    case 'id':
                        valA = parseInt(a.id) || 0;
                        valB = parseInt(b.id) || 0;
                        break;
                    case 'type':
                        valA = a.incidentType?.name?.toLowerCase() || '';
                        valB = b.incidentType?.name?.toLowerCase() || '';
                        break;
                    case 'title':
                        valA = a.title?.toLowerCase() || '';
                        valB = b.title?.toLowerCase() || '';
                        break;
                    case 'location':
                        valA = `${a.address || ''} ${a.city || ''}`.toLowerCase();
                        valB = `${b.address || ''} ${b.city || ''}`.toLowerCase();
                        break;
                    case 'date':
                        valA = new Date(a.incidentDate || a.createdAt || 0).getTime();
                        valB = new Date(b.incidentDate || b.createdAt || 0).getTime();
                        break;
                    case 'status':
                        valA = a.status?.name?.toLowerCase() || '';
                        valB = b.status?.name?.toLowerCase() || '';
                        break;
                    default:
                        valA = 0;
                        valB = 0;
                }

                // Perform the comparison based on the sort order
                if (currentSortOrder === 'ASC') {
                    return valA > valB ? 1 : (valA < valB ? -1 : 0);
                } else {
                    return valA < valB ? 1 : (valA > valB ? -1 : 0);
                }
            });

            console.log(`Sorted incidents by ${currentSortColumn} in ${currentSortOrder} order`, allIncidents);
        }

        // Function to create pagination controls
        function createPaginationControls(totalPages) {
            const paginationContainer = document.getElementById('incidents-pagination');

            if (!paginationContainer) return;

            // Clear previous pagination
            paginationContainer.innerHTML = '';

            // Don't show pagination if only one page
            if (totalPages <= 1) return;

            // Create pagination list
            const paginationList = document.createElement('ul');
            paginationList.className = 'pagination';

            // Previous button
            const prevLi = document.createElement('li');
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    loadIncidents();
                }
            });
            prevLi.appendChild(prevLink);
            if (currentPage === 1) prevLi.className = 'disabled';
            paginationList.appendChild(prevLi);

            // Page numbers
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust if we're near the end
            if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page button if not visible
            if (startPage > 1) {
                const firstLi = document.createElement('li');
                const firstLink = document.createElement('a');
                firstLink.href = '#';
                firstLink.textContent = '1';
                firstLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = 1;
                    loadIncidents();
                });
                firstLi.appendChild(firstLink);
                paginationList.appendChild(firstLi);

                // Ellipsis if needed
                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }
            }

            // Page buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                if (i === currentPage) pageLi.className = 'active';

                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = i;
                    loadIncidents();
                });
                pageLi.appendChild(pageLink);
                paginationList.appendChild(pageLi);
            }

            // Last page button if not visible
            if (endPage < totalPages) {
                // Ellipsis if needed
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                const lastLink = document.createElement('a');
                lastLink.href = '#';
                lastLink.textContent = totalPages;
                lastLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = totalPages;
                    loadIncidents();
                });
                lastLi.appendChild(lastLink);
                paginationList.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    loadIncidents();
                }
            });
            nextLi.appendChild(nextLink);
            if (currentPage === totalPages) nextLi.className = 'disabled';
            paginationList.appendChild(nextLi);

            paginationContainer.appendChild(paginationList);
        }

        function updateIncidentsTable(incidents) {
            const tableBody = document.querySelector('.data-table tbody');
            const mobileCards = document.getElementById('incidents-mobile-cards');

            // Remove skeleton rows if they exist
            const skeletonRows = tableBody.querySelectorAll('.skeleton-row');
            skeletonRows.forEach(row => row.remove());

            if (!incidents || incidents.length === 0) {
                // Handle empty state for table
                tableBody.innerHTML = '<tr style="background-color: #272e48 !important;"><td colspan="7" style="text-align: center; padding: 20px; background-color: transparent !important;">No incidents found. Try adjusting your filters.</td></tr>';

                // Handle empty state for mobile cards
                if (mobileCards) {
                    mobileCards.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>No incidents found. Try adjusting your filters.</p>
                        </div>
                    `;
                }

                // Add empty rows to maintain consistent height for a full page
                for (let i = 0; i < incidentsPerPage - 1; i++) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.className = 'empty-row';
                    emptyRow.style.backgroundColor = '#272e48';
                    emptyRow.innerHTML = '<td colspan="7" style="background-color: transparent !important;">&nbsp;</td>';
                    tableBody.appendChild(emptyRow);
                }
                return;
            }

            // Clear previous content (but skeleton rows are already removed above)
            const existingRows = tableBody.querySelectorAll('tr:not(.skeleton-row)');
            existingRows.forEach(row => row.remove());

            // Clear mobile cards
            if (mobileCards) {
                mobileCards.innerHTML = '';
            }

            // Display incidents
            incidents.forEach(incident => {
                // Determine incident type icon and class
                let typeIcon = '<i class="fas fa-question-circle"></i>';
                let typeClass = 'default';
                let typeName = 'Unknown';

                // Check if incident has an incidentType property
                if (incident.incidentType) {
                    if (incident.incidentType.category === 'fire') {
                        typeIcon = '<i class="fas fa-fire"></i>';
                        typeClass = 'fire';
                        typeName = 'Fire';
                    } else if (incident.incidentType.category === 'water') {
                        typeIcon = '<i class="fas fa-water"></i>';
                        typeClass = 'water';
                        typeName = 'Water';
                    }

                    // Use the specific type name if available
                    typeName = incident.incidentType.name || typeName;
                }

                // Format status with appropriate badge
                let statusBadge = '<span class="status-badge status-unknown">Unknown</span>';
                if (incident.status) {
                    const statusName = incident.status.name;
                    if (statusName) {
                        const statusClass = statusName.toLowerCase();
                        statusBadge = `<span class="status-badge status-${statusClass}">${statusName}</span>`;
                    }
                }

                // Format date using shared utility
                const dateToFormat = incident.incidentDate || incident.createdAt;
                const formattedDate = window.FireAlertsUtils ?
                    window.FireAlertsUtils.formatters.formatDate(dateToFormat) :
                    (dateToFormat ? new Date(dateToFormat).toLocaleString() : 'N/A');

                // Create location string from address fields
                const location = incident.address ?
                    `${incident.address}, ${incident.city || ''}` :
                    (incident.city || 'Unknown Location');

                // Create table row for desktop/tablet
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>#${incident.id}</td>
                    <td>
                        <div class="incident-type-icon ${typeClass}">
                            ${typeIcon}
                            <span>${typeName}</span>
                        </div>
                    </td>
                    <td>${incident.title || 'Untitled Incident'}</td>
                    <td>${location}</td>
                    <td>${formattedDate}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="action-buttons">
                            <a href="view-incident.html?id=${incident.id}" class="btn-icon" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="edit-incident.html?id=${incident.id}" class="btn-icon" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn-icon delete-incident" data-id="${incident.id}" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);

                // Create mobile card
                if (mobileCards) {
                    const card = document.createElement('div');
                    card.className = 'table-card';
                    card.innerHTML = `
                        <div class="table-card-header">
                            <div class="table-card-title">${incident.title || 'Untitled Incident'}</div>
                            <div class="table-card-meta">
                                <div class="incident-type-icon ${typeClass}">
                                    ${typeIcon}
                                    <span>${typeName}</span>
                                </div>
                            </div>
                        </div>
                        <div class="table-card-content">
                            <div class="table-card-field">
                                <div class="table-card-label">ID</div>
                                <div class="table-card-value">#${incident.id}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Status</div>
                                <div class="table-card-value">${statusBadge}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Location</div>
                                <div class="table-card-value">${location}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Date</div>
                                <div class="table-card-value">${formattedDate}</div>
                            </div>
                        </div>
                        <div class="table-card-actions">
                            <a href="view-incident.html?id=${incident.id}" class="btn-icon btn-icon-mobile" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="edit-incident.html?id=${incident.id}" class="btn-icon btn-icon-mobile" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn-icon btn-icon-mobile delete-incident" data-id="${incident.id}" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    mobileCards.appendChild(card);
                }
            });

            // Add empty rows to maintain consistent height if needed
            if (incidents.length < incidentsPerPage) {
                for (let i = incidents.length; i < incidentsPerPage; i++) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.className = 'empty-row';
                    emptyRow.style.backgroundColor = '#272e48';
                    emptyRow.innerHTML = '<td colspan="7" style="background-color: transparent !important;">&nbsp;</td>';
                    tableBody.appendChild(emptyRow);
                }
            }

            // Add event listeners for delete buttons
            document.querySelectorAll('.delete-incident').forEach(button => {
                button.addEventListener('click', function() {
                    const incidentId = this.getAttribute('data-id');
                    if (incidentId && confirm('Are you sure you want to delete this incident?')) {
                        deleteIncident(incidentId);
                    }
                });
            });
        }

        // Initialize the incidents page
        function initializeIncidentsPage() {
            // Populate status filter dropdown from API
            populateStatusFilter();

            // Set up event handlers
            setupEventHandlers();

            // Load initial incidents
            loadIncidents();
        }

        // Populate status filter dropdown from API
        async function populateStatusFilter() {
            const statusFilter = document.getElementById('status-filter');

            try {
                // Check if API method exists
                if (API.settings && typeof API.settings.getIncidentStatuses === 'function') {
                    const response = await API.settings.getIncidentStatuses();

                    if (response && Array.isArray(response) && response.length > 0) {
                        // Clear existing options except "All Status"
                        statusFilter.innerHTML = '<option value="">All Status</option>';

                        // Add status options from API
                        response.forEach(status => {
                            const option = document.createElement('option');
                            option.value = status.name || status.status;
                            option.textContent = status.name || status.status;
                            statusFilter.appendChild(option);
                        });

                        console.log(`✅ Loaded ${response.length} status options for filter`);
                    }
                }
            } catch (error) {
                console.log('Using fallback status options for filter');
                // Keep the existing hardcoded options as fallback
            }
        }

        // Set up all event handlers
        function setupEventHandlers() {
            // Filter form submission
            const filterForm = document.getElementById('filter-form');
            if (filterForm) {
                filterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    currentPage = 1; // Reset to first page when filtering
                    loadIncidents();
                });
            }

            // Search input with debounce
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        currentPage = 1; // Reset to first page when searching
                        loadIncidents();
                    }, 500); // 500ms debounce
                });
            }

            // Filter dropdowns with debounced loading
            const typeFilter = document.getElementById('type-filter');
            const statusFilter = document.getElementById('status-filter');

            if (typeFilter) {
                typeFilter.addEventListener('change', function() {
                    currentPage = 1; // Reset to first page when filtering
                    debouncedLoadIncidents(false, 100); // Quick debounce for dropdowns
                });
            }

            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    currentPage = 1; // Reset to first page when filtering
                    debouncedLoadIncidents(false, 100); // Quick debounce for dropdowns
                });
            }

            // Table header sorting with debounced loading
            const sortableHeaders = document.querySelectorAll('.data-table th[data-sort]');
            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const sortColumn = this.getAttribute('data-sort');

                    // Toggle sort order if clicking the same column
                    if (currentSortColumn === sortColumn) {
                        currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
                    } else {
                        currentSortColumn = sortColumn;
                        currentSortOrder = 'ASC';
                    }

                    currentPage = 1; // Reset to first page when sorting
                    debouncedLoadIncidents(false, 50); // Very quick debounce for sorting
                });
            });
        }

        function deleteIncident(id) {
            API.incidents.delete(id)
                .then(response => {
                    if (response && response.success !== false) {
                        showNotification('Incident deleted successfully', 'success');
                        loadIncidents(); // Reload the table
                    } else {
                        showNotification('Failed to delete incident: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error deleting incident:', error);
                    showNotification('Error connecting to server', 'error');
                });
        }
    </script>
</body>
</html>
