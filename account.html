<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Account Settings</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="dashboard-grid">
                <!-- Profile Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Profile Information</div>
                    </div>
                    <div class="card-content">
                        <form id="profileForm">
                            <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 20px;">
                                <div style="position: relative; margin-bottom: 15px;">
                                    <!-- Use standardized avatar display that matches other pages -->
                                    <div class="user-avatar-icon" id="avatarPreview" style="width: 150px; height: 150px; margin-right: 0; font-size: 60px;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" style="position: absolute; bottom: 0; right: 0; border-radius: 50%; width: 36px; height: 36px; padding: 0; display: flex; justify-content: center; align-items: center;" id="changeAvatarBtn">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                    <input type="file" id="avatarInput" style="display: none;" accept="image/*">
                                </div>
                                <h3 id="profileDisplayName" style="margin: 0; color: var(--text-light);">Loading...</h3>
                                <p id="profileDisplayRole" style="margin: 5px 0 0 0; color: var(--text-secondary);">Loading...</p>
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" required>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone">
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" id="updateProfileBtn">
                                    <i class="fas fa-save"></i> Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Password Change Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Change Password</div>
                    </div>
                    <div class="card-content">
                        <form id="passwordForm">
                            <!-- Hidden username field for accessibility -->
                            <input type="text" id="username_hidden" name="username" autocomplete="username" style="display: none;" readonly>

                            <div class="form-group">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" autocomplete="current-password" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" autocomplete="new-password" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" autocomplete="new-password" required>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                                    <i class="fas fa-lock"></i> Change Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Notification Preferences -->
            <div class="card" style="margin-top: 20px;">
                <div class="card-header">
                    <div class="card-title">Notification Preferences</div>
                </div>
                <div class="card-content">
                    <form id="notificationPrefsForm">
                        <!-- Notification Methods -->
                        <div style="margin-bottom: 25px;">
                            <h4 style="margin-bottom: 15px; color: var(--text-light); font-size: 16px;">Notification Methods</h4>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="notifyByEmail" name="notifyByEmail">
                                    <span style="margin-left: 10px;">Email Notifications</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Receive email notifications about new incidents and system updates</small>
                            </div>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="notifyBySms" name="notifyBySms">
                                    <span style="margin-left: 10px;">SMS Notifications</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Receive text messages about urgent incidents</small>
                            </div>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="notifyByPush" name="notifyByPush">
                                    <span style="margin-left: 10px;">Push Notifications</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Show desktop notifications when you're logged in</small>
                            </div>
                        </div>

                        <!-- Incident Types -->
                        <div style="margin-bottom: 25px;">
                            <h4 style="margin-bottom: 15px; color: var(--text-light); font-size: 16px;">Incident Types</h4>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="fireIncidentAlert" name="fireIncidentAlert">
                                    <span style="margin-left: 10px;">Fire Incidents</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Receive alerts for fire-related incidents</small>
                            </div>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="waterIncidentAlert" name="waterIncidentAlert">
                                    <span style="margin-left: 10px;">Water Incidents</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Receive alerts for water-related incidents</small>
                            </div>

                            <div class="form-group">
                                <label class="form-check">
                                    <input type="checkbox" id="otherIncidentAlert" name="otherIncidentAlert">
                                    <span style="margin-left: 10px;">Other Incidents</span>
                                </label>
                                <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Receive alerts for all other types of incidents</small>
                            </div>
                        </div>

                        <!-- Additional Settings -->
                        <div style="margin-bottom: 25px;">
                            <h4 style="margin-bottom: 15px; color: var(--text-light); font-size: 16px;">Additional Settings</h4>



                            <div class="form-group">
                                <label class="form-label">Alert Radius (miles)</label>
                                <input type="number" class="form-control" id="alertRadius" name="alertRadius" min="1" max="100" style="width: 120px;" placeholder="10">
                                <small style="color: var(--text-secondary); display: block; margin-top: 5px;">
                                    <strong>How far from your location should we send you alerts?</strong><br>
                                    For example: Setting this to 15 miles means you'll only receive notifications for incidents within a 15-mile radius of your registered address.
                                    Incidents further away won't trigger notifications. Default is 10 miles.
                                </small>
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Quiet Hours Start</label>
                                        <input type="time" class="form-control" id="quietHoursStart" name="quietHoursStart">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Quiet Hours End</label>
                                        <input type="time" class="form-control" id="quietHoursEnd" name="quietHoursEnd">
                                    </div>
                                </div>
                            </div>
                            <small style="color: var(--text-secondary); display: block; margin-top: -10px;">No notifications will be sent during quiet hours</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" id="savePreferencesBtn">
                                <i class="fas fa-save"></i> Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account Activity -->
            <div class="card" style="margin-top: 20px;">
                <div class="card-header">
                    <div class="card-title">Account Activity</div>
                    <div id="activityCount" style="color: var(--text-secondary); font-size: 14px;">
                        Loading...
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-wrapper">
                        <table class="data-table" id="accountActivityTable">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Date/Time</th>
                                    <th>IP Address</th>
                                    <th>Device</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Anti-flicker loading with blue background -->
                                <tr class="skeleton-row">
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                                </tr>
                                <tr class="skeleton-row">
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                                </tr>
                                <tr class="skeleton-row">
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text"></div></td>
                                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <div id="activityPagination" style="display: none; margin-top: 20px;">
                        <ul class="pagination">
                            <li>
                                <a href="#" id="prevPage" title="Previous Page">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <li id="pageNumbers">
                                <!-- Page numbers will be inserted here -->
                            </li>
                            <li>
                                <a href="#" id="nextPage" title="Next Page">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <!-- API Utility -->
    <script src="js/api.js?v=2"></script>
    <script>
        // Global variables to store user data
        let currentUserData = null;

        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Function to populate profile form
        function populateProfileForm(profile) {
            if (!profile) return;
            document.getElementById('first_name').value = profile.firstName || '';
            document.getElementById('last_name').value = profile.lastName || '';
            document.getElementById('email').value = profile.email || '';
            document.getElementById('phone').value = profile.phone || '';

            // Populate hidden username field for accessibility
            const hiddenUsernameField = document.getElementById('username_hidden');
            if (hiddenUsernameField) {
                hiddenUsernameField.value = profile.username || profile.email || '';
            }

            // Update profile header elements
            const profileDisplayName = document.getElementById('profileDisplayName');
            if (profileDisplayName) {
                profileDisplayName.textContent = `${profile.firstName || ''} ${profile.lastName || ''}`.trim() || 'User';
            }

            const profileDisplayRole = document.getElementById('profileDisplayRole');
            if (profileDisplayRole && profile.role) {
                profileDisplayRole.textContent = profile.role.charAt(0).toUpperCase() + profile.role.slice(1);
            }

            // Update avatar using shared utility
            if (window.FireAlertsUtils && window.FireAlertsUtils.avatar) {
                window.FireAlertsUtils.avatar.updateUserAvatar(profile);
            }
        }

        // Function to load user profile
        function loadUserProfile() {
            API.account.getProfile()
                .then(response => {
                    if (response && response.success !== false) {
                        currentUserData = response.data || response;
                        populateProfileForm(currentUserData);
                    } else {
                        showNotification('Error loading profile: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching user profile:', error);

                    // Check for authentication errors
                    if (error.message && error.message.includes('401')) {
                        showNotification('Authentication required. Please log in again.', 'error');
                        if (window.AuthCheck && window.AuthCheck.redirectToLogin) {
                            setTimeout(() => window.AuthCheck.redirectToLogin('session_expired'), 2000);
                        }
                    } else {
                        showNotification('Error connecting to server for profile data.', 'error');
                    }
                });
        }

        // Global pagination state
        let currentActivityPage = 1;
        let activityPagination = null;

        // Function to load account activity with pagination
        function loadAccountActivity(page = 1) {
            const tbody = document.querySelector('#accountActivityTable tbody');
            const activityCount = document.getElementById('activityCount');
            const paginationDiv = document.getElementById('activityPagination');

            if (!tbody) return;

            // Show skeleton loading rows
            tbody.innerHTML = `
                <tr class="skeleton-row">
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                </tr>
                <tr class="skeleton-row">
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                </tr>
                <tr class="skeleton-row">
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text"></div></td>
                    <td><div class="skeleton-text skeleton-text-long"></div></td>
                </tr>
            `;

            const limit = 20;
            const offset = (page - 1) * limit;

            API.account.getActivity({ limit, offset })
                .then(data => {
                    tbody.innerHTML = ''; // Clear loading/previous data

                    if (data && data.success !== false) {
                        const activities = data.data || [];
                        activityPagination = data.pagination || null;
                        currentActivityPage = page;

                        // Update activity count display
                        if (activityPagination) {
                            const start = activityPagination.offset + 1;
                            const end = Math.min(activityPagination.offset + activityPagination.limit, activityPagination.total);
                            activityCount.textContent = `Showing ${start}-${end} of ${activityPagination.total} activities`;
                        } else {
                            activityCount.textContent = `${activities.length} activities`;
                        }

                        if (activities && activities.length > 0) {
                            activities.forEach(activity => {
                                const row = tbody.insertRow();
                                row.innerHTML = `
                                    <td>${activity.action || 'N/A'}</td>
                                    <td>${activity.created_at ? new Date(activity.created_at).toLocaleString() : 'N/A'}</td>
                                    <td>${activity.ip_address || 'N/A'}</td>
                                    <td>${truncateUserAgent(activity.user_agent || 'N/A')}</td>
                                `;
                            });

                            // Show pagination if there are multiple pages
                            if (activityPagination && activityPagination.totalPages > 1) {
                                renderActivityPagination();
                                paginationDiv.style.display = 'block';
                            } else {
                                paginationDiv.style.display = 'none';
                            }
                        } else {
                            tbody.innerHTML = '<tr><td colspan="4">No recent activity found.</td></tr>';
                            paginationDiv.style.display = 'none';
                        }
                    } else {
                        tbody.innerHTML = '<tr><td colspan="4">Could not load activity.</td></tr>';
                        activityCount.textContent = 'Error loading activities';
                        paginationDiv.style.display = 'none';
                        showNotification('Error loading account activity: ' + (data?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    tbody.innerHTML = '<tr><td colspan="4">Error loading activity.</td></tr>';
                    activityCount.textContent = 'Error loading activities';
                    paginationDiv.style.display = 'none';
                    console.error('Error fetching account activity:', error);

                    // Check for authentication errors
                    if (error.message && error.message.includes('401')) {
                        showNotification('Authentication required. Please log in again.', 'error');
                        if (window.AuthCheck && window.AuthCheck.redirectToLogin) {
                            setTimeout(() => window.AuthCheck.redirectToLogin('session_expired'), 2000);
                        }
                    } else {
                        showNotification('Error connecting to server for activity log.', 'error');
                    }
                });
        }

        // Helper function to truncate user agent strings
        function truncateUserAgent(userAgent) {
            if (!userAgent || userAgent === 'N/A') return 'N/A';

            // Extract browser name from user agent
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';

            // Fallback to truncated version
            return userAgent.length > 30 ? userAgent.substring(0, 30) + '...' : userAgent;
        }

        // Function to load notification preferences
        function loadNotificationPreferences() {
            if (!API.account.getPreferences) {
                console.error('API.account.getPreferences is not available');
                showNotification('API method not available. Please refresh the page.', 'error');
                return;
            }

            API.account.getPreferences()
                .then(response => {
                    if (response && response.success !== false) {
                        const prefs = response.data || response;
                        populateNotificationPreferences(prefs);
                    } else {
                        showNotification('Error loading preferences: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching notification preferences:', error);

                    // Check for authentication errors
                    if (error.message && error.message.includes('401')) {
                        showNotification('Authentication required. Please log in again.', 'error');
                        if (window.AuthCheck && window.AuthCheck.redirectToLogin) {
                            setTimeout(() => window.AuthCheck.redirectToLogin('session_expired'), 2000);
                        }
                    } else {
                        showNotification('Error connecting to server for preferences.', 'error');
                    }
                });
        }

        // Function to render pagination controls
        function renderActivityPagination() {
            if (!activityPagination) return;

            const pageNumbers = document.getElementById('pageNumbers');
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');

            // Clear existing page numbers
            pageNumbers.innerHTML = '';

            const currentPage = activityPagination.currentPage;
            const totalPages = activityPagination.totalPages;

            // Previous button state
            if (currentPage <= 1) {
                prevPage.style.opacity = '0.5';
                prevPage.style.pointerEvents = 'none';
            } else {
                prevPage.style.opacity = '1';
                prevPage.style.pointerEvents = 'auto';
            }

            // Next button state
            if (currentPage >= totalPages) {
                nextPage.style.opacity = '0.5';
                nextPage.style.pointerEvents = 'none';
            } else {
                nextPage.style.opacity = '1';
                nextPage.style.pointerEvents = 'auto';
            }

            // Generate page numbers (show max 5 pages)
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // Adjust start if we're near the end
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#';
                a.textContent = i;
                a.onclick = (e) => {
                    e.preventDefault();
                    if (i !== currentPage) {
                        loadAccountActivity(i);
                    }
                };

                if (i === currentPage) {
                    li.classList.add('active');
                }

                li.appendChild(a);
                pageNumbers.appendChild(li);
            }
        }

        // Function to populate notification preferences form
        function populateNotificationPreferences(prefs) {
            if (!prefs) return;

            // Notification methods
            document.getElementById('notifyByEmail').checked = prefs.notifyByEmail !== false;
            document.getElementById('notifyBySms').checked = prefs.notifyBySms === true;
            document.getElementById('notifyByPush').checked = prefs.notifyByPush === true;

            // Incident types
            document.getElementById('fireIncidentAlert').checked = prefs.fireIncidentAlert !== false;
            document.getElementById('waterIncidentAlert').checked = prefs.waterIncidentAlert !== false;
            document.getElementById('otherIncidentAlert').checked = prefs.otherIncidentAlert !== false;

            // Additional settings - removed dailyDigest as per user preference
            document.getElementById('alertRadius').value = prefs.alertRadius || 10;

            // Quiet hours (convert from database format if needed)
            if (prefs.quietHoursStart) {
                document.getElementById('quietHoursStart').value = prefs.quietHoursStart;
            }
            if (prefs.quietHoursEnd) {
                document.getElementById('quietHoursEnd').value = prefs.quietHoursEnd;
            }
        }


        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        // Removed local sidebar rendering function - now using shared navigation system exclusively

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM content loaded, starting sidebar rendering for account page');
            console.log('Current URL:', window.location.href);
            console.log('Current pathname:', window.location.pathname);

            // Use shared sidebar rendering utility only - no local fallback
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                console.log('Using shared sidebar utility for account page');
                window.FireAlertsUtils.renderRoleBasedSidebar('account');

                // Verify sidebar was rendered
                setTimeout(() => {
                    const sidebarItems = document.querySelectorAll('.sidebar .nav-item');
                    console.log(`Sidebar rendered with ${sidebarItems.length} items`);
                    const activeItem = document.querySelector('.sidebar .nav-item.active');
                    if (activeItem) {
                        console.log('Active item:', activeItem.textContent.trim());
                    } else {
                        console.warn('No active navigation item found');
                    }
                }, 100);
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Load initial data
            loadUserProfile();
            loadAccountActivity();
            loadNotificationPreferences();

            // Pagination event handlers
            document.getElementById('prevPage').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentActivityPage > 1) {
                    loadAccountActivity(currentActivityPage - 1);
                }
            });

            document.getElementById('nextPage').addEventListener('click', function(e) {
                e.preventDefault();
                if (activityPagination && currentActivityPage < activityPagination.totalPages) {
                    loadAccountActivity(currentActivityPage + 1);
                }
            });

            // Profile form submission handler
            document.getElementById('profileForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const profileData = {
                    firstName: formData.get('first_name'),
                    lastName: formData.get('last_name'),
                    email: formData.get('email'),
                    phone: formData.get('phone')
                };

                const submitBtn = document.getElementById('updateProfileBtn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
                submitBtn.disabled = true;

                API.account.updateProfile(profileData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Profile updated successfully!', 'success');
                            currentUserData = response.data || response;
                            populateProfileForm(currentUserData);
                        } else {
                            showNotification('Error updating profile: ' + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating profile:', error);
                        showNotification('Error connecting to server', 'error');
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });

            // Password form submission handler
            document.getElementById('passwordForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const passwordData = {
                    currentPassword: formData.get('current_password'),
                    newPassword: formData.get('new_password'),
                    confirmPassword: formData.get('confirm_password')
                };

                // Client-side validation
                if (passwordData.newPassword !== passwordData.confirmPassword) {
                    showNotification('New password and confirmation do not match', 'error');
                    return;
                }

                if (passwordData.newPassword.length < 6) {
                    showNotification('New password must be at least 6 characters long', 'error');
                    return;
                }

                const submitBtn = document.getElementById('changePasswordBtn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Changing...';
                submitBtn.disabled = true;

                API.account.updatePassword(passwordData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Password changed successfully!', 'success');
                            this.reset(); // Clear the form
                        } else {
                            showNotification('Error changing password: ' + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error changing password:', error);
                        showNotification('Error connecting to server', 'error');
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });

            // Avatar upload functionality
            document.getElementById('changeAvatarBtn').addEventListener('click', function() {
                document.getElementById('avatarInput').click();
            });

            document.getElementById('avatarInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) return;

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    showNotification('Please select a valid image file', 'error');
                    return;
                }

                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    showNotification('Image file must be smaller than 5MB', 'error');
                    return;
                }

                // Show preview immediately using shared utilities
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Use shared avatar utility to update all avatar displays
                    if (window.FireAlertsUtils && window.FireAlertsUtils.avatar) {
                        window.FireAlertsUtils.avatar.updateAvatarWithUpload(e.target.result);
                    } else {
                        // Fallback to direct update
                        const avatarPreview = document.getElementById('avatarPreview');
                        if (avatarPreview) {
                            avatarPreview.src = e.target.result;
                        }
                    }
                };
                reader.readAsDataURL(file);

                // Upload the file
                const formData = new FormData();
                formData.append('avatar', file);

                // Show loading state
                const changeBtn = document.getElementById('changeAvatarBtn');
                const originalContent = changeBtn.innerHTML;
                changeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                changeBtn.disabled = true;

                // Upload avatar using API
                if (API.account && API.account.uploadAvatar) {
                    API.account.uploadAvatar(formData)
                        .then(response => {
                            if (response && response.success !== false) {
                                showNotification('Avatar updated successfully!', 'success');
                                // Update user data with new avatar URL
                                if (currentUserData && response.data) {
                                    currentUserData.avatarUrl = response.data.avatarUrl || response.avatarUrl;
                                }
                            } else {
                                showNotification('Error uploading avatar: ' + (response?.message || 'Unknown error'), 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error uploading avatar:', error);
                            showNotification('Error uploading avatar', 'error');
                        })
                        .finally(() => {
                            changeBtn.innerHTML = originalContent;
                            changeBtn.disabled = false;
                        });
                } else {
                    // Fallback: just show success message for now
                    showNotification('Avatar preview updated (upload functionality pending)', 'info');
                    changeBtn.innerHTML = originalContent;
                    changeBtn.disabled = false;
                }
            });

            // Notification preferences form handler
            document.getElementById('notificationPrefsForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const preferencesData = {
                    notifyByEmail: document.getElementById('notifyByEmail').checked,
                    notifyBySms: document.getElementById('notifyBySms').checked,
                    notifyByPush: document.getElementById('notifyByPush').checked,
                    fireIncidentAlert: document.getElementById('fireIncidentAlert').checked,
                    waterIncidentAlert: document.getElementById('waterIncidentAlert').checked,
                    otherIncidentAlert: document.getElementById('otherIncidentAlert').checked,
                    dailyDigest: false, // Removed daily digest option as per user preference
                    alertRadius: parseInt(document.getElementById('alertRadius').value) || 10,
                    quietHoursStart: document.getElementById('quietHoursStart').value || null,
                    quietHoursEnd: document.getElementById('quietHoursEnd').value || null
                };

                // Validate notification preferences
                if (!preferencesData.notifyByEmail && !preferencesData.notifyBySms && !preferencesData.notifyByPush) {
                    showNotification('Please enable at least one notification method.', 'warning');
                    return;
                }

                if (!preferencesData.fireIncidentAlert && !preferencesData.waterIncidentAlert && !preferencesData.otherIncidentAlert) {
                    if (!confirm('You have disabled all incident types. You will not receive any incident notifications. Continue?')) {
                        return;
                    }
                }

                const submitBtn = document.getElementById('savePreferencesBtn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                submitBtn.disabled = true;

                if (!API.account.updatePreferences) {
                    console.error('API.account.updatePreferences is not available');
                    showNotification('API method not available. Please refresh the page.', 'error');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                API.account.updatePreferences(preferencesData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Notification preferences saved successfully!', 'success');
                            // Reload preferences to ensure UI is in sync
                            loadNotificationPreferences();
                        } else {
                            showNotification('Error saving preferences: ' + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error saving notification preferences:', error);

                        // Check for authentication errors
                        if (error.message && error.message.includes('401')) {
                            showNotification('Authentication required. Please log in again.', 'error');
                            if (window.AuthCheck && window.AuthCheck.redirectToLogin) {
                                setTimeout(() => window.AuthCheck.redirectToLogin('session_expired'), 2000);
                            }
                        } else {
                            showNotification('Error connecting to server', 'error');
                        }
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });
        });
    </script>
</body>
</html>
