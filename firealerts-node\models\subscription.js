module.exports = (sequelize, DataTypes) => {
  const Subscription = sequelize.define('subscription', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    plan: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'standard'
    },
    maxUsers: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 5
    },
    billingCycle: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'monthly'
    },
    billingEmail: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'active'
    },
    nextBillingDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    currentUsers: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'subscriptions',
    underscored: true,
    indexes: [
      {
        fields: ['user_id'],  // Updated to reference user_id instead of subscriber_id
        unique: true
      }
    ]
  });
  return Subscription;
};
