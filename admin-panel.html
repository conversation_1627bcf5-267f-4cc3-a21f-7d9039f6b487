<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Admin Panel</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <style>
        .stats-card {
            display: flex;
            flex-direction: column;
            background-color: var(--secondary-dark);
            border-radius: 5px;
            padding: 20px;
            height: 100%;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .stats-subscribers {
            background-color: rgba(30, 136, 229, 0.2);
            color: var(--accent-blue);
        }

        .stats-incidents {
            background-color: rgba(229, 57, 53, 0.2);
            color: var(--accent-red);
        }

        .stats-users {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--accent-green);
        }

        .stats-notifications {
            background-color: rgba(255, 143, 0, 0.2);
            color: var(--accent-orange);
        }

        .stats-number {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-label {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .tabs {
            margin-bottom: 20px;
        }

        .tab-list {
            display: flex;
            overflow-x: auto;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            position: relative;
        }

        .tab-button:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: var(--text-light);
        }

        .tab-button.active:after {
            background-color: var(--accent-blue);
        }

        .tab-button:hover {
            color: var(--text-light);
        }

        .tab-button:hover:after {
            background-color: var(--accent-blue);
            opacity: 0.5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Modal Styling Improvements */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1050;
            backdrop-filter: blur(2px);
            padding: 20px;
        }

        .modal-content {
            background-color: var(--primary-dark);
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 550px;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-light);
            font-size: 18px;
        }

        .modal-body {
            padding: 20px;
        }

        .close-modal {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            transition: color 0.2s;
            line-height: 1;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-modal:hover {
            color: var(--text-light);
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Empty state for tables */
        .empty-state {
            padding: 30px;
            text-align: center;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin: 20px 0;
        }

        .empty-state i {
            font-size: 40px;
            color: var(--text-secondary);
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .empty-state h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--text-light);
        }

        .empty-state p {
            margin-bottom: 20px;
            color: var(--text-secondary);
        }

        /* Form help text styling */
        .form-help {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 5px;
            line-height: 1.4;
        }

        /* Twilio fields group styling */
        #twilioFieldsGroup {
            background-color: rgba(30, 136, 229, 0.1);
            border: 1px solid rgba(30, 136, 229, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        #twilioFieldsGroup::before {
            content: "📱 Twilio SMS Configuration";
            display: block;
            font-weight: 600;
            color: var(--accent-blue);
            margin-bottom: 15px;
            font-size: 14px;
        }

        /* Anti-flicker table styling */
        .table-wrapper {
            background-color: var(--secondary-dark) !important;
        }

        .data-table {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody tr {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody td {
            background-color: transparent !important;
        }

        /* Preserve hover effects */
        .data-table tbody tr:hover {
            background-color: var(--hover-bg) !important;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- Admin Panel Content -->
            <!-- Stats Overview -->
            <div class="dashboard-grid">
                <div class="card" style="padding: 0;">
                    <div class="stats-card">
                        <div class="stats-icon stats-subscribers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number" id="totalSubscribersCount">Loading...</div>
                        <div class="stats-label">Total Subscribers</div>
                    </div>
                </div>

                <div class="card" style="padding: 0;">
                    <div class="stats-card">
                        <div class="stats-icon stats-incidents">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="stats-number" id="activeIncidentsCount">Loading...</div>
                        <div class="stats-label">Active Incidents</div>
                    </div>
                </div>

                <div class="card" style="padding: 0;">
                    <div class="stats-card">
                        <div class="stats-icon stats-users">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="stats-number" id="totalUsersCount">Loading...</div>
                        <div class="stats-label">Total Users</div>
                    </div>
                </div>

                <div class="card" style="padding: 0;">
                    <div class="stats-card">
                        <div class="stats-icon stats-notifications">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stats-number" id="notificationsSentCount">Loading...</div>
                        <div class="stats-label">Notifications Sent</div>
                    </div>
                </div>
            </div>

            <!-- Main Admin Card with Tabs -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">System Administration</div>
                </div>
                <div class="card-content">
                    <!-- Tabs for different sections -->
                    <div class="tabs">
                        <div class="tab-list">
                            <button class="tab-button active" data-tab="users">User Management</button>
                            <button class="tab-button" data-tab="system">System Settings</button>
                            <button class="tab-button" data-tab="api">API Keys</button>
                            <button class="tab-button" data-tab="logs">System Logs</button>
                            <button class="tab-button" data-tab="backup">Backup & Restore</button>
                        </div>

                        <!-- Users Tab -->
                        <div class="tab-content active" id="users">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0;">FireAlerts911 Dispatcher Management</h3>
                                <button class="btn btn-primary" id="addDispatcherBtn">
                                    <i class="fas fa-user-plus"></i> Add Dispatcher
                                </button>
                            </div>

                            <!-- Dispatcher count display -->
                            <div style="margin-bottom: 15px;">
                                <span id="dispatcherCount" style="color: var(--text-secondary); font-size: 14px;">Loading FireAlerts911 dispatchers...</span>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Employee ID</th>
                                            <th>Last Login</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dispatchersTableBody">
                                        <!-- FireAlerts911 dispatcher users will be loaded dynamically -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Card View for Dispatchers -->
                            <div class="table-mobile-cards" id="dispatchers-mobile-cards">
                                <!-- Mobile cards will be populated by JavaScript -->
                            </div>

                            <!-- Pagination Controls for Dispatchers -->
                            <div id="dispatchersPagination" style="display: none; margin-top: 20px;">
                                <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
                                    <button id="prevDispatchersPage" class="btn btn-outline" style="padding: 8px 12px;">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </button>
                                    <div id="dispatchersPageNumbers" style="display: flex; gap: 5px;">
                                        <!-- Page numbers will be inserted here -->
                                    </div>
                                    <button id="nextDispatchersPage" class="btn btn-outline" style="padding: 8px 12px;">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings Tab -->
                        <div class="tab-content" id="system">
                            <div id="systemSettingsLoading" style="text-align: center; padding: 40px;">
                                <i class="fas fa-spinner fa-spin fa-2x" style="color: var(--primary-color);"></i>
                                <p style="margin-top: 15px; color: var(--text-secondary);">Loading system settings...</p>
                            </div>

                            <form id="systemSettingsForm" style="display: none;">
                                <h3 class="section-title">General Settings</h3>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">System Name</label>
                                            <input type="text" class="form-control" name="system_name" id="systemName">
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Default Time Zone</label>
                                            <select class="form-control" name="timezone" id="systemTimezone">
                                                <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                                                <option value="America/Denver">Mountain Time (US & Canada)</option>
                                                <option value="America/Chicago">Central Time (US & Canada)</option>
                                                <option value="America/New_York">Eastern Time (US & Canada)</option>
                                                <option value="UTC">UTC</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <h3 class="section-title">Notification Settings</h3>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Default Email From</label>
                                            <input type="email" class="form-control" name="default_email" id="defaultEmail">
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">SMS Provider</label>
                                            <select class="form-control" name="sms_provider" id="smsProvider">
                                                <option value="twilio">Twilio</option>
                                                <option value="aws_sns">AWS SNS</option>
                                                <option value="nexmo">Nexmo</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-check">
                                        <input type="checkbox" name="throttle_notifications" id="throttleNotifications">
                                        <span style="margin-left: 10px;">Throttle notifications during high-volume incidents</span>
                                    </label>
                                    <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Prevents overloading subscribers with too many notifications</small>
                                </div>

                                <h3 class="section-title">Security Settings</h3>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Session Timeout (minutes)</label>
                                            <input type="number" class="form-control" name="session_timeout" id="sessionTimeout" min="5" max="120">
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Password Expiry (days)</label>
                                            <input type="number" class="form-control" name="password_expiry" id="passwordExpiry" min="0" max="365">
                                            <small style="color: var(--text-secondary);">Use 0 for no expiration</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-check">
                                        <input type="checkbox" name="force_2fa" id="force2FA">
                                        <span style="margin-left: 10px;">Force 2FA for all admin accounts</span>
                                    </label>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="button" class="btn btn-outline">Reset to Defaults</button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- API Keys Tab -->
                        <div class="tab-content" id="api">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="margin: 0;">API Keys & Integrations</h3>
                                <div style="display: flex; gap: 10px;">
                                    <button class="btn btn-outline" id="migrateMailgunBtn" style="display: none;">
                                        <i class="fas fa-sync-alt"></i> Migrate Mailgun
                                    </button>
                                    <button class="btn btn-primary" id="addApiKeyBtn">
                                        <i class="fas fa-plus"></i> Add New API Key
                                    </button>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Service</th>
                                            <th>Key Name</th>
                                            <th>API Key</th>
                                            <th>Status</th>
                                            <th>Last Used</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="apiKeysTableBody">
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Card View for API Keys -->
                            <div class="table-mobile-cards" id="api-keys-mobile-cards">
                                <!-- Mobile cards will be populated by JavaScript -->
                            </div>

                            <!-- Add/Edit API Key Modal -->
                            <div class="modal" id="apiKeyModal">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="apiKeyModalTitle">Add New API Key</h3>
                                        <button class="close-modal">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="apiKeyForm">
                                            <input type="hidden" id="apiKeyMode" value="add">
                                            <input type="hidden" id="apiKeyId" value="">

                                            <div class="form-group">
                                                <label class="form-label">Service</label>
                                                <select class="form-control" id="apiKeyService" required>
                                                    <option value="">Select a Service</option>
                                                    <option value="google_maps">Google Maps</option>
                                                    <option value="twilio">Twilio SMS</option>
                                                    <option value="mailgun">Mailgun Email</option>
                                                    <option value="estated">Estated API</option>
                                                    <option value="openweather">OpenWeather API</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>

                                            <!-- Standard Key Name and Value fields - shown for non-Twilio services -->
                                            <div class="form-group" id="standardKeyNameGroup">
                                                <label class="form-label">Key Name</label>
                                                <input type="text" class="form-control" id="apiKeyName" required>
                                            </div>

                                            <div class="form-group" id="standardKeyValueGroup">
                                                <label class="form-label">API Key</label>
                                                <input type="text" class="form-control" id="apiKeyValue" required>
                                            </div>

                                            <!-- Twilio-specific fields - only shown when Twilio is selected -->
                                            <div id="twilioFieldsGroup" style="display: none;">
                                                <div class="form-group">
                                                    <label class="form-label">Twilio Account SID</label>
                                                    <input type="text" class="form-control" id="twilioAccountSid" placeholder="Starts with AC...">
                                                    <div class="form-help">Your Twilio Account SID from the Twilio Console</div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Twilio Auth Token</label>
                                                    <input type="text" class="form-control" id="twilioAuthToken" placeholder="Your Twilio Auth Token">
                                                    <div class="form-help">Your Twilio Auth Token from the Twilio Console</div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="form-label">Twilio Phone Number</label>
                                                    <input type="tel" class="form-control" id="twilioFromNumber" placeholder="+**********">
                                                    <div class="form-help">Your Twilio phone number in E.164 format (e.g., +**********)</div>
                                                </div>
                                            </div>

                                            <!-- Mailgun Domain Field - only shown when Mailgun is selected -->
                                            <div class="form-group" id="mailgunDomainGroup" style="display: none;">
                                                <label class="form-label">Mailgun Domain</label>
                                                <input type="text" class="form-control" id="mailgunDomain" placeholder="e.g., mg.yourdomain.com">
                                                <div class="form-help">The domain configured in your Mailgun account</div>
                                                <div class="form-info" style="margin-top: 8px; padding: 8px; background-color: #e3f2fd; border-left: 3px solid #1976d2; border-radius: 4px; font-size: 13px; color: #1565c0;">
                                                    <i class="fas fa-info-circle"></i> <strong>Note:</strong> Mailgun requires both an API key and domain. These will be saved as separate entries for security and flexibility.
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-check">
                                                    <input type="checkbox" id="apiKeyStatus" checked>
                                                    <span style="margin-left: 10px;">Active</span>
                                                </label>
                                            </div>

                                            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                                <button type="button" class="btn btn-outline cancel-modal">Cancel</button>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> <span id="apiKeySaveButtonText">Save API Key</span>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Logs Tab -->
                        <div class="tab-content" id="logs">
                            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
                                <div style="flex: 1; min-width: 200px;">
                                    <input type="text" class="form-control" placeholder="Search logs..." style="width: 100%;">
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <select class="form-control">
                                        <option value="all">All Log Types</option>
                                        <option value="error">Errors</option>
                                        <option value="warning">Warnings</option>
                                        <option value="info">Info</option>
                                        <option value="debug">Debug</option>
                                    </select>
                                    <input type="date" class="form-control" id="logDateFilter">
                                    <button class="btn btn-primary">Filter</button>
                                </div>
                            </div>

                            <!-- Logs count display -->
                            <div style="margin-bottom: 15px;">
                                <span id="logsCount" style="color: var(--text-secondary); font-size: 14px;">Loading logs...</span>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th style="width: 180px;">Timestamp</th>
                                            <th style="width: 100px;">Level</th>
                                            <th>Message</th>
                                            <th style="width: 150px;">Component</th>
                                            <th style="width: 120px;">User</th>
                                            <th style="width: 100px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="logsTableBody">
                                        <!-- Log rows will be dynamically loaded here by JavaScript -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Card View for Logs -->
                            <div class="table-mobile-cards" id="logs-mobile-cards">
                                <!-- Mobile cards will be populated by JavaScript -->
                            </div>

                            <!-- Pagination Controls -->
                            <div id="logsPagination" style="display: none; margin-top: 20px; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
                                    <button id="prevLogsPage" class="btn btn-outline" style="padding: 8px 12px;">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </button>
                                    <div id="logsPageNumbers" style="display: flex; gap: 5px;">
                                        <!-- Page numbers will be inserted here -->
                                    </div>
                                    <button id="nextLogsPage" class="btn btn-outline" style="padding: 8px 12px;">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>

                            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                <button class="btn btn-outline">
                                    <i class="fas fa-download"></i> Export Logs
                                </button>
                                <button class="btn btn-outline btn-danger">
                                    <i class="fas fa-eraser"></i> Clear Logs
                                </button>
                            </div>
                        </div>

                        <!-- Backup & Restore Tab -->
                        <div class="tab-content" id="backup">
                            <h3 class="section-title">Backup Database</h3>
                            <p>Create a backup of your database. This includes all incidents, subscribers, users, and system settings.</p>

                            <div style="background-color: rgba(40, 167, 69, 0.1); border: 1px solid var(--accent-green); border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                                <h4 style="margin-top: 0; color: var(--accent-green);">
                                    <i class="fas fa-check-circle"></i> Automatic Backups Enabled
                                </h4>
                                <p style="margin-bottom: 0;">Automatic daily backups are configured and running. Last successful backup: <strong>August 12, 2023 04:00 AM</strong></p>
                            </div>

                            <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                                <button class="btn btn-primary">
                                    <i class="fas fa-database"></i> Create Manual Backup
                                </button>
                                <button class="btn btn-outline">
                                    <i class="fas fa-cog"></i> Configure Auto Backups
                                </button>
                            </div>

                            <h3 class="section-title">Recent Backups</h3>

                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Backup Date</th>
                                            <th>Type</th>
                                            <th>Size</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2023-08-12 04:00 AM</td>
                                            <td>Automatic</td>
                                            <td>24.6 MB</td>
                                            <td><span class="status-badge status-active">Successful</span></td>
                                            <td>
                                                <div class="incident-actions">
                                                    <button class="btn-icon" data-tooltip="Download">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Restore">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Delete">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2023-08-11 04:00 AM</td>
                                            <td>Automatic</td>
                                            <td>24.5 MB</td>
                                            <td><span class="status-badge status-active">Successful</span></td>
                                            <td>
                                                <div class="incident-actions">
                                                    <button class="btn-icon" data-tooltip="Download">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Restore">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Delete">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2023-08-10 10:15 AM</td>
                                            <td>Manual</td>
                                            <td>24.3 MB</td>
                                            <td><span class="status-badge status-active">Successful</span></td>
                                            <td>
                                                <div class="incident-actions">
                                                    <button class="btn-icon" data-tooltip="Download">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Restore">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                    <button class="btn-icon" data-tooltip="Delete">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h3 class="section-title">Restore Database</h3>
                            <p>Restore your database from a backup file. This will replace all current data with the data from the backup.</p>

                            <div style="background-color: rgba(229, 57, 53, 0.1); border: 1px solid var(--accent-red); border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                                <h4 style="margin-top: 0; color: var(--accent-red);">
                                    <i class="fas fa-exclamation-triangle"></i> Warning
                                </h4>
                                <p style="margin-bottom: 0;">Restoring a backup will overwrite all current data. This action cannot be undone.</p>
                            </div>

                            <div style="display: flex; gap: 10px; align-items: flex-end;">
                                <div style="flex: 1;">
                                    <label class="form-label">Upload Backup File</label>
                                    <input type="file" class="form-control" accept=".sql,.zip">
                                </div>
                                <button class="btn btn-danger">
                                    <i class="fas fa-upload"></i> Restore from Upload
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dispatcher Management Modal -->
    <div id="dispatcherModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="dispatcherModalTitle">Add New FireAlerts911 Dispatcher</h3>
                <button class="close-modal" onclick="closeDispatcherModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="dispatcherForm">
                    <input type="hidden" id="dispatcherMode" value="add">
                    <input type="hidden" id="dispatcherId" value="">

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="dispatcherFirstName" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="dispatcherLastName" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="dispatcherEmail" required>
                                <div class="form-help">Use FireAlerts911 company email address</div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="dispatcherPhone">
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">Role *</label>
                                <select class="form-control" id="dispatcherRole" required>
                                    <option value="dispatcher">FireAlerts911 Dispatcher</option>
                                </select>
                                <div class="form-help">Dispatchers have system-wide access to manage incidents across all companies.</div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">Employee ID</label>
                                <input type="text" class="form-control" id="dispatcherEmployeeId" placeholder="Optional">
                                <div class="form-help">Internal FireAlerts911 employee identifier</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-check">
                            <input type="checkbox" id="dispatcherStatus" checked>
                            <span style="margin-left: 10px;">Active Status</span>
                        </label>
                        <div class="form-help">Inactive dispatchers cannot log in to the system.</div>
                    </div>

                    <div class="form-group" id="sendWelcomeEmailGroup">
                        <label class="form-check">
                            <input type="checkbox" id="sendWelcomeEmail" checked>
                            <span style="margin-left: 10px;">Send Welcome Email</span>
                        </label>
                        <div class="form-help">Sends login credentials to the dispatcher's FireAlerts911 email address.</div>
                    </div>

                    <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" class="btn btn-outline" onclick="closeDispatcherModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="dispatcherSaveBtn">
                            <i class="fas fa-save"></i> <span id="dispatcherSaveButtonText">Save Dispatcher</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Dispatcher View Modal -->
    <div id="dispatcherViewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>FireAlerts911 Dispatcher Details</h3>
                <button class="close-modal" onclick="closeDispatcherViewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="dispatcherViewContent">
                    <!-- FireAlerts911 dispatcher details will be loaded here -->
                </div>
                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn btn-outline" onclick="closeDispatcherViewModal()">Close</button>
                    <button type="button" class="btn btn-primary" id="editDispatcherFromViewBtn">
                        <i class="fas fa-edit"></i> Edit Dispatcher
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Function to show/hide API key
        function showApiKey(button, key) {
            const keyCell = button.closest('tr').querySelector('td:nth-child(3)');
            if (keyCell.textContent.includes('•')) {
                keyCell.textContent = key;
                button.querySelector('i').className = 'fas fa-eye-slash';
                button.setAttribute('data-tooltip', 'Hide');

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    keyCell.textContent = '•••••••••••••••••••' + key.slice(-5);
                    button.querySelector('i').className = 'fas fa-eye';
                    button.setAttribute('data-tooltip', 'View');
                }, 10000);
            } else {
                keyCell.textContent = '•••••••••••••••••••' + key.slice(-5);
                button.querySelector('i').className = 'fas fa-eye';
                button.setAttribute('data-tooltip', 'View');
            }
        }

        // Function to save API key to server (localStorage removed for security)
        function saveApiKey(service, name, key, isActive, expiresInDays = null) {
            if (!window.API || !window.API.settings) {
                console.error('API not available');
                return Promise.reject(new Error('API not available'));
            }

            // Map service to the appropriate server-side API key name
            const apiKeyMapping = {
                'google_maps': 'google_maps_api_key',
                'twilio': 'twilio_api_key',
                'mailgun': 'mailgun_config',
                'estated': 'estated_api_key',
                'openweather': 'openweather_api_key'
            };

            const serverKeyName = apiKeyMapping[service] || service;

            // Special handling for Mailgun - include domain in the request
            let requestData = { key: serverKeyName, value: key, description: name, isSecret: true, expiresInDays };

            if (service === 'mailgun') {
                const domainElement = document.getElementById('mailgunDomain');
                const domain = domainElement ? domainElement.value.trim() : '';

                if (!domain) {
                    return Promise.reject(new Error('Mailgun domain is required'));
                }

                requestData.mailgunDomain = domain;
            }

            // Save to server with enhanced security features
            const savePromise = window.API.settings.saveApiKey(requestData.key, requestData.value, requestData.description, requestData.isSecret, requestData.expiresInDays, requestData.mailgunDomain)
                .then(response => {
                    console.log('API key saved to server:', response);

                    // Update Google Maps if needed
                    if (service === 'google_maps' && window.updateGoogleMapsApiKey) {
                        window.updateGoogleMapsApiKey(key);
                    }

                    return response;
                })
                .catch(error => {
                    console.error('Error saving API key to server:', error);
                    throw error;
                });

            return savePromise;
        }

        // Function to save Twilio credentials (all three at once) - localStorage removed
        function saveTwilioCredentials(accountSid, authToken, fromNumber, isActive, expiresInDays = null) {
            if (!window.API || !window.API.settings) {
                return Promise.reject(new Error('API not available'));
            }

            // Save all three Twilio credentials to server with enhanced security
            const promises = [
                window.API.settings.saveApiKey('twilio_account_sid', accountSid, 'Twilio Account SID', isActive, expiresInDays),
                window.API.settings.saveApiKey('twilio_auth_token', authToken, 'Twilio Auth Token', isActive, expiresInDays),
                window.API.settings.saveApiKey('twilio_from_number', fromNumber, 'Twilio Phone Number', false, expiresInDays) // Phone number is not secret
            ];

            return Promise.all(promises);
        }

        // Function to migrate legacy Mailgun configuration to unified format
        async function migrateMailgunConfiguration() {
            if (!window.API || !window.API.settings) {
                throw new Error('API not available');
            }

            try {
                const response = await fetch('/api/settings/migrate-mailgun', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token')}`
                    },
                    credentials: 'include'
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `Migration failed: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Migration error:', error);
                throw error;
            }
        }

        // Function to load API keys into the table (server-side data only)
        function loadApiKeysTable() {
            const tableBody = document.getElementById('apiKeysTableBody');
            if (!tableBody) return;

            // Clear existing rows
            tableBody.innerHTML = '';

            // Show loading state
            tableBody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="7" style="text-align: center; padding: 20px; background-color: transparent !important; color: #f5f5f5;"><i class="fas fa-spinner fa-spin" style="color: #1e88e5;"></i> Loading API keys...</td></tr>';

            // Load API keys from server and session state
            if (!window.API || !window.API.settings) {
                tableBody.innerHTML = '<tr><td colspan="7"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h4>API Not Available</h4><p>Unable to load API keys. Please refresh the page.</p></div></td></tr>';
                return;
            }

            // Load API keys and session state (with graceful fallback for session state)
            Promise.allSettled([
                window.API.settings.getApiKeys(),
                window.API.settings.getSessionState()
            ])
                .then(([apiKeysResult, sessionStateResult]) => {
                    // Extract API keys (required)
                    if (apiKeysResult.status === 'rejected') {
                        console.error('Failed to load API keys:', apiKeysResult.reason);
                        tableBody.innerHTML = '<tr><td colspan="7"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h4>Error Loading API Keys</h4><p>Unable to load API keys from server. Please try refreshing the page.</p></div></td></tr>';
                        return;
                    }

                    const apiKeys = apiKeysResult.value;

                    // Extract session state (optional, graceful fallback)
                    let sessionState = {};
                    if (sessionStateResult.status === 'rejected') {
                        console.warn('Session state unavailable, continuing without it:', sessionStateResult.reason);
                    } else {
                        sessionState = sessionStateResult.value || {};
                    }
                    // Clear loading state
                    tableBody.innerHTML = '';

                    // Check if there are any keys
                    const hasKeys = apiKeys && apiKeys.length > 0;

                    // If no keys, show empty state
                    if (!hasKeys) {
                        const emptyRow = document.createElement('tr');
                        emptyRow.innerHTML = `
                            <td colspan="7">
                                <div class="empty-state">
                                    <i class="fas fa-key"></i>
                                    <h4>No API Keys Found</h4>
                                    <p>You haven't added any API keys yet. Add keys to enable features like address autocomplete.</p>
                                    <button class="btn btn-primary" id="emptyStateAddBtn">
                                        <i class="fas fa-plus"></i> Add Your First API Key
                                    </button>
                                </div>
                            </td>
                        `;
                        tableBody.appendChild(emptyRow);

                        // Add event listener to the empty state add button
                        setTimeout(() => {
                            const emptyStateAddBtn = document.getElementById('emptyStateAddBtn');
                            if (emptyStateAddBtn) {
                                emptyStateAddBtn.addEventListener('click', function() {
                                    showAddApiKeyModal();
                                });
                            }
                        }, 0);

                        return;
                    }

                    // Service display names map
                    const serviceNames = {
                        google_maps_api_key: 'Google Maps',
                        twilio_account_sid: 'Twilio Account SID',
                        twilio_auth_token: 'Twilio Auth Token',
                        twilio_from_number: 'Twilio Phone Number',
                        mailgun_config: 'Mailgun Email Service',
                        mailgun_api_key: 'Mailgun API Key (Legacy)',
                        mailgun_domain: 'Mailgun Domain (Legacy)',
                        estated_api_key: 'Estated API',
                        openweather_api_key: 'OpenWeather API'
                    };

                    // Service type mapping for edit modal (maps API key names to service types)
                    const serviceTypeMapping = {
                        google_maps_api_key: 'google_maps',
                        twilio_account_sid: 'twilio',
                        twilio_auth_token: 'twilio',
                        twilio_from_number: 'twilio',
                        mailgun_config: 'mailgun',
                        mailgun_api_key: 'mailgun',
                        mailgun_domain: 'mailgun',
                        estated_api_key: 'estated',
                        openweather_api_key: 'openweather'
                    };

                    // Check for legacy Mailgun entries that need migration
                    const hasLegacyMailgun = apiKeys.some(key => key.key === 'mailgun_api_key' || key.key === 'mailgun_domain');
                    const hasUnifiedMailgun = apiKeys.some(key => key.key === 'mailgun_config');
                    const migrateBtn = document.getElementById('migrateMailgunBtn');

                    if (hasLegacyMailgun && !hasUnifiedMailgun && migrateBtn) {
                        migrateBtn.style.display = 'block';
                    } else if (migrateBtn) {
                        migrateBtn.style.display = 'none';
                    }

                    // Add rows for each key
                    apiKeys.forEach(keyData => {
                        // Use 'key' property from backend, fallback to 'id' for compatibility
                        const keyId = keyData.key || keyData.id;
                        const displayService = serviceNames[keyId] || keyData.description || keyData.name || keyId;
                        const serviceType = serviceTypeMapping[keyId] || 'other';
                        const lastUsed = keyData.lastAccessedAt ? new Date(keyData.lastAccessedAt).toLocaleString() : 'Never';
                        const lastUpdated = keyData.updatedAt ? new Date(keyData.updatedAt).toLocaleString() : 'Unknown';

                        // Determine status based on expiration
                        let statusClass = 'active';
                        let statusText = 'Active';

                        if (keyData.expiresAt) {
                            const expirationDate = new Date(keyData.expiresAt);
                            const now = new Date();
                            const daysUntilExpiry = Math.ceil((expirationDate - now) / (1000 * 60 * 60 * 24));

                            if (expirationDate <= now) {
                                statusClass = 'expired';
                                statusText = 'Expired';
                            } else if (daysUntilExpiry <= 30) {
                                statusClass = 'warning';
                                statusText = 'Expiring Soon';
                            }
                        }

                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${displayService}</td>
                            <td>${keyData.description || 'No description'}</td>
                            <td>••••••••••••••••••• (Hidden for security)</td>
                            <td><span class="status-badge status-${statusClass}">${statusText}</span></td>
                            <td>${keyData.expiresAt ? new Date(keyData.expiresAt).toLocaleDateString() : 'No expiration'}</td>
                            <td>${lastUsed}</td>
                            <td>
                                <div class="incident-actions">
                                    <button class="btn-icon edit-api-key" data-tooltip="Edit"
                                        data-key="${keyId}"
                                        data-service="${serviceType}"
                                        data-name="${keyData.description || keyId}"
                                        data-expires="${keyData.expiresAt || ''}"
                                        data-status="${statusClass === 'active'}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    ${statusClass === 'warning' || statusClass === 'expired' ?
                                        `<button class="btn-icon extend-api-key" data-tooltip="Extend Expiration" data-key="${keyId}">
                                            <i class="fas fa-clock"></i>
                                        </button>` : ''
                                    }
                                    <button class="btn-icon delete-api-key" data-tooltip="Delete" data-key="${keyId}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        `;

                        tableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading API keys or session state:', error);
                    tableBody.innerHTML = '<tr><td colspan="7"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h4>Error Loading API Keys</h4><p>Unable to load API keys or session state. Please try refreshing the page.</p></div></td></tr>';
                });
        }

        // Function to save UI state to session (replaces localStorage)
        function saveUIStateToSession(key, state) {
            if (!window.API || !window.API.settings) {
                console.warn('API not available for session state saving');
                return Promise.resolve();
            }

            // Session state is automatically saved when API keys are updated
            // This function is for future UI state management if needed
            return Promise.resolve();
        }

        // Function to load UI state from session (replaces localStorage)
        function loadUIStateFromSession() {
            if (!window.API || !window.API.settings) {
                console.warn('API not available for session state loading');
                return Promise.resolve({});
            }

            return window.API.settings.getSessionState()
                .then(sessionState => {
                    console.log('Loaded session state:', sessionState);
                    return sessionState;
                })
                .catch(error => {
                    console.error('Error loading session state:', error);
                    return {};
                });
        }

        // Function to show/hide fields based on selected service
        function toggleServiceFields() {
            const service = document.getElementById('apiKeyService').value;
            const standardKeyNameGroup = document.getElementById('standardKeyNameGroup');
            const standardKeyValueGroup = document.getElementById('standardKeyValueGroup');
            const twilioFieldsGroup = document.getElementById('twilioFieldsGroup');
            const mailgunDomainGroup = document.getElementById('mailgunDomainGroup');

            // Hide all service-specific fields first
            twilioFieldsGroup.style.display = 'none';
            mailgunDomainGroup.style.display = 'none';

            if (service === 'twilio') {
                // Show Twilio fields, hide standard fields
                standardKeyNameGroup.style.display = 'none';
                standardKeyValueGroup.style.display = 'none';
                twilioFieldsGroup.style.display = 'block';

                // Clear standard fields
                document.getElementById('apiKeyName').value = '';
                document.getElementById('apiKeyValue').value = '';
            } else {
                // Show standard fields, hide Twilio fields
                standardKeyNameGroup.style.display = 'block';
                standardKeyValueGroup.style.display = 'block';
                twilioFieldsGroup.style.display = 'none';

                // Clear Twilio fields
                document.getElementById('twilioAccountSid').value = '';
                document.getElementById('twilioAuthToken').value = '';
                document.getElementById('twilioFromNumber').value = '';

                // Show Mailgun domain field if Mailgun is selected
                if (service === 'mailgun') {
                    mailgunDomainGroup.style.display = 'block';
                }
            }
        }

        // Function to show the add API key modal
        function showAddApiKeyModal() {
            const apiKeyModal = document.getElementById('apiKeyModal');
            if (!apiKeyModal) return;

            // Set modal to add mode
            document.getElementById('apiKeyMode').value = 'add';
            document.getElementById('apiKeyModalTitle').textContent = 'Add New API Key';
            document.getElementById('apiKeySaveButtonText').textContent = 'Save API Key';

            // Clear form
            document.getElementById('apiKeyService').value = '';
            document.getElementById('apiKeyName').value = '';
            document.getElementById('apiKeyValue').value = '';
            document.getElementById('apiKeyStatus').checked = true;

            // Clear Twilio fields
            document.getElementById('twilioAccountSid').value = '';
            document.getElementById('twilioAuthToken').value = '';
            document.getElementById('twilioFromNumber').value = '';

            // Clear Mailgun field
            document.getElementById('mailgunDomain').value = '';

            // Reset field visibility
            toggleServiceFields();

            // Show modal
            apiKeyModal.style.display = 'flex';
        }

        // Function to show the edit API key modal
        function showEditApiKeyModal(service, name, keyId, isActive) {
            const apiKeyModal = document.getElementById('apiKeyModal');
            if (!apiKeyModal) return;

            // Set modal to edit mode
            document.getElementById('apiKeyMode').value = 'edit';
            document.getElementById('apiKeyModalTitle').textContent = 'Edit API Key';
            document.getElementById('apiKeySaveButtonText').textContent = 'Update API Key';

            // Fill form with existing values (except API key value)
            document.getElementById('apiKeyService').value = service;
            document.getElementById('apiKeyName').value = name;
            document.getElementById('apiKeyStatus').checked = isActive !== false;

            // Show loading state for API key value
            const apiKeyValueField = document.getElementById('apiKeyValue');
            apiKeyValueField.value = 'Loading...';
            apiKeyValueField.disabled = true;

            // Store the key ID for later use
            document.getElementById('apiKeyModal').setAttribute('data-editing-key', keyId);

            // Fetch the actual API key value for editing
            if (window.API && window.API.settings) {
                window.API.settings.getApiKeyForEdit(keyId)
                    .then(keyData => {
                        // Populate the API key value field with the actual decrypted value
                        apiKeyValueField.value = keyData.value || '';
                        apiKeyValueField.disabled = false;

                        // Update other fields with server data if available
                        if (keyData.description) {
                            document.getElementById('apiKeyName').value = keyData.description;
                        }

                        // Special handling for unified Mailgun configuration
                        if (keyData.isMailgunConfig && keyData.mailgunDomain) {
                            document.getElementById('mailgunDomain').value = keyData.mailgunDomain;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading API key for editing:', error);
                        apiKeyValueField.value = '';
                        apiKeyValueField.disabled = false;
                        apiKeyValueField.placeholder = 'Error loading API key value. Please enter new value.';
                        showNotification('Error loading API key value. You can enter a new value.', 'warning');
                    });
            } else {
                // API not available
                apiKeyValueField.value = '';
                apiKeyValueField.disabled = false;
                apiKeyValueField.placeholder = 'API not available. Please enter API key value.';
                showNotification('API not available. Please enter the API key value manually.', 'warning');
            }

            // Show or hide Mailgun domain field based on selected service
            if (service === 'mailgun') {
                document.getElementById('mailgunDomainGroup').style.display = 'block';

                // Try to load existing Mailgun domain setting
                if (window.API && window.API.settings) {
                    window.API.settings.getSettings()
                        .then(settings => {
                            if (settings.mailgun_domain) {
                                document.getElementById('mailgunDomain').value = settings.mailgun_domain;
                            }
                        })
                        .catch(error => {
                            console.error("Error loading Mailgun domain:", error);
                        });
                }
            } else {
                document.getElementById('mailgunDomainGroup').style.display = 'none';
            }

            // Show modal
            apiKeyModal.style.display = 'flex';
        }

        // Function to load admin users
        function loadAdminUsers() {
            const tableBody = document.getElementById('adminUsersTableBody');
            if (!tableBody) return;

            // Show loading state with dark background to prevent flicker
            tableBody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="6" style="text-align: center; padding: 20px; background-color: transparent !important; color: #f5f5f5;"><i class="fas fa-spinner fa-spin" style="color: #1e88e5;"></i> Loading admin users...</td></tr>';

            // For now, show a placeholder message since we don't have a specific admin users endpoint
            // In a real implementation, this would call an API endpoint like /api/users?role=admin
            setTimeout(() => {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6">
                            <div class="empty-state">
                                <i class="fas fa-users-cog"></i>
                                <h4>Admin User Management</h4>
                                <p>Admin user management functionality requires additional API endpoints to be implemented.</p>
                                <p>Current user management is handled through the company management system.</p>
                            </div>
                        </td>
                    </tr>
                `;
            }, 1000);
        }

        // Function to load system settings
        function loadSystemSettings() {
            const loadingDiv = document.getElementById('systemSettingsLoading');
            const formDiv = document.getElementById('systemSettingsForm');

            if (!loadingDiv || !formDiv) return;

            // Show loading state
            loadingDiv.style.display = 'block';
            formDiv.style.display = 'none';

            // Load settings from API
            if (window.API && window.API.settings) {
                window.API.settings.getSettings()
                    .then(settings => {
                        // Populate form fields with API data
                        document.getElementById('systemName').value = settings.system_name || 'FireAlerts911';
                        document.getElementById('systemTimezone').value = settings.timezone || 'America/Los_Angeles';
                        document.getElementById('defaultEmail').value = settings.default_email || '<EMAIL>';
                        document.getElementById('smsProvider').value = settings.sms_provider || 'twilio';
                        document.getElementById('throttleNotifications').checked = settings.throttle_notifications !== 'false';
                        document.getElementById('sessionTimeout').value = settings.session_timeout || '30';
                        document.getElementById('passwordExpiry').value = settings.password_expiry || '90';
                        document.getElementById('force2FA').checked = settings.force_2fa !== 'false';

                        // Hide loading and show form
                        loadingDiv.style.display = 'none';
                        formDiv.style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error loading system settings:', error);

                        // Show error state
                        loadingDiv.innerHTML = `
                            <div class="empty-state">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h4>Error Loading Settings</h4>
                                <p>Unable to load system settings. Please try refreshing the page.</p>
                                <button class="btn btn-primary" onclick="loadSystemSettings()">
                                    <i class="fas fa-redo"></i> Retry
                                </button>
                            </div>
                        `;
                    });
            } else {
                // API not available - show default values
                console.warn('Settings API not available, using default values');

                // Set default values
                document.getElementById('systemName').value = 'FireAlerts911';
                document.getElementById('systemTimezone').value = 'America/Los_Angeles';
                document.getElementById('defaultEmail').value = '<EMAIL>';
                document.getElementById('smsProvider').value = 'twilio';
                document.getElementById('throttleNotifications').checked = true;
                document.getElementById('sessionTimeout').value = '30';
                document.getElementById('passwordExpiry').value = '90';
                document.getElementById('force2FA').checked = true;

                // Hide loading and show form
                loadingDiv.style.display = 'none';
                formDiv.style.display = 'block';
            }
        }

        // Function to save system settings
        function saveSystemSettings(formData) {
            if (!window.API || !window.API.settings) {
                console.error('Settings API not available');
                return Promise.reject(new Error('Settings API not available'));
            }

            // Convert form data to settings object
            const settings = {
                system_name: formData.get('system_name'),
                timezone: formData.get('timezone'),
                default_email: formData.get('default_email'),
                sms_provider: formData.get('sms_provider'),
                throttle_notifications: formData.get('throttle_notifications') ? 'true' : 'false',
                session_timeout: formData.get('session_timeout'),
                password_expiry: formData.get('password_expiry'),
                force_2fa: formData.get('force_2fa') ? 'true' : 'false'
            };

            // Save each setting individually (since the API expects individual key-value pairs)
            const savePromises = Object.entries(settings).map(([key, value]) => {
                return window.API.settings.saveApiKey(key, value, `System setting: ${key}`, false);
            });

            return Promise.all(savePromises);
        }

        // Function to load dashboard statistics
        function loadDashboardStats() {
            // Check authentication using the auth-check system (which handles both tokens and cookies)
            if (window.AuthCheck && !window.AuthCheck.isAuthenticated()) {
                // Try to validate with server (which checks cookies)
                window.AuthCheck.validateAuthWithServer(true)
                    .then(() => {
                        // Authentication successful, proceed with loading stats
                        loadStatsData();
                    })
                    .catch(() => {
                        // Authentication failed, redirect to login
                        console.log('Authentication failed - redirecting to login');
                        window.AuthCheck.redirectToLogin('auth_required');
                    });
                return;
            }

            // User is authenticated, load the stats
            loadStatsData();
        }

        // Function to actually load the statistics data
        function loadStatsData() {

            // Load subscriber stats (now using companies API)
            if (window.API && window.API.companies) {
                window.API.companies.getStats()
                    .then(stats => {
                        const totalElement = document.getElementById('totalSubscribersCount');
                        if (totalElement) {
                            totalElement.textContent = stats.totalCount || 0;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading subscriber stats:', error);
                        const totalElement = document.getElementById('totalSubscribersCount');
                        if (totalElement) {
                            totalElement.textContent = '0';
                        }
                    });
            } else {
                console.error('Companies API not available');
                const totalElement = document.getElementById('totalSubscribersCount');
                if (totalElement) {
                    totalElement.textContent = '0';
                }
            }

            // Load incident stats
            if (window.API && window.API.incidents) {
                window.API.incidents.getCounts()
                    .then(counts => {
                        const incidentsElement = document.getElementById('activeIncidentsCount');
                        if (incidentsElement) {
                            incidentsElement.textContent = counts.total || 0;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading incident stats:', error);
                        const incidentsElement = document.getElementById('activeIncidentsCount');
                        if (incidentsElement) {
                            incidentsElement.textContent = '0';
                        }
                    });
            } else {
                console.error('Incidents API not available');
                const incidentsElement = document.getElementById('activeIncidentsCount');
                if (incidentsElement) {
                    incidentsElement.textContent = '0';
                }
            }

            // Load user stats
            if (window.API && window.API.users) {
                window.API.users.getStats()
                    .then(stats => {
                        const usersElement = document.getElementById('totalUsersCount');
                        if (usersElement) {
                            usersElement.textContent = stats.totalCount || 0;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading user stats:', error);
                        const usersElement = document.getElementById('totalUsersCount');
                        if (usersElement) {
                            usersElement.textContent = '0';
                        }
                    });
            } else {
                console.error('Users API not available');
                const usersElement = document.getElementById('totalUsersCount');
                if (usersElement) {
                    usersElement.textContent = '0';
                }
            }

            // Load notification stats
            if (window.API && window.API.notifications) {
                window.API.notifications.getStats()
                    .then(stats => {
                        const notificationsElement = document.getElementById('notificationsSentCount');
                        if (notificationsElement) {
                            // Calculate total sent notifications (sent + delivered)
                            const sentCount = (stats.byStatus?.sent || 0) + (stats.byStatus?.delivered || 0);
                            notificationsElement.textContent = sentCount > 999 ? `${(sentCount / 1000).toFixed(1)}k` : sentCount;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading notification stats:', error);
                        const notificationsElement = document.getElementById('notificationsSentCount');
                        if (notificationsElement) {
                            notificationsElement.textContent = '0';
                        }
                    });
            } else {
                console.error('Notifications API not available');
                const notificationsElement = document.getElementById('notificationsSentCount');
                if (notificationsElement) {
                    notificationsElement.textContent = '0';
                }
            }
        }

        // Function to load and update user profile
        function loadUserProfile() {
            if (window.API && window.API.account) {
                window.API.account.getProfile()
                    .then(response => {
                        if (response && response.success !== false) {
                            const userData = response.data || response;
                            // Update avatar using shared utility
                            if (window.FireAlertsUtils && window.FireAlertsUtils.avatar) {
                                window.FireAlertsUtils.avatar.updateUserAvatar(userData);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error loading user profile:', error);
                        // Keep default avatar if profile loading fails
                    });
            }
        }

        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Load user profile for avatar
            loadUserProfile();

            // Load dashboard statistics
            loadDashboardStats();

            // Set today's date as default for log filter
            const logDateFilter = document.getElementById('logDateFilter');
            if (logDateFilter) {
                const today = new Date().toISOString().split('T')[0];
                logDateFilter.value = today;
            }

            // Add event listener for API service selection change
            document.getElementById('apiKeyService').addEventListener('change', function() {
                const selectedService = this.value;
                const mailgunDomainGroup = document.getElementById('mailgunDomainGroup');

                // Show Mailgun domain field only when Mailgun is selected
                if (selectedService === 'mailgun') {
                    mailgunDomainGroup.style.display = 'block';

                    // Try to load existing Mailgun domain setting if editing
                    if (document.getElementById('apiKeyMode').value === 'edit' && window.API && window.API.settings) {
                        window.API.settings.getSettings()
                            .then(settings => {
                                if (settings.mailgun_domain) {
                                    document.getElementById('mailgunDomain').value = settings.mailgun_domain;
                                }
                            })
                            .catch(error => {
                                console.error("Error loading Mailgun domain:", error);
                            });
                    }
                } else {
                    mailgunDomainGroup.style.display = 'none';
                }
            });

            // Use shared sidebar rendering utility
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('admin-panel');
            } else {
                // Fallback if shared utils not loaded
                console.error('FireAlertsUtils.renderRoleBasedSidebar not available');
                if (typeof renderRoleBasedSidebar === 'function') {
                    renderRoleBasedSidebar();
                }
            }

            // Tab switching functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    tabButtons.forEach(btn => btn.classList.remove('active'));

                    // Hide all tab contents
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Show corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');

                    // Load content based on active tab
                    if (tabId === 'api') {
                        loadApiKeysTable();
                    } else if (tabId === 'users') {
                        loadAdminUsers();
                    } else if (tabId === 'system') {
                        loadSystemSettings();
                    }
                });
            });

            // API Key modal functionality
            const apiKeyModal = document.getElementById('apiKeyModal');
            const addApiKeyBtn = document.getElementById('addApiKeyBtn');
            const apiKeyForm = document.getElementById('apiKeyForm');
            const cancelButtons = document.querySelectorAll('.close-modal, .cancel-modal');

            // Add New API Key button
            if (addApiKeyBtn) {
                addApiKeyBtn.addEventListener('click', function() {
                    showAddApiKeyModal();
                });
            }

            // Migrate Mailgun button
            const migrateMailgunBtn = document.getElementById('migrateMailgunBtn');
            if (migrateMailgunBtn) {
                migrateMailgunBtn.addEventListener('click', async function() {
                    if (confirm('This will migrate your separate Mailgun API key and domain entries into a single unified configuration. This action cannot be undone. Continue?')) {
                        try {
                            migrateMailgunBtn.disabled = true;
                            migrateMailgunBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Migrating...';

                            const result = await migrateMailgunConfiguration();

                            if (result.migrated) {
                                showNotification('Mailgun configuration migrated successfully to unified format', 'success');
                            } else {
                                showNotification(result.message, 'info');
                            }

                            // Reload the table to show updated configuration
                            loadApiKeysTable();
                        } catch (error) {
                            console.error('Migration failed:', error);
                            showNotification('Migration failed: ' + error.message, 'error');
                        } finally {
                            migrateMailgunBtn.disabled = false;
                            migrateMailgunBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Migrate Mailgun';
                        }
                    }
                });
            }

            // Edit and Delete API key buttons (using event delegation)
            document.addEventListener('click', function(e) {
                // Edit API key
                if (e.target.closest('.edit-api-key')) {
                    const button = e.target.closest('.edit-api-key');
                    const service = button.getAttribute('data-service');
                    const name = button.getAttribute('data-name');
                    const key = button.getAttribute('data-key');
                    const isActive = button.getAttribute('data-status') !== 'false';

                    showEditApiKeyModal(service, name, key, isActive);
                }

                // Delete API key
                if (e.target.closest('.delete-api-key')) {
                    const button = e.target.closest('.delete-api-key');
                    const keyId = button.getAttribute('data-key');

                    if (confirm('Are you sure you want to delete this API key?')) {
                        // Delete via server API (localStorage removed for security)
                        if (window.API && window.API.settings) {
                            window.API.settings.deleteApiKey(keyId)
                                .then(() => {
                                    // Reload the table
                                    loadApiKeysTable();

                                    // Show notification
                                    showNotification('API key deleted successfully', 'success');
                                })
                                .catch(error => {
                                    console.error('Error deleting API key:', error);
                                    showNotification('Error deleting API key. Please try again.', 'error');
                                });
                        } else {
                            showNotification('API not available. Please refresh the page.', 'error');
                        }
                    }
                }
            });

            // Close modal
            cancelButtons.forEach(button => {
                button.addEventListener('click', function() {
                    apiKeyModal.style.display = 'none';
                });
            });

            // Close modal when clicking outside content
            window.addEventListener('click', function(e) {
                if (e.target === apiKeyModal) {
                    apiKeyModal.style.display = 'none';
                }
            });

            // Service selection change handler
            const apiKeyServiceSelect = document.getElementById('apiKeyService');
            if (apiKeyServiceSelect) {
                apiKeyServiceSelect.addEventListener('change', toggleServiceFields);
            }

            // API Key form submission
            if (apiKeyForm) {
                apiKeyForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const mode = document.getElementById('apiKeyMode').value;
                    const service = document.getElementById('apiKeyService').value;
                    const isActive = document.getElementById('apiKeyStatus').checked;

                    if (service === 'twilio') {
                        // Handle Twilio credentials
                        const accountSid = document.getElementById('twilioAccountSid').value.trim();
                        const authToken = document.getElementById('twilioAuthToken').value.trim();
                        const fromNumber = document.getElementById('twilioFromNumber').value.trim();

                        // Validate Twilio fields
                        if (!accountSid || !authToken || !fromNumber) {
                            alert('Please fill in all Twilio fields');
                            return;
                        }

                        if (!accountSid.startsWith('AC')) {
                            alert('Twilio Account SID should start with "AC"');
                            return;
                        }

                        if (!fromNumber.startsWith('+')) {
                            alert('Phone number must be in E.164 format (start with +)');
                            return;
                        }

                        // Save Twilio credentials
                        saveTwilioCredentials(accountSid, authToken, fromNumber, isActive)
                            .then(() => {
                                // Show success notification
                                showNotification(`Twilio SMS configuration ${mode === 'add' ? 'added' : 'updated'} successfully`, 'success');

                                // Close modal
                                apiKeyModal.style.display = 'none';

                                // Reload the table
                                loadApiKeysTable();
                            })
                            .catch(error => {
                                console.error('Error saving Twilio credentials:', error);
                                alert('Error saving Twilio credentials. Please try again.');
                            });
                    } else {
                        // Handle standard API keys
                        const name = document.getElementById('apiKeyName').value.trim();
                        const key = document.getElementById('apiKeyValue').value.trim();

                        if (!name || !key) {
                            alert('Please fill in all required fields');
                            return;
                        }

                        // Save the API key
                        saveApiKey(service, name, key, isActive)
                            .then((response) => {
                                // Show success notification with custom message if available
                                const message = response?.message || `API key ${mode === 'add' ? 'added' : 'updated'} successfully`;
                                showNotification(message, 'success');

                                // Show additional warning if Mailgun domain failed
                                if (response?.mailgunDomainError) {
                                    setTimeout(() => {
                                        showNotification(`Warning: ${response.mailgunDomainError}`, 'warning');
                                    }, 2000);
                                }

                                // Close modal
                                apiKeyModal.style.display = 'none';

                                // Reload the table
                                loadApiKeysTable();
                            })
                            .catch(error => {
                                console.error('Error saving API key:', error);
                                showNotification('Error saving API key. Please try again.', 'error');
                            });
                    }
                });
            }

            // Load content for initially active tab
            const activeTabButton = document.querySelector('.tab-button.active');
            if (activeTabButton) {
                const activeTabId = activeTabButton.getAttribute('data-tab');
                if (activeTabId === 'api') {
                    loadApiKeysTable();
                } else if (activeTabId === 'users') {
                    loadDispatchers();
                } else if (activeTabId === 'system') {
                    loadSystemSettings();
                } else if (activeTabId === 'logs') {
                    loadSystemLogs();
                }
            }

            // System Settings form submission
            const systemSettingsForm = document.getElementById('systemSettingsForm');
            if (systemSettingsForm) {
                systemSettingsForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalText = submitButton.innerHTML;

                    // Show loading state
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    submitButton.disabled = true;

                    saveSystemSettings(formData)
                        .then(() => {
                            // Show success message
                            if (typeof window.modernDispatchShowNotification === 'function') {
                                window.modernDispatchShowNotification('System settings saved successfully!', 'success');
                            } else {
                                alert('System settings saved successfully!');
                            }
                        })
                        .catch(error => {
                            console.error('Error saving system settings:', error);
                            // Show error message
                            if (typeof window.modernDispatchShowNotification === 'function') {
                                window.modernDispatchShowNotification('Error saving system settings. Please try again.', 'error');
                            } else {
                                alert('Error saving system settings. Please try again.');
                            }
                        })
                        .finally(() => {
                            // Restore button state
                            submitButton.innerHTML = originalText;
                            submitButton.disabled = false;
                        });
                });
            }
        });
    </script>
</body>
</html>
