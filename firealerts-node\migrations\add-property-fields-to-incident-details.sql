-- Migration to add property owner and Estated API fields to incident_details table
-- This migration adds fields needed to store data from the Estated API

USE firealerts911;

-- Add new columns to incident_details table for property owner information
-- Note: Using individual ALTER statements to handle existing columns gracefully

-- Check and add secondary_owner column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'firealerts911'
     AND TABLE_NAME = 'incident_details'
     AND COLUMN_NAME = 'secondary_owner') = 0,
    'ALTER TABLE incident_details ADD COLUMN secondary_owner VARCHAR(255) COMMENT ''Secondary property owner name''',
    'SELECT ''Column secondary_owner already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add owner_address column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'firealerts911'
     AND TABLE_NAME = 'incident_details'
     AND COLUMN_NAME = 'owner_address') = 0,
    'ALTER TABLE incident_details ADD COLUMN owner_address TEXT COMMENT ''Property owner mailing address''',
    'SELECT ''Column owner_address already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add owner_phone column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'firealerts911'
     AND TABLE_NAME = 'incident_details'
     AND COLUMN_NAME = 'owner_phone') = 0,
    'ALTER TABLE incident_details ADD COLUMN owner_phone VARCHAR(20) COMMENT ''Property owner phone number''',
    'SELECT ''Column owner_phone already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add owner_email column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'firealerts911'
     AND TABLE_NAME = 'incident_details'
     AND COLUMN_NAME = 'owner_email') = 0,
    'ALTER TABLE incident_details ADD COLUMN owner_email VARCHAR(255) COMMENT ''Property owner email address''',
    'SELECT ''Column owner_email already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add property_value column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'firealerts911'
     AND TABLE_NAME = 'incident_details'
     AND COLUMN_NAME = 'property_value') = 0,
    'ALTER TABLE incident_details ADD COLUMN property_value DECIMAL(12,2) COMMENT ''Property valuation from Estated API''',
    'SELECT ''Column property_value already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for commonly queried fields
CREATE INDEX IF NOT EXISTS idx_property_value ON incident_details(property_value);
CREATE INDEX IF NOT EXISTS idx_dwelling_type ON incident_details(dwelling_type);
CREATE INDEX IF NOT EXISTS idx_year_built ON incident_details(year_built);

-- Update any existing records to have default values where appropriate
UPDATE incident_details
SET dwelling_type = 'Unknown'
WHERE dwelling_type IS NULL AND property_owner IS NOT NULL;

-- Add comments to existing columns for clarity
ALTER TABLE incident_details
MODIFY COLUMN property_owner VARCHAR(255) COMMENT 'Primary property owner name',
MODIFY COLUMN owner_contact VARCHAR(100) COMMENT 'Property owner contact information';

-- Display the updated table structure
DESCRIBE incident_details;
