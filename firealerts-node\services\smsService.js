const twilio = require('twilio');
const db = require('../models');

/**
 * SMS Service for FireAlerts911
 * Handles SMS notifications using Twilio with API key management integration
 */
class SmsService {
    constructor() {
        this.twilioClient = null;
        this.initialized = false;
        this.fromNumber = null;
        this.deliveryStats = {
            sent: 0,
            failed: 0,
            lastError: null,
            lastSuccess: null
        };
    }

    /**
     * Initialize the SMS service with Twilio credentials from the database
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            console.log('Initializing SMS service...');

            // Get Twilio credentials from the database
            const twilioAccountSid = await db.systemSetting.getValue('twilio_account_sid');
            const twilioAuthToken = await db.systemSetting.getValue('twilio_auth_token');
            const twilioFromNumber = await db.systemSetting.getValue('twilio_from_number');

            // Check if all required credentials are available
            if (!twilioAccountSid || !twilioAuthToken || !twilioFromNumber) {
                console.warn('Twilio credentials not fully configured. SMS notifications will be disabled.');
                console.warn('Missing:', {
                    accountSid: !twilioAccountSid,
                    authToken: !twilioAuthToken,
                    fromNumber: !twilioFromNumber
                });
                return;
            }

            // Initialize Twilio client
            this.twilioClient = twilio(twilioAccountSid, twilioAuthToken);
            this.fromNumber = twilioFromNumber;
            this.initialized = true;

            console.log('✅ SMS service initialized successfully with Twilio');
            console.log(`📱 SMS will be sent from: ${this.fromNumber}`);

            // Test the connection
            await this.testConnection();

        } catch (error) {
            console.error('❌ Error initializing SMS service:', error);
            this.deliveryStats.lastError = error.message;
            throw error;
        }
    }

    /**
     * Test the Twilio connection
     */
    async testConnection() {
        if (!this.twilioClient) {
            throw new Error('Twilio client not initialized');
        }

        try {
            // Validate the phone number format
            const phoneNumber = await this.twilioClient.lookups.v1.phoneNumbers(this.fromNumber).fetch();
            console.log('✅ Twilio connection test successful');
            console.log(`📱 From number validated: ${phoneNumber.phoneNumber} (${phoneNumber.countryCode})`);
            return true;
        } catch (error) {
            console.error('❌ Twilio connection test failed:', error);
            throw new Error(`Twilio connection test failed: ${error.message}`);
        }
    }

    /**
     * Send an SMS message
     * @param {string} to - Recipient phone number (E.164 format recommended)
     * @param {string} message - Message content
     * @param {Object} options - Additional options
     * @returns {Object} Result object with success status and message details
     */
    async sendSms(to, message, options = {}) {
        await this.initialize();

        if (!this.twilioClient) {
            throw new Error('SMS service not available - Twilio not configured');
        }

        try {
            // Validate and format phone number
            const formattedNumber = this.formatPhoneNumber(to);
            
            console.log(`📱 Sending SMS to ${formattedNumber}: ${message.substring(0, 50)}...`);

            // Send SMS via Twilio
            const twilioMessage = await this.twilioClient.messages.create({
                body: message,
                from: this.fromNumber,
                to: formattedNumber,
                ...options
            });

            // Update statistics
            this.deliveryStats.sent++;
            this.deliveryStats.lastSuccess = new Date().toISOString();

            console.log(`✅ SMS sent successfully via Twilio: ${twilioMessage.sid}`);

            return {
                success: true,
                messageId: twilioMessage.sid,
                provider: 'twilio',
                to: formattedNumber,
                status: twilioMessage.status,
                cost: twilioMessage.price || null,
                direction: twilioMessage.direction
            };

        } catch (error) {
            // Update statistics
            this.deliveryStats.failed++;
            this.deliveryStats.lastError = error.message;

            console.error(`❌ Failed to send SMS to ${to}:`, error);

            // Handle specific Twilio errors
            if (error.code === 21211) {
                throw new Error('Invalid phone number format');
            } else if (error.code === 21408) {
                throw new Error('Permission denied - check Twilio account permissions');
            } else if (error.code === 21610) {
                throw new Error('Message blocked - recipient has opted out');
            }

            throw new Error(`SMS delivery failed: ${error.message}`);
        }
    }

    /**
     * Format phone number to E.164 format
     * @param {string} phoneNumber - Raw phone number
     * @returns {string} Formatted phone number
     */
    formatPhoneNumber(phoneNumber) {
        if (!phoneNumber) {
            throw new Error('Phone number is required');
        }

        // Remove all non-digit characters
        let cleaned = phoneNumber.replace(/\D/g, '');

        // Add country code if missing (assume US +1)
        if (cleaned.length === 10) {
            cleaned = '1' + cleaned;
        }

        // Add + prefix for E.164 format
        if (!cleaned.startsWith('+')) {
            cleaned = '+' + cleaned;
        }

        // Validate length (E.164 format should be 10-15 digits after +)
        if (cleaned.length < 11 || cleaned.length > 16) {
            throw new Error('Invalid phone number length');
        }

        return cleaned;
    }

    /**
     * Send a test SMS message
     * @param {string} to - Test recipient phone number
     * @returns {Object} Test result
     */
    async sendTestSms(to) {
        const testMessage = `🚨 FireAlerts911 Test SMS - ${new Date().toLocaleString()}. If you received this, SMS notifications are working correctly!`;
        
        try {
            const result = await this.sendSms(to, testMessage);
            
            // Log the test
            await db.activity.create({
                action: 'test_sms_sent',
                details: JSON.stringify({ 
                    to: to,
                    messageId: result.messageId,
                    provider: result.provider
                }),
                module: 'sms',
                severity: 'info'
            });

            return {
                success: true,
                message: 'Test SMS sent successfully',
                details: result
            };
        } catch (error) {
            // Log the failed test
            await db.activity.create({
                action: 'test_sms_failed',
                details: JSON.stringify({ 
                    to: to,
                    error: error.message
                }),
                module: 'sms',
                severity: 'error'
            });

            return {
                success: false,
                message: 'Test SMS failed',
                error: error.message
            };
        }
    }

    /**
     * Get SMS delivery statistics
     * @returns {Object} Delivery statistics
     */
    getStats() {
        return {
            ...this.deliveryStats,
            isConfigured: this.initialized,
            provider: 'twilio',
            fromNumber: this.fromNumber
        };
    }

    /**
     * Check if SMS service is properly configured
     * @returns {boolean} Configuration status
     */
    isConfigured() {
        return this.initialized && this.twilioClient !== null;
    }
}

// Export singleton instance
module.exports = new SmsService();
