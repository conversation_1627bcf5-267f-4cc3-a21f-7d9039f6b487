/**
 * Comprehensive Location Data Seeding Script
 *
 * This script populates ALL US states and counties into the database.
 * It loads all 50 US states + DC and approximately 3,100+ counties.
 *
 * Features:
 * - Complete US coverage (all states and counties)
 * - Batch processing for performance
 * - Progress tracking
 * - Error handling and validation
 * - Transaction safety
 * - Duplicate prevention
 *
 * Usage:
 * - npm run seed-complete-locations (full seeding)
 * - node scripts/seed-complete-locations.js --states-only
 * - node scripts/seed-complete-locations.js --counties-only
 * - node scripts/seed-complete-locations.js --clear-first
 */

const { Sequelize, DataTypes } = require('sequelize');
const fs = require('fs');
const path = require('path');
const https = require('https');

// Load environment variables
require('dotenv').config();

// Import table creation utility
const { ensureTablesExist } = require('./utils/table-creation');

// Configuration
const config = {
  dialect: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'dispatchuser',
  password: process.env.DB_PASSWORD || 'dispatchpassword',
  database: process.env.DB_NAME || 'firealerts911',
  logging: false,
  dialectOptions: {
    ssl: process.env.DB_SSL === 'true' ? {
      require: true,
      rejectUnauthorized: false
    } : false
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
};

// Data source URLs
const DATA_SOURCES = {
  states: 'https://gist.githubusercontent.com/mshafrir/2646763/raw/states_titlecase.json',
  counties: 'https://gist.githubusercontent.com/vitalii-z8i/bbb96d55d57f1e4342c3408e7286d3f2/raw/counties_list.json'
};

// Processing configuration
const BATCH_SIZE = 500;
const PROGRESS_INTERVAL = 100;

// Command line arguments
const args = process.argv.slice(2);
const options = {
  statesOnly: args.includes('--states-only'),
  countiesOnly: args.includes('--counties-only'),
  clearFirst: args.includes('--clear-first'),
  includeTerritoriesFlag: args.includes('--include-territories'),
  verbose: args.includes('--verbose') || args.includes('-v')
};

// Check if we're in Docker environment
function isInDocker() {
  try {
    return fs.existsSync('/.dockerenv');
  } catch (err) {
    return false;
  }
}

// Fetch data from URL
function fetchData(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse JSON from ${url}: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Failed to fetch data from ${url}: ${error.message}`));
    });
  });
}

// Progress tracking utility
function showProgress(current, total, operation) {
  if (current % PROGRESS_INTERVAL === 0 || current === total) {
    const percentage = Math.round((current / total) * 100);
    const filledBars = Math.max(0, Math.min(20, Math.floor(percentage / 5)));
    const emptyBars = Math.max(0, 20 - filledBars);
    const progressBar = '█'.repeat(filledBars) + '░'.repeat(emptyBars);
    process.stdout.write(`\r${operation}: [${progressBar}] ${percentage}% (${current}/${total})`);
    if (current === total) {
      console.log(''); // New line when complete
    }
  }
}

// Filter states to exclude territories if needed
function filterStates(states, includeTerritoriesFlag) {
  const mainStates = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FL',
    'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME',
    'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH',
    'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI',
    'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
  ];

  if (includeTerritoriesFlag) {
    return states; // Include all states and territories
  }

  return states.filter(state => mainStates.includes(state.abbreviation));
}

// Validate and clean county data
function validateCountyData(counties, stateMap) {
  const validCounties = [];
  const errors = [];

  for (let i = 0; i < counties.length; i++) {
    const county = counties[i];

    if (!county.County || !county.State) {
      errors.push(`Row ${i + 1}: Missing county name or state`);
      continue;
    }

    const stateInfo = stateMap[county.State];
    if (!stateInfo) {
      errors.push(`Row ${i + 1}: Unknown state "${county.State}" for county "${county.County}"`);
      continue;
    }

    // Standardize county name with proper suffix
    let countyName = county.County.trim();

    // Determine the appropriate suffix based on state
    let suffix = 'County';
    if (stateInfo.abbreviation === 'LA') {
      suffix = 'Parish';
    } else if (stateInfo.abbreviation === 'AK') {
      // Alaska has mixed naming - some are boroughs, some are census areas
      if (countyName.toLowerCase().includes('borough') ||
          countyName.toLowerCase().includes('census area') ||
          countyName.toLowerCase().includes('municipality')) {
        // Keep existing suffix for Alaska special cases
        suffix = '';
      } else {
        suffix = 'Borough';
      }
    }

    // Remove existing suffix if present to avoid duplication
    const suffixesToRemove = ['County', 'Parish', 'Borough', 'City and Borough', 'Municipality', 'Census Area'];
    for (const suffixToRemove of suffixesToRemove) {
      const regex = new RegExp(`\\s+${suffixToRemove}$`, 'i');
      if (regex.test(countyName)) {
        countyName = countyName.replace(regex, '').trim();
        break;
      }
    }

    // Add the appropriate suffix (unless it's a special case with no suffix)
    if (suffix) {
      countyName = `${countyName} ${suffix}`;
    }

    validCounties.push({
      name: countyName,
      state_id: stateInfo.id,
      state: stateInfo.abbreviation,
      is_active: true
    });
  }

  // Add special handling for states without counties in the dataset
  const statesWithCounties = new Set(validCounties.map(c => c.state));
  const allStates = Object.values(stateMap).filter(s => s.abbreviation);

  for (const state of allStates) {
    if (!statesWithCounties.has(state.abbreviation)) {
      // Add a default county for states without county data with proper suffix
      let defaultCountyName;
      if (state.abbreviation === 'DC') {
        defaultCountyName = 'District of Columbia';
      } else if (state.abbreviation === 'LA') {
        defaultCountyName = `${state.abbreviation} Parish`;
      } else if (state.abbreviation === 'AK') {
        defaultCountyName = `${state.abbreviation} Borough`;
      } else {
        defaultCountyName = `${state.abbreviation} County`;
      }

      validCounties.push({
        name: defaultCountyName,
        state_id: state.id,
        state: state.abbreviation,
        is_active: true
      });

      if (options.verbose) {
        console.log(`ℹ️ Added default county "${defaultCountyName}" for ${state.abbreviation}`);
      }
    }
  }

  if (errors.length > 0 && options.verbose) {
    console.log('\n⚠️ Data validation warnings:');
    errors.slice(0, 10).forEach(error => console.log(`  ${error}`));
    if (errors.length > 10) {
      console.log(`  ... and ${errors.length - 10} more warnings`);
    }
  }

  return validCounties;
}

// Process data in batches
async function processBatches(data, batchSize, processor, operation) {
  const batches = [];
  for (let i = 0; i < data.length; i += batchSize) {
    batches.push(data.slice(i, i + batchSize));
  }

  console.log(`📦 Processing ${data.length} records in ${batches.length} batches of ${batchSize}`);

  for (let i = 0; i < batches.length; i++) {
    const currentCount = Math.min((i + 1) * batchSize, data.length);
    showProgress(currentCount, data.length, operation);
    await processor(batches[i], i);
  }

  return batches.length;
}

// Clear existing location data
async function clearLocationData(sequelize, State, County) {
  console.log('🗑️ Clearing existing location data...');

  const transaction = await sequelize.transaction();
  try {
    await County.destroy({ where: {}, transaction });
    await State.destroy({ where: {}, transaction });
    await transaction.commit();
    console.log('✅ Existing location data cleared');
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// Seed states data
async function seedStates(sequelize, State) {
  console.log('🌱 Fetching states data...');

  try {
    const statesData = await fetchData(DATA_SOURCES.states);
    console.log(`📥 Fetched ${statesData.length} states from data source`);

    // Filter states based on options
    const filteredStates = filterStates(statesData, options.includeTerritoriesFlag);
    console.log(`🔍 Processing ${filteredStates.length} states (territories ${options.includeTerritoriesFlag ? 'included' : 'excluded'})`);

    // Check if states already exist
    const existingStateCount = await State.count();
    if (existingStateCount > 0 && !options.clearFirst) {
      console.log(`✅ States already exist (${existingStateCount} records). Use --clear-first to reseed.`);
      return filteredStates;
    }

    // Prepare states data for insertion
    const statesToInsert = filteredStates.map((state, index) => ({
      id: index + 1,
      name: state.name,
      abbreviation: state.abbreviation,
      is_active: true
    }));

    // Insert states in transaction
    const transaction = await sequelize.transaction();
    try {
      await State.bulkCreate(statesToInsert, {
        transaction,
        ignoreDuplicates: true,
        updateOnDuplicate: ['name', 'is_active']
      });
      await transaction.commit();
      console.log(`✅ Successfully seeded ${statesToInsert.length} states`);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return filteredStates;
  } catch (error) {
    console.error('❌ Error seeding states:', error.message);
    throw error;
  }
}

// Seed counties data
async function seedCounties(sequelize, State, County) {
  console.log('🌱 Fetching counties data...');

  try {
    // Get state mapping for county processing
    const states = await State.findAll({
      attributes: ['id', 'name', 'abbreviation']
    });

    const stateMap = {};
    states.forEach(state => {
      stateMap[state.name] = { id: state.id, abbreviation: state.abbreviation };
      stateMap[state.abbreviation] = { id: state.id, abbreviation: state.abbreviation };
    });

    // Add special mappings for data source inconsistencies
    stateMap['District of Columbia'] = stateMap['DC'] || stateMap['District Of Columbia'];
    stateMap['Hawai?i'] = stateMap['HI'] || stateMap['Hawaii'];

    console.log(`📋 Loaded ${states.length} states for county mapping`);

    // Fetch counties data
    const countiesData = await fetchData(DATA_SOURCES.counties);
    console.log(`📥 Fetched ${countiesData.length} counties from data source`);

    // Validate and clean county data
    const validCounties = validateCountyData(countiesData, stateMap);
    console.log(`🔍 Validated ${validCounties.length} counties for insertion`);

    if (validCounties.length === 0) {
      throw new Error('No valid counties found after validation');
    }

    // Check if counties already exist
    const existingCountyCount = await County.count();
    if (existingCountyCount > 0 && !options.clearFirst) {
      console.log(`✅ Counties already exist (${existingCountyCount} records). Use --clear-first to reseed.`);
      return;
    }

    // Process counties in batches
    let totalInserted = 0;

    const batchProcessor = async (batch, batchIndex) => {
      const transaction = await sequelize.transaction();
      try {
        await County.bulkCreate(batch, {
          transaction,
          ignoreDuplicates: true,
          updateOnDuplicate: ['name', 'state_id', 'state', 'is_active']
        });
        await transaction.commit();
        totalInserted += batch.length;
      } catch (error) {
        await transaction.rollback();
        console.error(`\n❌ Error processing batch ${batchIndex + 1}:`, error.message);
        throw error;
      }
    };

    await processBatches(validCounties, BATCH_SIZE, batchProcessor, 'Seeding counties');
    console.log(`✅ Successfully seeded ${totalInserted} counties`);

  } catch (error) {
    console.error('❌ Error seeding counties:', error.message);
    throw error;
  }
}

// Validate seeded data
async function validateSeededData(State, County) {
  console.log('🔍 Validating seeded data...');

  try {
    const stateCount = await State.count();
    const countyCount = await County.count();
    const activeStateCount = await State.count({ where: { is_active: true } });
    const activeCountyCount = await County.count({ where: { is_active: true } });

    console.log('📊 Data Summary:');
    console.log(`  States: ${stateCount} total, ${activeStateCount} active`);
    console.log(`  Counties: ${countyCount} total, ${activeCountyCount} active`);

    // Check for orphaned counties
    const orphanedCounties = await County.count({
      include: [{
        model: State,
        as: 'stateObj',
        required: false
      }],
      where: {
        '$stateObj.id$': null
      }
    });

    if (orphanedCounties > 0) {
      console.log(`⚠️ Warning: ${orphanedCounties} counties found without valid state references`);
    }

    // Sample data verification
    const sampleStates = await State.findAll({
      limit: 5,
      include: [{
        model: County,
        as: 'counties',
        limit: 3
      }]
    });

    console.log('📋 Sample data verification:');
    sampleStates.forEach(state => {
      console.log(`  ${state.name} (${state.abbreviation}): ${state.counties?.length || 0} counties loaded`);
    });

    return {
      states: stateCount,
      counties: countyCount,
      orphanedCounties
    };

  } catch (error) {
    console.error('❌ Error validating data:', error.message);
    throw error;
  }
}

// Main seeding function
async function seedCompleteLocationData() {
  let sequelize;
  const startTime = Date.now();

  try {
    console.log('🚀 Starting comprehensive location data seeding...');
    console.log(`🔌 Using environment: ${isInDocker() ? 'Docker' : 'Local'}`);
    console.log(`🔌 Connecting to database ${config.database} at ${config.host}...`);

    // Initialize database connection
    sequelize = new Sequelize(config);
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Define models
    console.log('📋 Defining location models...');

    // States model
    const State = sequelize.define('state', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: { type: DataTypes.STRING(100), allowNull: false },
      abbreviation: { type: DataTypes.STRING(2), allowNull: false },
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true }
    }, {
      tableName: 'states',
      timestamps: true,
      underscored: true
    });

    // Counties model
    const County = sequelize.define('county', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: { type: DataTypes.STRING, allowNull: false },
      state_id: { type: DataTypes.INTEGER, allowNull: true },
      state: { type: DataTypes.STRING, allowNull: false },
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true }
    }, {
      tableName: 'counties',
      timestamps: true,
      underscored: true
    });

    // Define associations
    State.hasMany(County, { foreignKey: 'state_id', as: 'counties' });
    County.belongsTo(State, { foreignKey: 'state_id', as: 'stateObj' });

    console.log('✅ Models defined successfully');

    // Ensure all required tables exist before seeding
    console.log('🔧 Ensuring database tables exist...');
    try {
      const models = { State, County };

      const tableResults = await ensureTablesExist(sequelize, models, {
        verbose: options.verbose,
        force: false,
        alter: false
      });

      if (tableResults.created.length > 0) {
        console.log(`✅ Created ${tableResults.created.length} missing tables: ${tableResults.created.join(', ')}`);
      }
      if (tableResults.existed.length > 0) {
        console.log(`✅ Verified ${tableResults.existed.length} existing tables`);
      }
    } catch (tableError) {
      console.error('❌ Failed to create required database tables:', tableError.message);
      console.error('   This may indicate a database connection or permission issue.');
      throw tableError;
    }

    // Show configuration
    console.log('\n📋 Seeding Configuration:');
    console.log(`  States only: ${options.statesOnly}`);
    console.log(`  Counties only: ${options.countiesOnly}`);
    console.log(`  Clear first: ${options.clearFirst}`);
    console.log(`  Include territories: ${options.includeTerritoriesFlag}`);
    console.log(`  Batch size: ${BATCH_SIZE}`);
    console.log(`  Verbose mode: ${options.verbose}`);

    // Clear existing data if requested
    if (options.clearFirst) {
      await clearLocationData(sequelize, State, County);
    }

    // Seed states (unless counties-only mode)
    let seededStates = [];
    if (!options.countiesOnly) {
      seededStates = await seedStates(sequelize, State);
    }

    // Seed counties (unless states-only mode)
    if (!options.statesOnly) {
      await seedCounties(sequelize, State, County);
    }

    // Validate the seeded data
    const validation = await validateSeededData(State, County);

    // Final summary
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('\n🎉 Comprehensive location data seeding completed successfully!');
    console.log(`⏱️ Total execution time: ${duration} seconds`);
    console.log('\n📊 Final Summary:');
    console.log(`  ✅ States seeded: ${validation.states}`);
    console.log(`  ✅ Counties seeded: ${validation.counties}`);

    if (validation.orphanedCounties > 0) {
      console.log(`  ⚠️ Orphaned counties: ${validation.orphanedCounties}`);
    }

    console.log('\n💡 Usage Tips:');
    console.log('  - Use the existing API endpoints to access this data');
    console.log('  - Counties are linked to states via state_id foreign key');
    console.log('  - All records are marked as active by default');
    console.log('  - Run with --verbose for detailed processing information');

    return {
      success: true,
      duration,
      states: validation.states,
      counties: validation.counties,
      orphanedCounties: validation.orphanedCounties
    };

  } catch (error) {
    console.error('\n❌ Error during comprehensive location seeding:', error);

    if (error.name === 'SequelizeConnectionError') {
      console.error('💡 Database connection failed. Please check:');
      console.error('  - Database server is running');
      console.error('  - Connection credentials are correct');
      console.error('  - Network connectivity');
    } else if (error.name === 'SequelizeValidationError') {
      console.error('💡 Data validation failed. Please check:');
      console.error('  - Database schema matches model definitions');
      console.error('  - Required fields are properly defined');
    }

    process.exit(1);
  } finally {
    if (sequelize) {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    }
  }
}

// Show help information
function showHelp() {
  console.log(`
🌍 Comprehensive Location Data Seeding Script

Usage:
  npm run seed-complete-locations
  node scripts/seed-complete-locations.js [options]

Options:
  --states-only          Seed only states data
  --counties-only        Seed only counties data (requires states to exist)
  --clear-first          Clear existing data before seeding
  --include-territories  Include US territories in addition to 50 states + DC
  --verbose, -v          Show detailed processing information
  --help, -h             Show this help message

Examples:
  # Full seeding (recommended for new installations)
  npm run seed-complete-locations

  # Seed only states
  node scripts/seed-complete-locations.js --states-only

  # Clear and reseed everything
  node scripts/seed-complete-locations.js --clear-first

  # Include territories
  node scripts/seed-complete-locations.js --include-territories

  # Verbose mode for debugging
  node scripts/seed-complete-locations.js --verbose

Data Sources:
  - States: All 50 US states + DC (optionally territories)
  - Counties: ~3,100+ counties from all states
  - Processing: Batch processing for optimal performance
  - Validation: Comprehensive data validation and error handling

Note: This script provides complete US coverage with all 50 states + DC
and ~3,100+ counties. Use for production deployments where comprehensive
location data is required.
`);
}

// Export the main function for use in other scripts
module.exports = seedCompleteLocationData;

// Run the seed function if executed directly
if (require.main === module) {
  // Check for help flag
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  seedCompleteLocationData().catch(err => {
    console.error('❌ Unhandled error:', err);
    process.exit(1);
  });
}
