<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Add Incident</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-plus-circle"></i> Create New Incident</h1>
                <p>Complete all sections to create a comprehensive incident report</p>
            </div>

            <!-- Form Container -->
            <form id="addIncidentForm" action="#" method="post" data-validate="true">

                <!-- Section 1: Incident Type -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-fire" style="margin-right: 10px; color: var(--accent-red);"></i>
                            Incident Type
                        </div>
                        <div class="section-status" id="type-status">
                            <i class="fas fa-circle incomplete"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Select Incident Type *</label>
                            <div class="incident-type-selector">
                                <div class="incident-type-option fire" data-type="fire">
                                    <i class="fas fa-fire"></i>
                                    <span>Fire Incident</span>
                                    <small>Structure fires, vehicle fires, explosions</small>
                                </div>
                                <div class="incident-type-option water" data-type="water">
                                    <i class="fas fa-water"></i>
                                    <span>Water Incident</span>
                                    <small>Water main breaks, flooding, hydrant damage</small>
                                </div>
                                <input type="hidden" name="incident_type" id="incident_type" required>
                            </div>
                        </div>

                        <!-- Fire-specific fields -->
                        <div class="fire-specific" style="display: none;">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Fire Type *</label>
                                        <select class="form-control" name="fire_type" id="fire_type">
                                            <option value="">Select Fire Type</option>
                                            <option value="1">Structure Fire - Residential</option>
                                            <option value="2">Structure Fire - Commercial</option>
                                            <option value="3">Vehicle Fire</option>
                                            <option value="4">Brush/Wildland Fire</option>
                                            <option value="11">Electrical Fire</option>
                                            <option value="12">Chimney Fire</option>
                                            <option value="13">Explosion</option>
                                            <option value="14">Hazardous Material</option>
                                            <option value="15">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Fire Alarm Level *</label>
                                        <select class="form-control" name="alarm_level" id="alarm_level">
                                            <option value="1" selected>Level 1 - Single Engine Response</option>
                                            <option value="2">Level 2 - Multiple Engine Response</option>
                                            <option value="3">Level 3 - Full Assignment Response</option>
                                            <option value="4">Level 4 - Multiple Alarm Response</option>
                                            <option value="5">Level 5 - Major Emergency Response</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Smoke Severity</label>
                                        <select class="form-control" name="smoke_severity" id="smoke_severity">
                                            <option value="">Select Smoke Severity</option>
                                            <option value="No Visible Smoke">No Visible Smoke</option>
                                            <option value="Light">Light</option>
                                            <option value="Moderate">Moderate</option>
                                            <option value="Heavy">Heavy</option>
                                            <option value="Dense">Dense</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <!-- Empty for layout balance -->
                                </div>
                            </div>
                        </div>

                        <!-- Water-specific fields -->
                        <div class="water-specific" style="display: none;">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Water Incident Type *</label>
                                        <select class="form-control" name="water_type" id="water_type">
                                            <option value="">Select Water Type</option>
                                            <option value="5">Water Main Break</option>
                                            <option value="6">Flooding</option>
                                            <option value="7">Fire Hydrant Damage</option>
                                            <option value="8">Sewer Backup</option>
                                            <option value="9">Water Rescue</option>
                                            <option value="10">Dam/Levee Breach</option>
                                            <option value="16">Water Treatment Issue</option>
                                            <option value="17">Water Supply Contamination</option>
                                            <option value="18">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Affected Area</label>
                                        <textarea class="form-control" name="affected_area" rows="3" placeholder="Describe the area affected..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Basic Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-info-circle" style="margin-right: 10px; color: var(--accent-blue);"></i>
                            Basic Information
                        </div>
                        <div class="section-status" id="basic-status">
                            <i class="fas fa-circle incomplete"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Incident Title *</label>
                                    <input type="text" class="form-control" name="title" id="title" required placeholder="E.g., Residential Structure Fire">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Status *</label>
                                    <select class="form-control" name="status" id="status" required>
                                        <option value="">Select Status</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Severity</label>
                                    <select class="form-control" name="severity">
                                        <option value="low">Low</option>
                                        <option value="moderate" selected>Moderate</option>
                                        <option value="high">High</option>
                                        <option value="critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <!-- Empty for layout balance -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 3: Location Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-map-marker-alt" style="margin-right: 10px; color: var(--accent-green);"></i>
                            Location Information
                        </div>
                        <div class="section-status" id="location-status">
                            <i class="fas fa-circle incomplete"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Search Address *</label>
                            <div style="position: relative;">
                                <input type="text" class="form-control" id="google_address" placeholder="123 Main Street, Springfield">
                                <div id="geocoding-status" style="position: absolute; top: 8px; right: 10px; font-size: 12px; color: var(--text-secondary); display: none;">
                                    <i class="fas fa-map-marker-alt"></i> <span id="geocoding-provider">Loading...</span>
                                </div>
                            </div>
                            <small style="color: var(--text-secondary); display: block; margin-top: 5px;">Type street address and city (e.g., "123 Main St, Springfield") - all location details will auto-populate</small>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Street Address *</label>
                                    <input type="text" class="form-control" name="address" id="address" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">City *</label>
                                    <input type="text" class="form-control" name="city" id="city" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">State *</label>
                                    <select class="form-control" name="state_id" id="state_select" required>
                                        <option value="">Select State</option>
                                    </select>
                                    <input type="hidden" name="state" id="state">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">County *</label>
                                    <select class="form-control" name="county_id" id="county_select" required disabled>
                                        <option value="">Select County</option>
                                    </select>
                                    <input type="hidden" name="county" id="county">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col flex-30">
                                <div class="form-group">
                                    <label class="form-label">ZIP Code</label>
                                    <input type="text" class="form-control" name="zip_code" id="zip_code">
                                </div>
                            </div>
                            <div class="form-col flex-30">
                                <div class="form-group">
                                    <label class="form-label">Building Stories</label>
                                    <input type="number" class="form-control" name="home_stories" id="home_stories" min="1" max="10" placeholder="Number of floors/stories">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Coordinates</label>
                                    <div style="display: flex; gap: 10px;">
                                        <input type="text" class="form-control" name="latitude" id="latitude" placeholder="Latitude" readonly style="flex: 1;">
                                        <input type="text" class="form-control" name="longitude" id="longitude" placeholder="Longitude" readonly style="flex: 1;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 4: Property Owner Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-user-circle" style="margin-right: 10px; color: var(--accent-orange);"></i>
                            Property Owner Information
                        </div>
                        <div class="section-actions">
                            <button type="button" class="btn btn-outline btn-sm" id="refreshOwnerDataBtn">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Primary Owner</label>
                                    <input type="text" class="form-control" name="owner1" id="owner1">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Secondary Owner</label>
                                    <input type="text" class="form-control" name="owner2" id="owner2">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Address</label>
                                    <input type="text" class="form-control" name="ownerAddr" id="ownerAddr">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Phone</label>
                                    <input type="text" class="form-control" name="ownerPhone" id="ownerPhone">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Email</label>
                                    <input type="email" class="form-control" name="ownerEmail" id="ownerEmail">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Dwelling Type</label>
                                    <input type="text" class="form-control" name="dwellingType" id="dwellingType">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Year Built</label>
                                    <input type="number" class="form-control" name="yearBuilt" id="yearBuilt" min="1800" max="2030" placeholder="e.g. 1985">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Property Value</label>
                                    <input type="text" class="form-control" name="propertyValue" id="propertyValue" placeholder="e.g. $450,000" readonly>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- Section 5: Incident Details -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-clipboard-list" style="margin-right: 10px; color: var(--text-secondary);"></i>
                            Incident Details
                        </div>
                        <div class="section-status" id="details-status">
                            <i class="fas fa-circle incomplete"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Incident Description *</label>
                            <textarea class="form-control" name="description" rows="4" required placeholder="Provide detailed information about the incident..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Response Details</label>
                            <textarea class="form-control" name="response_details" rows="3" placeholder="Information about responding units, actions taken..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Conditions on Arrival</label>
                            <textarea class="form-control" name="conditions_on_arrival" id="conditions_on_arrival" rows="3" placeholder="Describe the scene conditions when first responders arrived..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-control" name="notes" rows="2" placeholder="Any additional information..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Upload Images</label>
                            <input type="file" class="form-control" name="images" multiple accept="image/*">
                            <small style="color: var(--text-secondary); display: block; margin-top: 5px;">You can upload multiple images (Max 5MB each)</small>
                        </div>
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="card">
                    <div class="card-content">
                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <a href="incidents.html" class="btn btn-outline">Cancel</a>
                            <button type="submit" class="btn btn-primary" id="add-incident-btn">
                                <i class="fas fa-plus-circle"></i> Create Incident
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div id="loadingMessage">Loading property information...</div>
        </div>
    </div>

    <!-- Notification element -->
    <div class="notification" id="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notification-message">Notification message</span>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/google-maps-integration.js"></script>
    <script src="js/api-keys.js"></script>
    <script src="js/api.js"></script>

    <style>
        /* Version 1: Organized Sections - Following existing design language */
        .page-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .page-header h1 {
            font-size: 1.8rem;
            margin-bottom: 8px;
            color: var(--text-light);
            display: flex;
            align-items: center;
        }

        .page-header h1 i {
            color: var(--accent-blue);
            margin-right: 12px;
        }

        .page-header p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 1rem;
        }

        .section-card {
            margin-bottom: 25px;
        }

        .section-card .card-header {
            position: relative;
        }

        .section-status {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .section-status i {
            font-size: 12px;
        }

        .section-status .incomplete {
            color: var(--text-secondary);
        }

        .section-status .complete {
            color: var(--accent-green);
        }

        .section-actions {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        /* Enhanced incident type selector */
        .incident-type-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .incident-type-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .incident-type-option:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--accent-blue);
        }

        .incident-type-option.active {
            border-color: var(--accent-blue);
            background: rgba(30, 136, 229, 0.1);
        }

        .incident-type-option.fire.active {
            border-color: var(--accent-red);
            background: rgba(229, 57, 53, 0.1);
        }

        .incident-type-option i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
            color: var(--text-secondary);
        }

        .incident-type-option.fire i {
            color: var(--accent-red);
        }

        .incident-type-option.water i {
            color: var(--accent-blue);
        }

        .incident-type-option span {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--text-light);
        }

        .incident-type-option small {
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        /* Fire and water specific sections */
        .fire-specific, .water-specific {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: var(--secondary-dark);
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            color: var(--text-light);
        }

        .loading-spinner i {
            font-size: 24px;
            margin-bottom: 15px;
            color: var(--accent-blue);
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--secondary-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 10000;
            max-width: 400px;
            color: var(--text-light);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--accent-green);
        }

        .notification.error {
            border-left: 4px solid var(--accent-red);
        }

        .notification.warning {
            border-left: 4px solid var(--accent-orange);
        }

        .notification.info {
            border-left: 4px solid var(--accent-blue);
        }

        .notification i {
            font-size: 16px;
        }

        .notification.success i {
            color: var(--accent-green);
        }

        .notification.error i {
            color: var(--accent-red);
        }

        .notification.warning i {
            color: var(--accent-orange);
        }

        .notification.info i {
            color: var(--accent-blue);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .incident-type-selector {
                grid-template-columns: 1fr;
            }

            .form-row {
                flex-direction: column;
            }

            .form-col {
                width: 100%;
                margin-bottom: 15px;
            }

            .form-control {
                min-height: 44px;
                font-size: 16px; /* Prevent zoom on iOS */
            }

            .btn {
                min-height: 44px;
                padding: 12px 20px;
            }

            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .section-status,
            .section-actions {
                position: static;
                transform: none;
            }

            .notification {
                right: 10px;
                left: 10px;
                max-width: none;
                transform: translateY(-100px);
            }

            .notification.show {
                transform: translateY(0);
            }
        }

        @media (max-width: 576px) {
            .page-header h1 {
                font-size: 1.5rem;
            }

            .incident-type-option {
                padding: 15px;
            }

            .incident-type-option i {
                font-size: 1.5rem;
            }

            .card {
                margin: 10px;
                border-radius: 12px;
            }

            .card-content {
                padding: 15px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-label {
                font-size: 14px;
                margin-bottom: 8px;
            }

            /* Stack submit buttons on mobile */
            .card-content > div[style*="justify-content: flex-end"] {
                flex-direction: column;
                gap: 10px;
            }

            .card-content > div[style*="justify-content: flex-end"] .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        function updateSectionStatus(sectionId, isComplete) {
            const statusElement = document.getElementById(sectionId + '-status');
            if (statusElement) {
                const icon = statusElement.querySelector('i');
                icon.className = isComplete ? 'fas fa-check-circle complete' : 'fas fa-circle incomplete';
            }
        }

        function setupIncidentTypeSelection() {
            const incidentTypeOptions = document.querySelectorAll('.incident-type-option');
            const incidentTypeInput = document.getElementById('incident_type');
            const fireSpecificElements = document.querySelectorAll('.fire-specific');
            const waterSpecificElements = document.querySelectorAll('.water-specific');

            incidentTypeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    incidentTypeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');

                    const selectedType = this.getAttribute('data-type');
                    incidentTypeInput.value = selectedType;

                    if (selectedType === 'fire') {
                        fireSpecificElements.forEach(el => el.style.display = 'block');
                        waterSpecificElements.forEach(el => el.style.display = 'none');
                    } else if (selectedType === 'water') {
                        fireSpecificElements.forEach(el => el.style.display = 'none');
                        waterSpecificElements.forEach(el => el.style.display = 'block');
                    }

                    updateSectionStatus('type', true);
                });
            });
        }

        function setupFormValidation() {
            const requiredFields = document.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    const section = this.closest('.section-card');
                    if (section) {
                        const sectionId = section.querySelector('.section-status').id.replace('-status', '');
                        const sectionRequiredFields = section.querySelectorAll('[required]');
                        const allFilled = Array.from(sectionRequiredFields).every(f => f.value.trim() !== '');
                        updateSectionStatus(sectionId, allFilled);
                    }
                });
            });
        }

        // Populate states dropdown from API - NO FALLBACK DATA
        async function populateStatesDropdown() {
            const stateSelect = document.getElementById('state_select');

            try {
                // Show loading state
                stateSelect.innerHTML = '<option value="">Loading states...</option>';
                stateSelect.disabled = true;

                // Fetch states from API
                const response = await API.locations.getStates();

                if (response && Array.isArray(response) && response.length > 0) {
                    // Clear loading state
                    stateSelect.innerHTML = '<option value="">Select State</option>';

                    // Populate with real states
                    response.forEach(state => {
                        const option = document.createElement('option');
                        option.value = state.id;
                        option.textContent = `${state.name} (${state.abbreviation})`;
                        option.dataset.abbr = state.abbreviation;
                        option.dataset.name = state.name;
                        stateSelect.appendChild(option);
                    });

                    stateSelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} states from API`);
                } else {
                    throw new Error('No states received from API');
                }
            } catch (error) {
                console.error('Error loading states:', error);

                // NO FALLBACK - Show error state
                stateSelect.innerHTML = '<option value="">Error loading states</option>';
                stateSelect.disabled = true;
                showNotification('Failed to load states from server. Please refresh the page or contact support.', 'error');
            }
        }

        async function populateCountiesDropdown(stateId) {
            const countySelect = document.getElementById('county_select');

            if (!stateId) {
                countySelect.innerHTML = '<option value="">Select County</option>';
                countySelect.disabled = true;
                return;
            }

            try {
                // Show loading state
                countySelect.innerHTML = '<option value="">Loading counties...</option>';
                countySelect.disabled = true;

                // Fetch counties from API
                const response = await API.locations.getCounties(stateId);

                if (response && Array.isArray(response) && response.length > 0) {
                    // Clear loading state
                    countySelect.innerHTML = '<option value="">Select County</option>';

                    // Populate with real counties (now with proper "County" suffixes)
                    response.forEach(county => {
                        const option = document.createElement('option');
                        option.value = county.id;
                        option.textContent = county.name;
                        option.dataset.name = county.name;
                        countySelect.appendChild(option);
                    });

                    countySelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} counties for state ID ${stateId} from API`);
                } else {
                    // No counties found
                    countySelect.innerHTML = '<option value="">No counties available</option>';
                    countySelect.disabled = true;
                    console.warn(`No counties found for state ID ${stateId}`);
                }
            } catch (error) {
                console.error('Error loading counties:', error);

                // NO FALLBACK - Show error state
                countySelect.innerHTML = '<option value="">Error loading counties</option>';
                countySelect.disabled = true;
                showNotification('Failed to load counties from server. Please try selecting a different state or refresh the page.', 'error');
            }
        }



        // Populate incident statuses dropdown from API
        async function populateIncidentStatusesDropdown() {
            const statusSelect = document.getElementById('status');

            if (!statusSelect) {
                console.warn('Status select element not found');
                return;
            }

            try {
                // Show loading state
                statusSelect.innerHTML = '<option value="">Loading statuses...</option>';
                statusSelect.disabled = true;

                // Check if API method exists
                if (!API.settings || typeof API.settings.getIncidentStatuses !== 'function') {
                    throw new Error('API method getIncidentStatuses not available');
                }

                // Fetch incident statuses from API
                const response = await API.settings.getIncidentStatuses();

                if (response && Array.isArray(response) && response.length > 0) {
                    // Clear loading state
                    statusSelect.innerHTML = '<option value="">Select Status</option>';

                    // Populate with real incident statuses
                    response.forEach(status => {
                        const option = document.createElement('option');
                        option.value = status.name || status.status;
                        option.textContent = status.name || status.status;
                        statusSelect.appendChild(option);
                    });

                    statusSelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} incident statuses from API`);
                } else {
                    throw new Error('No incident statuses received from API');
                }
            } catch (error) {
                console.error('Error loading incident statuses:', error);

                // Provide basic fallback statuses
                statusSelect.innerHTML = '<option value="">Select Status</option>';
                const basicStatuses = [
                    { name: 'active', display: 'Active' },
                    { name: 'pending', display: 'Pending' },
                    { name: 'critical', display: 'Critical' },
                    { name: 'resolved', display: 'Resolved' }
                ];

                basicStatuses.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.name;
                    option.textContent = status.display;
                    statusSelect.appendChild(option);
                });

                statusSelect.disabled = false;
                console.log('Using fallback incident statuses');
                showNotification('Using basic incident statuses (API unavailable)', 'warning');
            }
        }

        // Populate operating systems dropdown from API
        async function populateOperatingSystemsDropdown() {
            const osSelect = document.getElementById('operating_system');

            if (!osSelect) {
                console.warn('Operating system select element not found');
                return; // Skip if field doesn't exist
            }

            try {
                // Show loading state
                osSelect.innerHTML = '<option value="">Loading systems...</option>';
                osSelect.disabled = true;

                // Check if API method exists
                if (!API.settings || typeof API.settings.getOperatingSystems !== 'function') {
                    throw new Error('API method getOperatingSystems not available');
                }

                // Fetch operating systems from API
                const response = await API.settings.getOperatingSystems();

                if (response && Array.isArray(response) && response.length > 0) {
                    // Clear loading state
                    osSelect.innerHTML = '<option value="">Select Operating System</option>';

                    // Populate with real operating systems
                    response.forEach(os => {
                        const option = document.createElement('option');
                        option.value = os.name || os.os;
                        option.textContent = os.name || os.os;
                        osSelect.appendChild(option);
                    });

                    osSelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} operating systems from API`);
                } else {
                    throw new Error('No operating systems received from API');
                }
            } catch (error) {
                console.error('Error loading operating systems:', error);

                // Provide basic fallback operating systems
                osSelect.innerHTML = '<option value="">Select Operating System</option>';
                const basicOS = [
                    { name: 'fire_dept', display: 'Fire Department' },
                    { name: 'water_dept', display: 'Water Department' },
                    { name: 'emergency_mgmt', display: 'Emergency Management' },
                    { name: 'police_dept', display: 'Police Department' }
                ];

                basicOS.forEach(os => {
                    const option = document.createElement('option');
                    option.value = os.name;
                    option.textContent = os.display;
                    osSelect.appendChild(option);
                });

                osSelect.disabled = false;
                console.log('Using fallback operating systems');
                showNotification('Using basic operating systems (API unavailable)', 'warning');
            }
        }

        // Google Maps Autocomplete Integration
        function initAutocomplete() {
            console.log('Google Maps API loaded successfully, initializing autocomplete');

            // Check if Google Maps is blocked or fallback is already initialized
            if (window.geocodingState.googleMapsBlocked) {
                console.log('Google Maps is blocked, skipping initialization');
                return;
            }

            if (window.geocodingState.fallbackInitialized) {
                console.log('Fallback already initialized, skipping Google Maps setup');
                return;
            }

            // Clear the fallback timer since Google Maps loaded successfully
            if (window.geocodingState.fallbackTimer) {
                clearTimeout(window.geocodingState.fallbackTimer);
                window.geocodingState.fallbackTimer = null;
            }

            const addressInput = document.getElementById('google_address');
            if (!addressInput) {
                console.warn('Google address input not found');
                // Initialize fallback geocoding if Google Maps fails
                initializeFallbackGeocoding();
                return;
            }

            try {
                // Try modern PlaceAutocompleteElement first
                if (google.maps.places.PlaceAutocompleteElement) {
                    console.log('Using modern PlaceAutocompleteElement API');
                    const autocompleteElement = new google.maps.places.PlaceAutocompleteElement({
                        inputElement: addressInput,
                        types: ['address'],
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry', 'formatted_address']
                    });

                    autocompleteElement.addListener('place_changed', async () => {
                        const place = await autocompleteElement.getPlace();
                        fillInAddress(place);
                    });
                } else {
                    throw new Error('PlaceAutocompleteElement not available');
                }
            } catch (e) {
                console.log('Using legacy Autocomplete API (PlaceAutocompleteElement not yet available)');

                // Fallback to legacy Autocomplete API
                const autocomplete = new google.maps.places.Autocomplete(
                    addressInput,
                    {
                        types: ['address'],
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry', 'formatted_address']
                    }
                );

                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();
                    fillInAddress(place);
                });
            }

            // Set provider state and show status
            window.geocodingState.currentProvider = 'google';
            updateGeocodingStatus('Google Maps', 'var(--accent-blue)');
            console.log('Google Maps geocoding initialized successfully');
        }

        function updateGeocodingStatus(provider, color) {
            const statusElement = document.getElementById('geocoding-status');
            const providerElement = document.getElementById('geocoding-provider');

            if (statusElement && providerElement) {
                providerElement.textContent = provider;
                statusElement.style.color = color;
                statusElement.style.display = 'block';
            }
        }

        function fillInAddress(place) {
            if (!place || !place.geometry) {
                console.warn('No place geometry available');
                return;
            }

            // Extract coordinates
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();

            console.log('Geocoded coordinates:', lat, lng);

            // Fill coordinate fields
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;

            // Parse address components
            let addressData = {};
            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('street_number')) {
                    addressData.streetNumber = component.long_name;
                } else if (types.includes('route')) {
                    addressData.street = component.long_name;
                } else if (types.includes('locality')) {
                    addressData.city = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                    addressData.state = component.short_name;
                } else if (types.includes('postal_code')) {
                    addressData.zip = component.long_name;
                } else if (types.includes('administrative_area_level_2')) {
                    addressData.county = component.long_name.replace(' County', '');
                }
            });

            // Fill address fields
            if (addressData.streetNumber && addressData.street) {
                document.getElementById('address').value = `${addressData.streetNumber} ${addressData.street}`;
            } else if (place.formatted_address) {
                // Use first part of formatted address as fallback
                const addressParts = place.formatted_address.split(',');
                document.getElementById('address').value = addressParts[0];
            }

            if (addressData.city) {
                document.getElementById('city').value = addressData.city;
            }

            if (addressData.zip) {
                document.getElementById('zip_code').value = addressData.zip;
            }

            // Handle state and county dropdowns
            if (addressData.state) {
                const stateSelect = document.getElementById('state_select');
                const stateHidden = document.getElementById('state');

                if (stateSelect && stateHidden) {
                    // Find and select the state in dropdown
                    for (let i = 0; i < stateSelect.options.length; i++) {
                        if (stateSelect.options[i].dataset.abbr === addressData.state) {
                            stateSelect.selectedIndex = i;
                            stateHidden.value = addressData.state;

                            // Trigger change event to load counties
                            const event = new Event('change');
                            stateSelect.dispatchEvent(event);

                            // Set county after a delay to allow counties to load
                            if (addressData.county) {
                                setTimeout(() => {
                                    const countySelect = document.getElementById('county_select');
                                    const countyHidden = document.getElementById('county');

                                    if (countySelect && countyHidden) {
                                        for (let j = 0; j < countySelect.options.length; j++) {
                                            if (countySelect.options[j].textContent.includes(addressData.county)) {
                                                countySelect.selectedIndex = j;
                                                countyHidden.value = addressData.county;
                                                break;
                                            }
                                        }
                                    }
                                }, 1000);
                            }
                            break;
                        }
                    }
                }
            }

            showNotification('Address details populated successfully!', 'success');
        }

        // OpenStreetMap/Nominatim Fallback Geocoding System
        function initializeFallbackGeocoding() {
            // Prevent duplicate initialization
            if (window.geocodingState.fallbackInitialized) {
                console.log('Fallback geocoding already initialized');
                return;
            }

            console.log('Initializing OpenStreetMap/Nominatim fallback geocoding');
            const addressInput = document.getElementById('google_address');

            if (!addressInput) {
                console.warn('Address input not found for fallback geocoding');
                return;
            }

            // Clear any existing Google Maps autocomplete and prevent interference
            try {
                if (typeof google !== 'undefined' && google.maps && google.maps.places) {
                    // Remove any existing Google Maps autocomplete listeners
                    google.maps.event.clearInstanceListeners(addressInput);
                }

                // Remove any Google Maps related attributes or classes
                addressInput.removeAttribute('data-gm-autocomplete');
                addressInput.classList.remove('pac-target-input');

                // Clear any existing autocomplete containers
                const existingContainers = document.querySelectorAll('.pac-container');
                existingContainers.forEach(container => container.remove());

            } catch (e) {
                console.log('Cleared Google Maps interference:', e.message);
            }

            // Mark fallback as initialized
            window.geocodingState.fallbackInitialized = true;
            window.geocodingState.currentProvider = 'openstreetmap';

            // Create autocomplete container
            const autocompleteContainer = document.createElement('div');
            autocompleteContainer.id = 'nominatim-autocomplete';
            autocompleteContainer.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--secondary-dark);
                border: 1px solid var(--border-color);
                border-top: none;
                border-radius: 0 0 8px 8px;
                max-height: 200px;
                overflow-y: auto;
                z-index: 1000;
                display: none;
            `;

            // Make the parent container relative for positioning
            const parentContainer = addressInput.parentElement;
            parentContainer.style.position = 'relative';
            parentContainer.appendChild(autocompleteContainer);

            let searchTimeout;
            let currentResults = [];

            // Remove any existing input event listeners to prevent conflicts
            const newAddressInput = addressInput.cloneNode(true);
            addressInput.parentNode.replaceChild(newAddressInput, addressInput);

            // Add input event listener for autocomplete
            newAddressInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length < 3) {
                    hideAutocomplete();
                    return;
                }

                // Debounce the search
                searchTimeout = setTimeout(() => {
                    searchAddresses(query);
                }, 300);
            });

            // Hide autocomplete when clicking outside
            document.addEventListener('click', function(e) {
                if (!parentContainer.contains(e.target)) {
                    hideAutocomplete();
                }
            });

            function searchAddresses(query) {
                // Show loading state
                autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--text-secondary);">Searching addresses...</div>';
                autocompleteContainer.style.display = 'block';

                // Improve search query for better user input handling
                const optimizedQuery = optimizeSearchQuery(query);

                // Search using Nominatim API with improved parameters
                const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(optimizedQuery)}&countrycodes=us&limit=5&addressdetails=1&extratags=1&namedetails=1`;

                fetch(nominatimUrl)
                    .then(response => response.json())
                    .then(data => {
                        // Filter and sort results for better relevance
                        const filteredResults = filterAndSortResults(data, query);
                        currentResults = filteredResults;
                        displayAutocompleteResults(filteredResults);
                    })
                    .catch(error => {
                        console.error('Nominatim geocoding error:', error);
                        autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--accent-red);">Error searching addresses</div>';
                    });
            }

            // Optimize search query for better Nominatim results
            function optimizeSearchQuery(query) {
                // Handle common user input patterns
                let optimized = query.trim();

                // If query contains comma, treat parts appropriately
                if (optimized.includes(',')) {
                    const parts = optimized.split(',').map(part => part.trim());

                    // If user typed "123 Main St, Springfield" format
                    if (parts.length === 2) {
                        const [streetPart, cityPart] = parts;
                        // Enhance query to help Nominatim find better matches
                        optimized = `${streetPart}, ${cityPart}, USA`;
                    }
                }

                return optimized;
            }

            // Filter and sort results for better relevance
            function filterAndSortResults(results, originalQuery) {
                if (!results || results.length === 0) return results;

                // Filter out results that are too generic or not useful
                const filtered = results.filter(result => {
                    const address = result.address || {};

                    // Must have at least a road/street name
                    if (!address.road && !address.house_number) {
                        return false;
                    }

                    // Prefer results with house numbers for specific addresses
                    if (originalQuery.match(/^\d+/)) {
                        return address.house_number || address.road;
                    }

                    return true;
                });

                // Sort by relevance (prefer results with house numbers if query starts with number)
                const sorted = filtered.sort((a, b) => {
                    const aAddress = a.address || {};
                    const bAddress = b.address || {};

                    // If original query starts with a number, prioritize results with house numbers
                    if (originalQuery.match(/^\d+/)) {
                        if (aAddress.house_number && !bAddress.house_number) return -1;
                        if (!aAddress.house_number && bAddress.house_number) return 1;
                    }

                    // Prefer more complete addresses
                    const aCompleteness = (aAddress.house_number ? 1 : 0) +
                                         (aAddress.road ? 1 : 0) +
                                         (aAddress.city || aAddress.town ? 1 : 0) +
                                         (aAddress.state ? 1 : 0);
                    const bCompleteness = (bAddress.house_number ? 1 : 0) +
                                         (bAddress.road ? 1 : 0) +
                                         (bAddress.city || bAddress.town ? 1 : 0) +
                                         (bAddress.state ? 1 : 0);

                    return bCompleteness - aCompleteness;
                });

                return sorted.slice(0, 5); // Limit to top 5 results
            }

            function displayAutocompleteResults(results) {
                if (!results || results.length === 0) {
                    autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--text-secondary);">No addresses found</div>';
                    return;
                }

                autocompleteContainer.innerHTML = '';

                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.style.cssText = `
                        padding: 10px;
                        cursor: pointer;
                        border-bottom: 1px solid var(--border-color);
                        color: var(--text-light);
                        transition: background-color 0.2s ease;
                    `;

                    // Format address in standard US format: "Street Address, City, State Zipcode"
                    const formattedAddress = formatAddressForDisplay(result);

                    resultItem.innerHTML = `
                        <div style="font-weight: 500;">${formattedAddress}</div>
                        <small style="color: var(--text-secondary);">Lat: ${result.lat}, Lng: ${result.lon}</small>
                    `;

                    // Hover effects
                    resultItem.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'var(--hover-bg)';
                    });

                    resultItem.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                    });

                    // Click handler
                    resultItem.addEventListener('click', function() {
                        selectNominatimResult(result);
                        hideAutocomplete();
                    });

                    autocompleteContainer.appendChild(resultItem);
                });
            }

            // Format address for display in standard US format
            function formatAddressForDisplay(result) {
                const address = result.address || {};
                let formattedParts = [];

                // Street address (house number + road)
                let streetAddress = '';
                if (address.house_number && address.road) {
                    streetAddress = `${address.house_number} ${address.road}`;
                } else if (address.road) {
                    streetAddress = address.road;
                } else {
                    // Use first part of display name as fallback
                    streetAddress = result.display_name.split(',')[0];
                }
                formattedParts.push(streetAddress);

                // City
                const city = address.city || address.town || address.village || address.hamlet || '';
                if (city) {
                    formattedParts.push(city);
                }

                // State and ZIP (use abbreviations to match Google Maps format)
                let stateZip = '';
                if (address.state) {
                    const stateAbbr = getStateAbbreviation(address.state);
                    stateZip = stateAbbr;
                    if (address.postcode) {
                        stateZip += ` ${address.postcode}`;
                    }
                    formattedParts.push(stateZip);
                } else if (address.postcode) {
                    formattedParts.push(address.postcode);
                }

                return formattedParts.join(', ');
            }

            function selectNominatimResult(result) {
                console.log('Selected Nominatim result:', result);

                // Fill coordinate fields
                document.getElementById('latitude').value = result.lat;
                document.getElementById('longitude').value = result.lon;

                // Parse address components from Nominatim result
                const address = result.address || {};

                // Fill address field
                let streetAddress = '';
                if (address.house_number && address.road) {
                    streetAddress = `${address.house_number} ${address.road}`;
                } else if (address.road) {
                    streetAddress = address.road;
                } else {
                    // Use first part of display name as fallback
                    streetAddress = result.display_name.split(',')[0];
                }
                document.getElementById('address').value = streetAddress;

                // Fill city field
                const city = address.city || address.town || address.village || address.hamlet || '';
                if (city) {
                    document.getElementById('city').value = city;
                }

                // Fill zip code
                if (address.postcode) {
                    document.getElementById('zip_code').value = address.postcode;
                }

                // Handle state
                if (address.state) {
                    const stateSelect = document.getElementById('state_select');
                    const stateHidden = document.getElementById('state');

                    if (stateSelect && stateHidden) {
                        // Try to find state by name or abbreviation
                        const stateName = address.state;
                        for (let i = 0; i < stateSelect.options.length; i++) {
                            const option = stateSelect.options[i];
                            if (option.dataset.name === stateName ||
                                option.textContent.includes(stateName) ||
                                option.dataset.abbr === getStateAbbreviation(stateName)) {
                                stateSelect.selectedIndex = i;
                                stateHidden.value = option.dataset.abbr || getStateAbbreviation(stateName);

                                // Trigger change event to load counties
                                const event = new Event('change');
                                stateSelect.dispatchEvent(event);

                                // Set county after delay
                                if (address.county) {
                                    setTimeout(() => {
                                        setCountyFromNominatim(address.county);
                                    }, 1000);
                                }
                                break;
                            }
                        }
                    }
                }

                // Update the search input with the formatted address (without county)
                const formattedDisplayAddress = formatAddressForDisplay(result);
                newAddressInput.value = formattedDisplayAddress;

                showNotification('Address geocoded successfully using OpenStreetMap!', 'success');
            }

            function setCountyFromNominatim(countyName) {
                const countySelect = document.getElementById('county_select');
                const countyHidden = document.getElementById('county');

                if (countySelect && countyHidden) {
                    // Clean county name (remove "County" suffix if present)
                    const cleanCountyName = countyName.replace(' County', '');

                    for (let j = 0; j < countySelect.options.length; j++) {
                        if (countySelect.options[j].textContent.includes(cleanCountyName)) {
                            countySelect.selectedIndex = j;
                            countyHidden.value = cleanCountyName;
                            break;
                        }
                    }
                }
            }

            function hideAutocomplete() {
                autocompleteContainer.style.display = 'none';
            }

            // Helper function to get state abbreviation from full name
            function getStateAbbreviation(stateName) {
                const stateMap = {
                    'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
                    'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
                    'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
                    'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
                    'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS', 'Missouri': 'MO',
                    'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ',
                    'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH',
                    'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
                    'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
                    'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY'
                };
                return stateMap[stateName] || stateName;
            }

            // Show OpenStreetMap status
            updateGeocodingStatus('OpenStreetMap', 'var(--accent-green)');

            console.log('✅ OpenStreetMap/Nominatim fallback geocoding initialized successfully');
            console.log('✅ Address input field is now fully functional');

            // Notify user that geocoding is ready
            showNotification('Address geocoding ready (OpenStreetMap)', 'info');
        }

        // Property owner data fetching
        async function fetchPropertyOwnerData(address, city, state, zip) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingMessage = document.getElementById('loadingMessage');

            try {
                loadingOverlay.style.display = 'flex';
                loadingMessage.textContent = 'Fetching property owner information...';

                // Make real API call to Estated
                const response = await fetch('/api/locations/property', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                    },
                    body: JSON.stringify({
                        address,
                        city,
                        state,
                        zip
                    })
                });

                if (!response.ok) {
                    // Try to parse error message from response
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || 'Failed to retrieve property data');
                }

                const data = await response.json();

                if (data.success) {
                    // Populate owner fields with real data
                    const owner1Field = document.getElementById('owner1');
                    if (owner1Field) owner1Field.value = data.property.owner1 || '';

                    const owner2Field = document.getElementById('owner2');
                    if (owner2Field) owner2Field.value = data.property.owner2 || '';

                    const ownerAddrField = document.getElementById('ownerAddr');
                    if (ownerAddrField) ownerAddrField.value = data.property.ownerAddress || '';

                    const ownerPhoneField = document.getElementById('ownerPhone');
                    if (ownerPhoneField) ownerPhoneField.value = data.property.ownerPhone || '';

                    const ownerEmailField = document.getElementById('ownerEmail');
                    if (ownerEmailField) ownerEmailField.value = data.property.ownerEmail || '';

                    const dwellingTypeField = document.getElementById('dwellingType');
                    if (dwellingTypeField) dwellingTypeField.value = data.property.dwellingType || '';

                    const yearBuiltField = document.getElementById('yearBuilt');
                    if (yearBuiltField) yearBuiltField.value = data.property.yearBuilt || '';

                    const propertyValueField = document.getElementById('propertyValue');
                    if (propertyValueField) propertyValueField.value = data.property.valuationFormatted || '';

                    showNotification('Property owner information loaded successfully from Estated API!', 'success');
                } else {
                    throw new Error(data.error || 'Failed to retrieve property data');
                }
            } catch (error) {
                console.error('Error fetching property owner data:', error);
                showNotification(error.message, 'error');

                // Do not populate fields with mock data - leave them empty
                // This ensures users only see real property information or clear error messages
            } finally {
                loadingOverlay.style.display = 'none';
            }
        }

        // Global geocoding state management
        window.geocodingState = {
            fallbackTimer: null,
            googleMapsAttempted: false,
            fallbackInitialized: false,
            currentProvider: null,
            googleMapsBlocked: false
        };

        // Global Google Maps error handler - must be defined before any Google Maps API loading
        window.gm_authFailure = function() {
            console.error('Google Maps authentication failed - using OpenStreetMap fallback');
            window.geocodingState.googleMapsBlocked = true;
            initializeFallbackGeocoding();
        };

        // Handle other Google Maps errors
        window.addEventListener('error', function(e) {
            if (e.message && (e.message.includes('Google Maps') || e.message.includes('InvalidKeyMapError'))) {
                console.warn('🔄 Google Maps API error detected, switching to OpenStreetMap fallback');
                window.geocodingState.googleMapsBlocked = true;
                if (!window.geocodingState.fallbackInitialized) {
                    initializeFallbackGeocoding();
                }
                // Prevent the error from propagating to avoid console clutter
                e.preventDefault();
                return false;
            }
        });

        // Suppress Google Maps console errors to keep console clean
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('Google Maps') ||
                message.includes('InvalidKeyMapError') ||
                message.includes('gm_authFailure') ||
                message.includes('maps.googleapis.com')) {
                // Suppress Google Maps errors, but log our own message
                console.warn('🔄 Google Maps unavailable, using OpenStreetMap fallback');
                return;
            }
            // Call original console.error for other errors
            originalConsoleError.apply(console, args);
        };

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('add-incident');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Check if Google Maps is already blocked by errors
            if (window.geocodingState.googleMapsBlocked) {
                console.log('Google Maps already blocked by errors, using OpenStreetMap');
                initializeFallbackGeocoding();
            } else if (window.apiKeys) {
                console.log('Attempting to load Google Maps API...');
                window.geocodingState.googleMapsAttempted = true;

                try {
                    // Add a pre-check for API key validity (async)
                    window.apiKeys.getKey('google_maps').then(apiKey => {
                        if (!apiKey || apiKey === 'AIzaSyDefaultKeyForDevelopment') {
                            console.warn('Invalid or default Google Maps API key detected, using OpenStreetMap fallback');
                            initializeFallbackGeocoding();
                        } else {
                            window.apiKeys.loadGoogleMapsApi('initAutocomplete', ['places']);

                            // Set a much shorter fallback timer (2 seconds)
                            window.geocodingState.fallbackTimer = setTimeout(() => {
                                if (!window.geocodingState.currentProvider && !window.geocodingState.googleMapsBlocked) {
                                    console.warn('Google Maps API failed to load within 2 seconds, initializing fallback');
                                    initializeFallbackGeocoding();
                                }
                            }, 2000);
                        }
                    }).catch(error => {
                        console.error('Error retrieving Google Maps API key:', error);
                        window.geocodingState.googleMapsBlocked = true;
                        initializeFallbackGeocoding();
                    });
                } catch (error) {
                    console.error('Error loading Google Maps API:', error);
                    window.geocodingState.googleMapsBlocked = true;
                    initializeFallbackGeocoding();
                }
            } else {
                console.warn("API Keys utility not loaded. Using OpenStreetMap fallback.");
                initializeFallbackGeocoding();
            }

            // Initialize fallback immediately if no Google Maps provider is set within 500ms
            // This ensures the address field is always functional
            setTimeout(() => {
                if (!window.geocodingState.currentProvider && !window.geocodingState.fallbackInitialized) {
                    console.log('No geocoding provider initialized within 500ms, using OpenStreetMap fallback');
                    initializeFallbackGeocoding();
                }
            }, 500);

            // Also initialize fallback immediately if user starts typing before Google Maps loads
            const addressInput = document.getElementById('google_address');
            if (addressInput) {
                let userStartedTyping = false;
                addressInput.addEventListener('input', function() {
                    if (!userStartedTyping && !window.geocodingState.currentProvider) {
                        userStartedTyping = true;
                        console.log('User started typing before geocoding initialized, using fallback');
                        if (window.geocodingState.fallbackTimer) {
                            clearTimeout(window.geocodingState.fallbackTimer);
                        }
                        initializeFallbackGeocoding();
                    }
                });
            }

            setupIncidentTypeSelection();
            setupFormValidation();

            // Initialize all dropdowns from API
            populateStatesDropdown();
            populateIncidentStatusesDropdown();
            populateOperatingSystemsDropdown();

            // State selection handler
            const stateSelect = document.getElementById('state_select');
            if (stateSelect) {
                stateSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const stateAbbr = selectedOption.dataset.abbr || '';
                    const stateName = selectedOption.dataset.name || '';

                    // Update hidden state field with abbreviation
                    document.getElementById('state').value = stateAbbr;

                    // Load counties for selected state
                    if (this.value) {
                        populateCountiesDropdown(this.value);
                    } else {
                        // Clear counties if no state selected
                        const countySelect = document.getElementById('county_select');
                        countySelect.innerHTML = '<option value="">Select County</option>';
                        countySelect.disabled = true;
                        document.getElementById('county').value = '';
                    }
                });
            }

            // County selection handler
            const countySelect = document.getElementById('county_select');
            if (countySelect) {
                countySelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const countyName = selectedOption.dataset.name || selectedOption.textContent;

                    // Update hidden county field with the county name
                    document.getElementById('county').value = countyName;
                });
            }

            // Refresh owner data button
            const refreshBtn = document.getElementById('refreshOwnerDataBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    const address = document.getElementById('address').value;
                    const city = document.getElementById('city').value;
                    const state = document.getElementById('state').value;
                    const zip = document.getElementById('zip_code').value;

                    if (!address || !city || !state) {
                        showNotification('Please enter address, city, and state first.', 'warning');
                        return;
                    }

                    fetchPropertyOwnerData(address, city, state, zip);
                });
            }

            // Form submission handler
            const addIncidentForm = document.getElementById('addIncidentForm');
            if (addIncidentForm) {
                addIncidentForm.addEventListener('submit', handleFormSubmission);
            }
        });

        // Handle Form Submission
        function handleFormSubmission(e) {
            e.preventDefault();

            const submitButton = document.getElementById('add-incident-btn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const form = e.target;
            const formData = new FormData(form);

            // Validate required fields
            const requiredFields = ['title', 'address', 'city', 'state', 'county', 'incident_type'];
            const missingFields = [];

            requiredFields.forEach(field => {
                const value = formData.get(field);
                if (!value || value.trim() === '') {
                    missingFields.push(field);
                }
            });

            if (missingFields.length > 0) {
                showNotification(`Please fill in all required fields: ${missingFields.join(', ')}`, 'error');
                return;
            }

            // Find incident type ID based on incident type and subtype
            let incidentTypeId;
            const incidentType = formData.get('incident_type');

            if (incidentType === 'fire') {
                const fireType = formData.get('fire_type');
                switch(fireType) {
                    case 'structure_residential': incidentTypeId = 1; break;
                    case 'structure_commercial': incidentTypeId = 2; break;
                    case 'vehicle_fire': incidentTypeId = 3; break;
                    case 'brush_fire': incidentTypeId = 4; break;
                    case 'electrical_fire': incidentTypeId = 11; break;
                    default: incidentTypeId = 1; // Default to residential
                }
            } else if (incidentType === 'water') {
                const waterType = formData.get('water_type');
                switch(waterType) {
                    case 'water_main_break': incidentTypeId = 5; break;
                    case 'water_service_disruption': incidentTypeId = 6; break;
                    case 'water_quality_issue': incidentTypeId = 7; break;
                    case 'water_pressure_issue': incidentTypeId = 8; break;
                    case 'water_contamination': incidentTypeId = 9; break;
                    case 'water_boil_advisory': incidentTypeId = 10; break;
                    default: incidentTypeId = 5; // Default to main break
                }
            } else {
                showNotification('Please select a valid incident type', 'error');
                return;
            }

            // Find status ID based on status name
            let statusId = 1; // Default to active
            const status = formData.get('status');
            if (status) {
                switch(status.toLowerCase()) {
                    case 'active': statusId = 1; break;
                    case 'pending': statusId = 2; break;
                    case 'critical': statusId = 3; break;
                    case 'resolved': statusId = 4; break;
                    default: statusId = 1;
                }
            }

            // Basic incident data
            const incidentData = {
                title: formData.get('title'),
                address: formData.get('address'),
                city: formData.get('city'),
                state: formData.get('state'),
                zip: formData.get('zip_code'),
                county: formData.get('county'),
                latitude: formData.get('latitude') || null,
                longitude: formData.get('longitude') || null,
                incidentTypeId: incidentTypeId,
                statusId: statusId,
                description: formData.get('add_details') || '',
                severity: formData.get('severity') || 'moderate'
            };

            // Incident detail data (fire/water specific fields + common fields)
            let incidentDetail = {
                // Common fields for all incident types
                propertyOwner: formData.get('owner1'),
                secondaryOwner: formData.get('owner2'),
                ownerAddress: formData.get('ownerAddr'),
                ownerPhone: formData.get('ownerPhone'),
                ownerEmail: formData.get('ownerEmail'),
                dwellingType: formData.get('dwellingType'),
                yearBuilt: formData.get('yearBuilt') ? parseInt(formData.get('yearBuilt')) : null,
                propertyValue: formData.get('propertyValue') ? parseFloat(formData.get('propertyValue').replace(/[$,]/g, '')) : null,
                homeStories: formData.get('home_stories') ? parseInt(formData.get('home_stories')) : null,
                responseDetails: formData.get('response_details'),
                conditionsOnArrival: formData.get('conditions_on_arrival'),
                notes: formData.get('notes')
            };

            if (incidentType === 'fire') {
                // Add fire-specific fields
                Object.assign(incidentDetail, {
                    fireType: formData.get('fire_type'),
                    smokeSeverity: formData.get('smoke_severity'),
                    structureType: formData.get('structure_type'),
                    estimatedDamage: formData.get('estimated_damage'),
                    injuries: parseInt(formData.get('injuries')) || 0,
                    fatalities: parseInt(formData.get('fatalities')) || 0,
                    evacuations: formData.get('evacuations') === 'on',
                    roadClosures: formData.get('road_closures') === 'on',
                    cause: formData.get('cause'),
                    fireSize: formData.get('fire_size'),
                    containmentStatus: formData.get('containment_status'),
                    resourcesDeployed: formData.get('resources_deployed'),
                    weatherConditions: formData.get('weather_conditions'),
                    windSpeed: formData.get('wind_speed'),
                    windDirection: formData.get('wind_direction'),
                    temperature: formData.get('temperature'),
                    humidity: formData.get('humidity')
                });
            } else if (incidentType === 'water') {
                // Add water-specific fields
                Object.assign(incidentDetail, {
                    waterType: formData.get('water_type'),
                    affectedCustomers: parseInt(formData.get('affected_customers')) || 0,
                    estimatedRepairTime: formData.get('estimated_repair_time'),
                    waterPressure: formData.get('water_pressure'),
                    serviceDisruption: formData.get('service_disruption') === 'on',
                    boilWaterAdvisory: formData.get('boil_water_advisory') === 'on',
                    alternativeSupply: formData.get('alternative_supply') === 'on',
                    cause: formData.get('cause'),
                    repairCrew: formData.get('repair_crew'),
                    priority: formData.get('priority'),
                    mainSize: formData.get('main_size'),
                    depth: formData.get('depth'),
                    soilConditions: formData.get('soil_conditions')
                });
            }

            // Show loading state
            loadingOverlay.style.display = 'flex';
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Incident...';

            console.log('Creating incident with data:', incidentData);
            console.log('Incident detail:', incidentDetail);

            // Create the incident
            API.incidents.create({
                ...incidentData,
                incidentDetail: incidentDetail
            })
            .then(response => {
                loadingOverlay.style.display = 'none';
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-plus-circle"></i> Create Incident';

                console.log('Create response:', response);

                if (response && response.id) {
                    showNotification('Incident created successfully!', 'success');

                    // Redirect to the new incident view page after a delay
                    setTimeout(() => {
                        window.location.href = `view-incident.html?id=${response.id}`;
                    }, 1500);
                } else {
                    const errorMsg = response?.message || response?.error || 'Unknown error occurred';
                    showNotification('Error creating incident: ' + errorMsg, 'error');
                }
            })
            .catch(error => {
                loadingOverlay.style.display = 'none';
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-plus-circle"></i> Create Incident';

                console.error('Error creating incident:', error);
                showNotification('Error connecting to server. Please try again.', 'error');
            });
        }
    </script>
</body>
</html>
