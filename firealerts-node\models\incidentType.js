module.exports = (sequelize, DataTypes) => {
  const IncidentType = sequelize.define('incidentType', {
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    category: {
      type: DataTypes.STRING(50),
      defaultValue: 'other'
    },
    icon: {
      type: DataTypes.STRING(50)
    },
    description: {
      type: DataTypes.TEXT
    }
  }, {
    tableName: 'incident_types',
    underscored: true
  });

  return IncidentType;
};
