module.exports = (sequelize, DataTypes) => {
  const UserLocation = sequelize.define('userLocation', {
    address: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    county: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    state: {
      type: DataTypes.STRING(2),
      allowNull: false
    },
    zip: {
      type: DataTypes.STRING(10)
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 8)
    },
    longitude: {
      type: DataTypes.DECIMAL(11, 8)
    },
    locationName: {
      type: DataTypes.STRING(100),
      field: 'location_name'
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_primary'
    }
  }, {
    tableName: 'user_locations',
    underscored: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['county', 'state']
      },
      {
        fields: ['latitude', 'longitude']
      }
    ]
  });

  return UserLocation;
};
