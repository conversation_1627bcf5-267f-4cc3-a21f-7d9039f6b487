/**
 * Schema Validation Script
 * Tests all database operations to ensure schema consistency fixes are working
 */

require('dotenv').config();
const { sequelize } = require('../models');

async function validateSchemaFixes() {
  try {
    console.log('🧪 Starting comprehensive schema validation...');
    
    // Test database connection
    console.log('🔌 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Test all models can be queried without errors
    const models = sequelize.models;
    const testResults = {
      passed: [],
      failed: [],
      warnings: []
    };

    console.log('\n📊 Testing model queries...');
    
    for (const [modelName, model] of Object.entries(models)) {
      try {
        console.log(`  Testing ${modelName}...`);
        
        // Test basic count query
        const count = await model.count();
        console.log(`    ✅ ${modelName}: ${count} records`);
        
        // Test findAll with limit
        const records = await model.findAll({ limit: 1 });
        console.log(`    ✅ ${modelName}: Query successful`);
        
        testResults.passed.push(modelName);
        
      } catch (error) {
        console.log(`    ❌ ${modelName}: ${error.message}`);
        testResults.failed.push({
          model: modelName,
          error: error.message
        });
      }
    }

    // Test specific operations that were failing before
    console.log('\n🎯 Testing previously failing operations...');
    
    try {
      // Test admin user lookup (was failing due to cell_phone column)
      const { user: User } = models;
      const adminUser = await User.findOne({ where: { username: 'admin' } });
      if (adminUser) {
        console.log('  ✅ Admin user query successful');
        testResults.passed.push('admin_user_query');
      } else {
        console.log('  ⚠️ Admin user not found (but query worked)');
        testResults.warnings.push('admin_user_not_found');
      }
    } catch (error) {
      console.log(`  ❌ Admin user query failed: ${error.message}`);
      testResults.failed.push({
        operation: 'admin_user_query',
        error: error.message
      });
    }

    try {
      // Test states query (was failing due to created_at column)
      const { state: State } = models;
      const states = await State.findAll({ limit: 5 });
      console.log(`  ✅ States query successful: ${states.length} states found`);
      testResults.passed.push('states_query');
    } catch (error) {
      console.log(`  ❌ States query failed: ${error.message}`);
      testResults.failed.push({
        operation: 'states_query',
        error: error.message
      });
    }

    try {
      // Test counties query (was failing due to schema mismatch)
      const { county: County } = models;
      const counties = await County.findAll({ limit: 5 });
      console.log(`  ✅ Counties query successful: ${counties.length} counties found`);
      testResults.passed.push('counties_query');
    } catch (error) {
      console.log(`  ❌ Counties query failed: ${error.message}`);
      testResults.failed.push({
        operation: 'counties_query',
        error: error.message
      });
    }

    try {
      // Test companies query (was failing due to subscription_type field)
      const { company: Company } = models;
      const companies = await Company.findAll({ limit: 5 });
      console.log(`  ✅ Companies query successful: ${companies.length} companies found`);
      testResults.passed.push('companies_query');
    } catch (error) {
      console.log(`  ❌ Companies query failed: ${error.message}`);
      testResults.failed.push({
        operation: 'companies_query',
        error: error.message
      });
    }

    // Summary
    console.log('\n📋 Validation Summary:');
    console.log(`  ✅ Passed: ${testResults.passed.length}`);
    console.log(`  ❌ Failed: ${testResults.failed.length}`);
    console.log(`  ⚠️ Warnings: ${testResults.warnings.length}`);

    if (testResults.failed.length > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.failed.forEach(failure => {
        console.log(`  - ${failure.model || failure.operation}: ${failure.error}`);
      });
    }

    if (testResults.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      testResults.warnings.forEach(warning => {
        console.log(`  - ${warning}`);
      });
    }

    const success = testResults.failed.length === 0;
    console.log(`\n🎯 Overall Result: ${success ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    return success;

  } catch (error) {
    console.error('❌ Validation failed:', error);
    return false;
  } finally {
    await sequelize.close();
  }
}

// Run validation if executed directly
if (require.main === module) {
  validateSchemaFixes()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = validateSchemaFixes;
