module.exports = (sequelize, DataTypes) => {
  const Incident = sequelize.define('incident', {
    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    address: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    county: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    state: {
      type: DataTypes.STRING(2),
      allowNull: false
    },
    zip: {
      type: DataTypes.STRING(10)
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 8),
      validate: {
        min: -90,
        max: 90
      }
    },
    longitude: {
      type: DataTypes.DECIMAL(11, 8),
      validate: {
        min: -180,
        max: 180
      }
    },
    incidentDate: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      validate: {
        isDate: true
      },
      field: 'incident_date'
    },
    dispatchInfo: {
      type: DataTypes.TEXT,
      field: 'dispatch_info'
    },
    crossStreet: {
      type: DataTypes.STRING(255),
      field: 'cross_street'
    },
    alarmLevel: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      field: 'alarm_level'
    },
    updates: {
      type: DataTypes.TEXT
    },
    severity: {
      type: DataTypes.ENUM('minor', 'moderate', 'major', 'critical'),
      defaultValue: 'moderate'
    },
    notificationSent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'notification_sent'
    },
    notificationCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      field: 'notification_count'
    },
    userId: {
      type: DataTypes.INTEGER,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'SET NULL'
    },
    incidentTypeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'incident_type_id',
      references: {
        model: 'incident_types',
        key: 'id'
      }
    },
    statusId: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      field: 'status_id',
      references: {
        model: 'statuses',
        key: 'id'
      }
    }
  }, {
    tableName: 'incidents',
    underscored: true,
    indexes: [
      {
        fields: ['incident_date']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['incident_type_id']
      },
      {
        fields: ['status_id']
      },
      {
        fields: ['county']
      },
      {
        fields: ['latitude', 'longitude']
      }
    ]
  });

  return Incident;
};
