/**
 * FireAlerts911 API Utility Functions
 */

// Determine API base URL based on environment
// Use conditional declaration to prevent redeclaration errors
if (typeof API_BASE_URL === 'undefined') {
    var API_BASE_URL = (() => {
        // If we're running from file:// protocol, use localhost
        if (window.location.protocol === 'file:') {
            return 'http://localhost:5000/api';
        }
        // If we're on localhost:3000 (frontend server), point to backend
        if (window.location.hostname === 'localhost' && window.location.port === '3000') {
            return 'http://localhost:5000/api';
        }
        // If we're on localhost:80 (Docker frontend), point to backend
        if (window.location.hostname === 'localhost' && (window.location.port === '80' || window.location.port === '')) {
            return 'http://localhost:5000/api';
        }
        // Otherwise use relative URL (production)
        return '/api';
    })();
}

// Helper function to handle API errors
function handleApiError(error) {
    console.error('API Error:', error);

    if (error.response && error.response.status === 401) {
        // Unauthorized - use centralized auth system
        if (window.AuthCheck && typeof window.AuthCheck.redirectToLogin === 'function') {
            window.AuthCheck.redirectToLogin('session_expired');
        } else {
            // Fallback redirect
            window.location.href = 'login.html?reason=session_expired';
        }
        return;
    }

    return {
        success: false,
        message: error.message || 'An error occurred communicating with the server'
    };
}

// Helper function to get the authentication token
// Note: The backend primarily uses HTTP-only cookies, but we also support Bearer tokens
// for API compatibility. The cookies are automatically sent with credentials: 'include'
function getAuthToken() {
    // Return token for Authorization header (secondary auth method)
    // Primary auth is via HTTP-only cookies which are automatically included
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    // Only log missing token for non-login endpoints to reduce console noise
    if (!token && window.location.pathname !== '/login.html') {
        console.debug('No Bearer token available - relying on HTTP-only cookies for authentication');
    }

    return token;
}

// Check if user is authenticated using the centralized auth system
function isAuthenticated() {
    // Use the centralized authentication check from auth-check.js
    if (window.AuthCheck && typeof window.AuthCheck.isAuthenticated === 'function') {
        return window.AuthCheck.isAuthenticated();
    }

    // Fallback if auth-check.js is not loaded
    const token = getAuthToken();
    const hasLoginState = localStorage.getItem('isLoggedIn') === 'true';
    return !!token || hasLoginState;
}

// Demo mode removed for production security

// Main API request function
async function apiRequest(endpoint, options = {}, retryCount = 0) {
    const url = `${API_BASE_URL}${endpoint}`;
    const maxRetries = 2; // Maximum number of retries
    const timeout = options.timeout || 10000; // Default 10 second timeout

    // Debug logging to identify which endpoint is being called
    console.log(`API Request to: ${url}`, { method: options.method || 'GET', time: new Date().toISOString() });

    // Special logging for subscription plans
    if (endpoint === '/subscription-plans') {
        console.log('Attempting to load subscription plans...', { url, API_BASE_URL })
    }

    // Let the backend handle token validation - don't do client-side checks

    // Set default headers
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };

    // Add auth token if available
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    try {
        // Log request payload for debugging (in development only)
        if (options.body) {
            try {
                const bodyObj = JSON.parse(options.body);
                console.log('Request payload:', {
                    ...bodyObj,
                    password: bodyObj.password ? '********' : undefined
                });
            } catch (e) {
                console.log('Non-JSON payload');
            }
        }

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        // Make the fetch request with timeout
        const response = await fetch(url, {
            ...options,
            headers,
            credentials: 'include', // Include cookies for authentication
            signal: controller.signal
        }).finally(() => clearTimeout(timeoutId));

        // For all responses, try to parse the JSON
        const responseData = await response.json().catch(() => ({}));

        if (!response.ok) {
            // Special handling for 401 Unauthorized
            if (response.status === 401) {
                console.error('Authentication failed: Token may be missing or invalid');
                // Try to get detailed error info
                const errorDetails = responseData.message || responseData.msg || 'Session expired or invalid credentials';

                if (typeof window.showNotification === 'function') {
                    window.showNotification('Authentication error: ' + errorDetails, 'error');
                }

                // Use centralized auth system for logout/redirect
                if (window.AuthCheck && typeof window.AuthCheck.redirectToLogin === 'function') {
                    window.AuthCheck.redirectToLogin('session_expired');
                } else {
                    // Fallback redirect
                    setTimeout(() => {
                        window.location.href = 'login.html?reason=session_expired&details=' +
                            encodeURIComponent(errorDetails);
                    }, 1500);
                }

                return null;
            }

            // Special handling for 403 Forbidden (Permission errors)
            if (response.status === 403) {
                console.error('Permission denied error:', responseData);

                // Let backend handle auth validation - just show the error
                if (typeof window.showNotification === 'function') {
                    window.showNotification('Access denied: ' + (responseData.msg || 'Insufficient permissions'), 'error');
                }
            }

            console.log('API Error Details:', {
                status: response.status,
                url: url,
                responseData: responseData
            });

            // Create a more informative error message
            let errorMsg;

            if (responseData.errors && Array.isArray(responseData.errors)) {
                // Handle express-validator error format
                errorMsg = responseData.errors.map(err => err.msg).join(', ');
            } else {
                errorMsg = responseData.msg || responseData.message || responseData.error || `API Error: ${response.status}`;
            }

            // For 500 or 503 errors, attempt to retry if we haven't hit max retries
            if ((response.status === 500 || response.status === 503) && retryCount < maxRetries) {
                console.log(`Retrying request to ${url} (attempt ${retryCount + 1}/${maxRetries})`);
                // Exponential backoff: wait longer between each retry
                const retryDelay = Math.pow(2, retryCount) * 1000;
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return apiRequest(endpoint, options, retryCount + 1);
            }

            throw new Error(errorMsg);
        }

        // Return the parsed JSON response
        return responseData;
    } catch (error) {
        // Special handling for timeout errors
        if (error.name === 'AbortError') {
            console.warn(`Request to ${url} timed out after ${timeout}ms`);

            // Retry for timeouts if we haven't hit max retries
            if (retryCount < maxRetries) {
                console.log(`Retrying timed-out request to ${url} (attempt ${retryCount + 1}/${maxRetries})`);
                const retryDelay = Math.pow(2, retryCount) * 1000;
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return apiRequest(endpoint, options, retryCount + 1);
            }

            return {
                success: false,
                message: `Request timed out. The database might be slow to respond.`
            };
        }

        return handleApiError(error);
    }
}

// API Interface
const API = {
    // Authentication
    auth: {
        login: async (username, password) => {
            console.log('Login attempt for user:', username);
            // Ensure the credentials are properly formatted
            const credentials = {
                username: username?.trim(),
                password: password
            };

            // Log the structure of what we're sending (don't log actual password in production)
            console.log('Sending credentials structure:', {
                username: credentials.username,
                password: credentials.password ? '[REDACTED]' : 'missing'
            });

            return apiRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify(credentials)
            });
        },
        logout: async () => {
            // Use centralized auth system for logout
            if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                return window.AuthCheck.logout();
            } else {
                // Fallback to direct API call
                return apiRequest('/auth/logout', { method: 'POST' });
            }
        }
    },

    // Incidents
    incidents: {
        getAll: async (params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/incidents?${queryParams}`);
        },
        getById: async (id) => {
            return apiRequest(`/incidents/${id}`);
        },
        create: async (incidentData) => {
            return apiRequest('/incidents', {
                method: 'POST',
                body: JSON.stringify(incidentData)
            });
        },
        update: async (id, incidentData, incidentDetail = null) => {
            const payload = { ...incidentData };
            if (incidentDetail) {
                payload.incidentDetail = incidentDetail;
            }
            return apiRequest(`/incidents/${id}`, {
                method: 'PUT',
                body: JSON.stringify(payload)
            });
        },
        delete: async (id) => {
            return apiRequest(`/incidents/${id}`, {
                method: 'DELETE'
            });
        },
        notify: async (id) => {
            return apiRequest(`/incidents/${id}/notify`, {
                method: 'POST'
            });
        },
        // NEW: Get incident counts by type with enhanced error handling for 500 errors
        getCounts: async () => {
            // Check local storage for cached counts to reduce repeated failing requests
            const cachedCounts = localStorage.getItem('incident_counts_cache');
            const cacheTimestamp = localStorage.getItem('incident_counts_timestamp');
            const cacheAge = cacheTimestamp ? (Date.now() - parseInt(cacheTimestamp)) : null;
            const MAX_CACHE_AGE = 5 * 60 * 1000; // 5 minutes

            if (cachedCounts && cacheAge && cacheAge < MAX_CACHE_AGE) {
                console.log("Using cached incident counts due to previous API error", JSON.parse(cachedCounts));
                return JSON.parse(cachedCounts);
            }

            // First try - direct API call bypassing the problematic endpoint
            try {
                // Use direct connection to the base incidents endpoint which is more reliable
                console.log("Attempting to bypass stats/counts endpoint due to 500 error");
                const response = await API.incidents.getAll({ limit: 100 });
                const incidents = response.data || response;

                if (Array.isArray(incidents)) {
                    let fireCount = 0, waterCount = 0;
                    incidents.forEach(incident => {
                        if (incident.incidentType && incident.incidentType.category === 'fire') {
                            fireCount++;
                        } else if (incident.incidentType && incident.incidentType.category === 'water') {
                            waterCount++;
                        }
                    });

                    const result = {
                        fireCount,
                        waterCount,
                        total: incidents.length,
                        source: 'direct'
                    };

                    // Cache the results to avoid hammering the API with failing requests
                    localStorage.setItem('incident_counts_cache', JSON.stringify(result));
                    localStorage.setItem('incident_counts_timestamp', Date.now().toString());

                    return result;
                }
            } catch (directError) {
                console.error("Direct count method failed:", directError);
            }

            // If direct method fails, try the original stats/counts endpoint as fallback
            // with increased timeout since it's already experiencing issues
            return apiRequest('/incidents/stats/counts', { timeout: 20000 })
                .catch(error => {
                    console.error("Both count methods failed, using emergency fallback", error);

                    // Emergency fallback with fixed data
                    const emergencyFallback = {
                        fireCount: 0,
                        waterCount: 0,
                        total: 0,
                        source: 'emergency_fallback'
                    };

                    // Cache this fallback to avoid repeated failing requests
                    localStorage.setItem('incident_counts_cache', JSON.stringify(emergencyFallback));
                    localStorage.setItem('incident_counts_timestamp', Date.now().toString());

                    return emergencyFallback;
                });
        },
        // NEW: Get all incident locations for map display
        getLocations: async () => {
            return apiRequest('/incidents?includeLocation=true')
                .then(response => {
                    const incidents = response.data || response;
                    if (!Array.isArray(incidents)) {
                        return [];
                    }
                    return incidents.filter(incident =>
                        incident.latitude && incident.longitude ||
                        (incident.address && incident.city)
                    );
                });
        }
    },

    // Companies (formerly Subscribers)
    companies: {
        getAll: async (params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/companies?${queryParams}`);
        },
        getById: async (id) => {
            return apiRequest(`/companies/${id}`);
        },
        create: async (companyData) => {
            return apiRequest('/companies', {
                method: 'POST',
                body: JSON.stringify(companyData)
            });
        },
        update: async (id, companyData) => {
            return apiRequest(`/companies/${id}`, {
                method: 'PUT',
                body: JSON.stringify(companyData)
            });
        },
        delete: async (id) => {
            return apiRequest(`/companies/${id}`, {
                method: 'DELETE'
            });
        },
        getStats: async () => {
            return apiRequest('/companies/stats');
        },
        getUsers: async (companyId) => {
            return apiRequest(`/companies/${companyId}/users`);
        },
        getActivity: async (companyId, params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/companies/${companyId}/activity?${queryParams}`);
        },
        getNotifications: async (companyId, params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/companies/${companyId}/notifications?${queryParams}`);
        },
        updateSubscription: async (id, subscriptionData) => {
            return apiRequest(`/companies/${id}/subscription`, {
                method: 'PUT',
                body: JSON.stringify(subscriptionData)
            });
        },
        updateAlertSettings: async (id, alertSettings) => {
            return apiRequest(`/companies/${id}/alert-settings`, {
                method: 'PUT',
                body: JSON.stringify(alertSettings)
            });
        },
        getCounties: async (companyId) => {
            return apiRequest(`/companies/${companyId}/counties`);
        },
        updateCounties: async (companyId, counties) => {
            return apiRequest(`/companies/${companyId}/counties`, {
                method: 'PUT',
                body: JSON.stringify({ counties })
            });
        },
        // User management methods for companies
        addUser: async (userData) => {
            return apiRequest('/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
        },
        updateUser: async (userId, userData) => {
            return apiRequest(`/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
        },
        getUserById: async (userId) => {
            return apiRequest(`/users/${userId}`);
        },
        deleteUser: async (userId) => {
            return apiRequest(`/users/${userId}`, {
                method: 'DELETE'
            });
        },
        resetUserPassword: async (userId) => {
            return apiRequest(`/users/${userId}/reset-password`, {
                method: 'POST'
            });
        },
        sendBulkNotification: async (notificationData) => {
            return apiRequest('/notifications/bulk', {
                method: 'POST',
                body: JSON.stringify(notificationData)
            });
        }
    },

    // Note: Subscribers API has been removed - use companies API directly

    // Account
    account: {
        getProfile: async () => {
            return apiRequest('/account/profile');
        },
        updateProfile: async (profileData) => {
            return apiRequest('/account/profile', {
                method: 'PUT',
                body: JSON.stringify(profileData)
            });
        },
        updatePassword: async (passwordData) => {
            return apiRequest('/account/password', {
                method: 'PUT',
                body: JSON.stringify(passwordData)
            });
        },
        getActivity: async (options = {}) => {
            const { limit = 20, offset = 0 } = options;
            const params = new URLSearchParams({
                limit: limit.toString(),
                offset: offset.toString()
            });
            return apiRequest(`/account/activity?${params}`);
        },
        getPreferences: async () => {
            return apiRequest('/account/preferences');
        },
        updatePreferences: async (preferencesData) => {
            return apiRequest('/account/preferences', {
                method: 'PUT',
                body: JSON.stringify(preferencesData)
            });
        }
    },

    // Notifications
    notifications: {
        getAll: async (params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/notifications?${queryParams}`);
        },
        markAsRead: async (id) => {
            return apiRequest(`/notifications/${id}/read`, { method: 'PUT' });
        },
        markAllAsRead: async () => {
            return apiRequest('/notifications/read-all', { method: 'PUT' });
        },
        getSettings: async () => {
            return apiRequest('/notifications/settings');
        },
        updateSettings: async (settings) => {
            return apiRequest('/notifications/settings', {
                method: 'PUT',
                body: JSON.stringify(settings)
            });
        },
        getStats: async () => {
            return apiRequest('/notifications/stats/count');
        }
    },

    // Users
    users: {
        getAll: async (params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/users?${queryParams}`);
        },
        getById: async (id) => {
            return apiRequest(`/users/${id}`);
        },
        create: async (userData) => {
            return apiRequest('/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
        },
        update: async (id, userData) => {
            return apiRequest(`/users/${id}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
        },
        delete: async (id) => {
            return apiRequest(`/users/${id}`, {
                method: 'DELETE'
            });
        },
        getStats: async () => {
            return apiRequest('/users/stats');
        }
    },

    // Alerts
    alerts: {
        getAll: async (params = {}) => {
            const queryParams = new URLSearchParams(params).toString();
            return apiRequest(`/alerts?${queryParams}`);
        },
        getById: async (id) => {
            return apiRequest(`/alerts/${id}`);
        },
        create: async (alertData) => {
            return apiRequest('/alerts', {
                method: 'POST',
                body: JSON.stringify(alertData)
            });
        },
        update: async (id, alertData) => {
            return apiRequest(`/alerts/${id}`, {
                method: 'PUT',
                body: JSON.stringify(alertData)
            });
        },
        delete: async (id) => {
            return apiRequest(`/alerts/${id}`, {
                method: 'DELETE'
            });
        }
    },

    // Locations
    locations: {
        getStates: async () => {
            return apiRequest('/locations/states');
        },
        getCounties: async (stateId) => {
            return apiRequest(`/locations/counties?stateId=${stateId}`);
        },
        getZips: async (countyId) => {
            return apiRequest(`/locations/zips?countyId=${countyId}`);
        },
        getCities: async (countyId) => {
            return apiRequest(`/locations/cities?countyId=${countyId}`);
        },
        getProperty: async (address, city, state, zip) => {
            return apiRequest('/locations/property', {
                method: 'POST',
                body: JSON.stringify({ address, city, state, zip })
            });
        },
        geocode: async (address, city, state, zip) => {
            return apiRequest('/locations/geocode', {
                method: 'POST',
                body: JSON.stringify({ address, city, state, zip })
            });
        }
    },

    // Settings
    settings: {
        /**
         * Get all API keys (admin only)
         */
        getApiKeys: async function() {
            try {
                const response = await apiRequest('/settings/api-keys');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Save an API key to the server
         */
        saveApiKey: async function(key, value, description = null, isSecret = true) {
            try {
                const response = await apiRequest('/settings/api-keys', {
                    method: 'POST',
                    body: JSON.stringify({
                        key,
                        value,
                        description,
                        isSecret
                    })
                });
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Delete an API key from the server
         */
        deleteApiKey: async function(key) {
            try {
                const response = await apiRequest(`/settings/api-keys/${key}`, {
                    method: 'DELETE'
                });
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get all system settings (non-secret only)
         */
        getSettings: async function() {
            try {
                const response = await apiRequest('/settings');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Test email delivery using the configured providers
         * @param {string} email - Email address to send test to
         * @param {string} provider - Provider to use ('mailgun', 'smtp', or 'auto')
         */
        testEmailDelivery: async function(email, provider = 'auto') {
            try {
                const response = await apiRequest('/settings/test-email', {
                    method: 'POST',
                    body: JSON.stringify({
                        email,
                        provider
                    })
                });
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Test SMS delivery using the configured Twilio provider
         * @param {string} phoneNumber - Phone number to send test SMS to
         */
        testSmsDelivery: async function(phoneNumber) {
            try {
                const response = await apiRequest('/settings/test-sms', {
                    method: 'POST',
                    body: JSON.stringify({
                        phoneNumber
                    })
                });
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get SMS service configuration status
         */
        getSmsStatus: async function() {
            try {
                const response = await apiRequest('/settings/sms-status');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Check email delivery status
         * Returns availability status for configured email providers
         */
        checkEmailDeliveryStatus: async function() {
            try {
                // This will check if both Mailgun and SMTP are properly configured
                const mailgunKey = await this.getApiKeys().then(keys =>
                    keys.find(k => k.name === 'mailgun_api_key'));

                const smtpSettings = await this.getSettings().then(settings => ({
                    host: settings.smtp_host,
                    user: settings.smtp_user,
                    port: settings.smtp_port
                }));

                return {
                    mailgun: {
                        available: !!mailgunKey,
                        lastUpdated: mailgunKey ? mailgunKey.lastUpdated : null
                    },
                    smtp: {
                        available: !!(smtpSettings.host && smtpSettings.user && smtpSettings.port),
                        settings: smtpSettings.host ? {
                            host: smtpSettings.host,
                            port: smtpSettings.port
                        } : null
                    }
                };
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get all incident types
         */
        getIncidentTypes: async function() {
            try {
                const response = await apiRequest('/settings/types');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get all incident statuses
         */
        getIncidentStatuses: async function() {
            try {
                const response = await apiRequest('/settings/statuses');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get all operating systems
         */
        getOperatingSystems: async function() {
            try {
                const response = await apiRequest('/settings/os');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        },

        /**
         * Get all info types (fire/water specific)
         */
        getInfoTypes: async function() {
            try {
                const response = await apiRequest('/settings/infotypes');
                return response;
            } catch (error) {
                return handleApiError(error);
            }
        }
    }
};

// Export the API interface
window.API = API;

// Add a utility function for showing notifications
function showNotification(message, type = 'info', duration = 4000) {
    if (typeof document === 'undefined') return;

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Add icon based on notification type
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';

    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
    `;

    // Add to document
    document.body.appendChild(notification);

    // Remove after duration
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        notification.style.transition = 'all 0.3s ease-out';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// Make showNotification available globally
window.showNotification = showNotification;
