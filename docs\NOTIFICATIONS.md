# FireAlerts911 Notification System

This comprehensive guide covers the setup, configuration, testing, and troubleshooting of the FireAlerts911 notification system, which handles email, SMS, and push notifications to subscribers.

## 📋 **Table of Contents**

1. [Overview](#overview)
2. [System Components](#system-components)
3. [Email Configuration](#email-configuration)
4. [SMS Configuration](#sms-configuration)
5. [Testing Notification Delivery](#testing-notification-delivery)
6. [API Endpoints](#api-endpoints)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

---

## 🎯 **Overview**

The notification system consists of several components that work together to deliver timely alerts to subscribers when incidents occur in their areas of interest.

### **Notification Flow**
1. New incident is created → Notifications created for matching subscribers
2. Notification worker runs every minute to process pending notifications
3. Worker attempts to deliver via Mailgun first, falls back to SMTP if needed
4. Delivery results are recorded in the notification record

### **Supported Delivery Methods**
- **Email**: Primary (Mailgun) and fallback (SMTP) delivery
- **SMS**: Twilio integration for text message alerts
- **Push**: Future enhancement for mobile app notifications

---

## 🏗️ **System Components**

### **Database Models**
- **Notification**: Tracks individual notification delivery attempts
- **Subscriber**: User accounts with notification preferences (now unified with Users)
- **SubscriberPreference**: Individual notification settings per user
- **Companies**: Organization management for subscriber grouping

### **Worker Process**
- **Scheduler**: Uses node-cron to process pending notifications
- **Delivery Engine**: Handles multiple delivery methods with fallback
- **Error Handling**: Comprehensive logging and retry mechanisms
- **Status Tracking**: Records delivery success/failure for each notification

### **Frontend Integration**
- **Admin Panel**: Configuration interface for delivery settings
- **User Preferences**: Individual notification preference management
- **Testing Tools**: Built-in testing capabilities for administrators

---

## 📧 **Email Configuration**

### **Mailgun Setup (Recommended)**

Mailgun provides reliable email delivery with excellent deliverability rates and comprehensive analytics.

#### **Getting Started with Mailgun**
1. Create a Mailgun account at [mailgun.com](https://www.mailgun.com/)
2. Verify your domain or use the sandbox domain for testing
3. Get your API key and domain from your Mailgun dashboard
4. Configure DNS records for your domain (if using custom domain)

#### **Configuration Methods**

**Method 1: Admin Panel Configuration**
1. Login to FireAlerts911 as an administrator
2. Navigate to "Admin Panel" → "Notification Management"
3. Click "Email Delivery" → "Configure Mailgun"
4. Enter your Mailgun API key and domain
5. Test the configuration using the built-in test button

**Method 2: Environment Variables**
```env
MAILGUN_API_KEY=key-xxxxxxxxxxxxxxxxxxxxxxxxx
MAILGUN_DOMAIN=mg.example.com
```

#### **Domain Configuration**
For production use, configure a custom domain:
1. Add your domain in Mailgun dashboard
2. Configure DNS records as provided by Mailgun:
   - TXT record for domain verification
   - MX records for receiving emails
   - CNAME records for tracking
3. Wait for DNS propagation (up to 48 hours)
4. Verify domain status in Mailgun dashboard

### **SMTP Fallback Configuration**

SMTP serves as a backup delivery method when Mailgun is unavailable.

#### **Configuration Methods**

**Method 1: Admin Panel Configuration**
1. Navigate to "Admin Panel" → "Notification Management"
2. Click "Email Delivery" → "Configure SMTP"
3. Enter your SMTP server details
4. Test the configuration

**Method 2: Environment Variables**
```env
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_username
SMTP_PASSWORD=your_password
SMTP_SECURE=false  # Use true for SSL/TLS (port 465)
```

#### **Common SMTP Providers**
- **Gmail**: smtp.gmail.com:587 (requires app password)
- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **SendGrid**: smtp.sendgrid.net:587
- **Amazon SES**: email-smtp.region.amazonaws.com:587

---

## 📱 **SMS Configuration**

### **Twilio Setup**

Twilio provides reliable SMS delivery with global coverage and competitive pricing.

#### **Getting Started with Twilio**
1. Create a Twilio account at [twilio.com](https://www.twilio.com/)
2. Purchase a phone number for sending SMS
3. Get your Account SID and Auth Token from the dashboard
4. Configure your messaging service (optional but recommended)

#### **Configuration Methods**

**Method 1: Admin Panel Configuration**
1. Navigate to "Admin Panel" → "Notification Management"
2. Click "SMS Delivery" → "Configure Twilio"
3. Enter your Twilio credentials:
   - Account SID
   - Auth Token
   - From Phone Number
4. Test the configuration

**Method 2: Environment Variables**
```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********
```

#### **Phone Number Requirements**
- Use E.164 format: +**********
- Verify the number is SMS-enabled in Twilio
- For production, use a dedicated Twilio phone number
- For testing, you can use Twilio's trial number

---

## 🧪 **Testing Notification Delivery**

The system includes comprehensive testing tools to verify notification delivery before going live.

### **Quick Email Test**
Tests email delivery directly, bypassing the database:

```bash
# Test with environment/stored settings
npm run quick-test -- --email=<EMAIL>

# Test with Mailgun
npm run quick-test -- --email=<EMAIL> --mailgun-key=YOUR_KEY --mailgun-domain=YOUR_DOMAIN

# Test with SMTP
npm run quick-test -- --email=<EMAIL> --smtp-host=smtp.example.com --smtp-user=user --smtp-pass=pass
```

### **Simple Email Test**
Basic email testing using either Mailgun or SMTP:

```bash
# Test SMTP
npm run test-email -- --email=<EMAIL> --type=smtp

# Test Mailgun
npm run test-email -- --email=<EMAIL> --type=mailgun --key=YOUR_KEY --domain=YOUR_DOMAIN
```

### **Full Notification Flow Test**
Simulates the complete notification process including incident creation:

```bash
# Basic test with default options
npm run simulate -- --email=<EMAIL> --county="Test County" --state=FL

# Advanced test with multiple options
npm run simulate -- --type=fire --county="Orange County" --state=FL --subscribers=2 --cleanup

# Test specific incident types
npm run simulate -- --type=water --email=<EMAIL> --county="Miami-Dade County" --state=FL
```

### **Database-Connected Tests**
Test the full system with database connectivity:

```bash
# Test notification with database
npm run test-notification -- --email=<EMAIL>

# Test the complete notification flow
npm run test-flow

# Test with specific user preferences
npm run test-notification -- --user-id=123 --incident-type=fire
```

### **Admin Panel Testing**
Use the built-in testing features in the admin panel:

1. **Email Delivery Test**:
   - Navigate to "Admin Panel" → "Notification Management"
   - Click "Test Email Delivery"
   - Enter test email address
   - Select delivery method (Mailgun or SMTP)
   - Click "Send Test Email"

2. **SMS Delivery Test**:
   - Navigate to "Admin Panel" → "Notification Management"
   - Click "Test SMS Delivery"
   - Enter test phone number
   - Click "Send Test SMS"

3. **Full System Test**:
   - Create a test incident
   - Monitor notification delivery in real-time
   - Check delivery status in the notifications log

---

## 🔌 **API Endpoints**

### **Testing Endpoints**
The system includes API endpoints for testing notification delivery:

#### **Email Testing**
```http
POST /api/settings/test-email
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "email": "<EMAIL>",
  "provider": "mailgun"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test email sent successfully",
  "provider": "mailgun",
  "messageId": "<EMAIL>"
}
```

#### **SMS Testing**
```http
POST /api/settings/test-sms
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "phone": "+**********",
  "message": "Test SMS from FireAlerts911"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test SMS sent successfully",
  "sid": "SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

### **Configuration Endpoints**
```http
# Get notification settings
GET /api/settings/notifications

# Update notification settings
PUT /api/settings/notifications
{
  "mailgun_api_key": "key-xxx",
  "mailgun_domain": "mg.example.com",
  "twilio_account_sid": "ACxxx",
  "twilio_auth_token": "xxx",
  "twilio_from_number": "+**********"
}
```

### **Notification Management**
```http
# Get notification history
GET /api/notifications?limit=50&offset=0

# Get notification details
GET /api/notifications/:id

# Retry failed notification
POST /api/notifications/:id/retry
```

---

## 🔧 **Troubleshooting**

### **Common Email Issues**

#### **Mailgun Authentication Errors**
**Symptoms**: 401 Unauthorized, Invalid API key
**Solutions**:
1. Verify API key is correct and active
2. Check domain is verified in Mailgun
3. Ensure API key has sending permissions
4. Test with Mailgun's API explorer

#### **SMTP Connection Errors**
**Symptoms**: Connection timeout, Authentication failed
**Solutions**:
1. Verify SMTP server settings (host, port, security)
2. Check username and password
3. Enable "Less secure app access" for Gmail
4. Use app-specific passwords for 2FA accounts
5. Check firewall/network restrictions

#### **Email Delivery Issues**
**Symptoms**: Emails not received, marked as spam
**Solutions**:
1. Check spam/junk folders
2. Verify sender domain reputation
3. Configure SPF, DKIM, and DMARC records
4. Use a dedicated IP address (Mailgun)
5. Monitor bounce and complaint rates

### **Common SMS Issues**

#### **Twilio Authentication Errors**
**Symptoms**: 401 Unauthorized, Invalid credentials
**Solutions**:
1. Verify Account SID and Auth Token
2. Check account status and balance
3. Ensure phone number is SMS-enabled
4. Verify geographic permissions

#### **SMS Delivery Failures**
**Symptoms**: Messages not delivered, error codes
**Solutions**:
1. Verify phone number format (E.164)
2. Check recipient's carrier restrictions
3. Monitor Twilio error logs
4. Verify message content compliance
5. Check account balance and limits

### **System-Level Issues**

#### **Notification Worker Not Running**
**Symptoms**: Notifications stuck in pending status
**Solutions**:
1. Check if notification worker is enabled
2. Verify cron job is running
3. Check server logs for errors
4. Restart the application
5. Monitor system resources

#### **Database Connection Issues**
**Symptoms**: Cannot save/retrieve notifications
**Solutions**:
1. Verify database connectivity
2. Check notification table schema
3. Monitor database performance
4. Check for table locks
5. Verify user permissions

### **Debugging Steps**

1. **Check Notification Records**: Look for status and error messages in the database
2. **Verify Configuration**: Ensure all API keys and settings are correct
3. **Use Test Scripts**: Isolate issues with individual test scripts
4. **Monitor Logs**: Check application logs for detailed error messages
5. **Test Connectivity**: Verify network access to external services

---

## 🎯 **Best Practices**

### **Security**
1. **Keep API Keys Secure**: Store in environment variables, not in code
2. **Rotate Credentials**: Regularly update API keys and passwords
3. **Use HTTPS**: Ensure all API communications are encrypted
4. **Monitor Access**: Track API usage and unusual activity
5. **Limit Permissions**: Use least-privilege access for API keys

### **Deliverability**
1. **Domain Verification**: Always verify your sending domain
2. **Authentication Records**: Configure SPF, DKIM, and DMARC
3. **Monitor Reputation**: Track bounce and complaint rates
4. **List Hygiene**: Remove invalid email addresses promptly
5. **Content Quality**: Avoid spam trigger words and excessive formatting

### **Performance**
1. **Batch Processing**: Process notifications in batches for efficiency
2. **Rate Limiting**: Respect API rate limits for external services
3. **Error Handling**: Implement proper retry logic with exponential backoff
4. **Monitoring**: Set up alerts for delivery failures
5. **Caching**: Cache configuration settings to reduce database queries

### **User Experience**
1. **Preference Management**: Allow users to control notification frequency
2. **Unsubscribe Options**: Provide easy unsubscribe mechanisms
3. **Delivery Timing**: Respect user time zones and quiet hours
4. **Content Relevance**: Only send notifications for relevant incidents
5. **Delivery Confirmation**: Provide delivery status to administrators

### **Maintenance**
1. **Regular Testing**: Test notification delivery regularly
2. **Monitor Metrics**: Track delivery rates and response times
3. **Update Dependencies**: Keep notification libraries up to date
4. **Backup Configuration**: Maintain backups of notification settings
5. **Documentation**: Keep configuration and troubleshooting docs current

---

## 📊 **Implementation Status**

✅ **Mailgun Integration**: Complete and ready for production use
✅ **SMTP Fallback**: Configured with comprehensive error handling
✅ **Testing Tools**: Multiple testing scripts and admin panel integration
✅ **API Endpoints**: Full REST API for configuration and testing
✅ **Error Handling**: Comprehensive logging and retry mechanisms
✅ **Documentation**: Complete setup and troubleshooting guides

**The notification system is production-ready and fully integrated with the FireAlerts911 platform.**
