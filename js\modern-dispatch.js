// Function to initialize tooltips
function initTooltips() {
    // Find all elements with data-tooltip attribute
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        // Get tooltip text
        const tooltipText = element.getAttribute('data-tooltip');

        if (!tooltipText) return;

        // Add mouseover event listener
        element.addEventListener('mouseover', function(e) {
            // Create tooltip element if it doesn't exist
            let tooltip = document.getElementById('active-tooltip');

            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.id = 'active-tooltip';
                tooltip.style.position = 'absolute';
                tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                tooltip.style.color = '#fff';
                tooltip.style.padding = '5px 10px';
                tooltip.style.borderRadius = '4px';
                tooltip.style.fontSize = '12px';
                tooltip.style.zIndex = '10000';
                tooltip.style.pointerEvents = 'none';
                document.body.appendChild(tooltip);
            }

            // Set tooltip content
            tooltip.textContent = tooltipText;

            // Position tooltip
            const rect = element.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            tooltip.style.top = (rect.top + scrollTop - tooltip.offsetHeight - 5) + 'px';
            tooltip.style.left = (rect.left + scrollLeft + (rect.width / 2) - (tooltip.offsetWidth / 2)) + 'px';
            tooltip.style.visibility = 'visible';
            tooltip.style.opacity = '1';
        });

        // Add mouseout event listener
        element.addEventListener('mouseout', function() {
            const tooltip = document.getElementById('active-tooltip');
            if (tooltip) {
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            }
        });
    });
}

// Make sure the function is globally accessible
window.initTooltips = initTooltips;

document.addEventListener('DOMContentLoaded', function() {
    // Define the global initTooltips function right away
    if (typeof window.initTooltips !== 'function') {
        console.log('Initializing tooltips function globally');
        // If not defined yet, define it again to ensure it's available
        window.initTooltips = function() {
            // Just call the original function if available
            if (typeof initTooltips === 'function') {
                return initTooltips();
            } else {
                console.warn('Original initTooltips function not available');
            }
        };
    }

    // Toggle sidebar
    const toggleSidebarBtn = document.querySelector('.toggle-sidebar');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');

    // Sidebar rendering is now handled by shared-utils.js and auth-check.js

    if (toggleSidebarBtn && sidebar && mainContent) {
        toggleSidebarBtn.addEventListener('click', function() {
            // On mobile, toggle mobile-open class
            if (window.innerWidth <= 576) {
                sidebar.classList.toggle('mobile-open');
            } else {
                // On desktop/tablet, toggle collapsed state
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
    }

    // Mobile sidebar toggle (legacy support)
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

    if (mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
        });
    }

    // Ensure hamburger menu is visible on mobile
    function updateHamburgerVisibility() {
        const hamburger = document.querySelector('.toggle-sidebar');
        if (hamburger) {
            if (window.innerWidth <= 576) {
                hamburger.style.display = 'flex';
                hamburger.style.alignItems = 'center';
                hamburger.style.justifyContent = 'center';
                hamburger.style.minWidth = '44px';
                hamburger.style.minHeight = '44px';
            } else {
                hamburger.style.display = '';
                hamburger.style.alignItems = '';
                hamburger.style.justifyContent = '';
                hamburger.style.minWidth = '';
                hamburger.style.minHeight = '';
            }
        }
    }

    // Update hamburger visibility on load and resize
    updateHamburgerVisibility();
    window.addEventListener('resize', updateHamburgerVisibility);

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        try {
            const viewport = window.innerWidth;
            const sidebar = document.querySelector('.sidebar'); // Re-query to ensure it exists on this page

            if (!sidebar || viewport > 576) {
                return; // Exit early if no sidebar or not mobile view
            }

            // Safely check if the clicked element is within these containers
            const isClickedInsideSidebar = event.target && typeof event.target.closest === 'function' && event.target.closest('.sidebar');
            const isClickedOnMenuBtn = event.target && typeof event.target.closest === 'function' && event.target.closest('.mobile-menu-btn');
            const isClickedOnToggleBtn = event.target && typeof event.target.closest === 'function' && event.target.closest('.toggle-sidebar');

            // Only close the sidebar if it's open and the click is outside relevant elements
            if (!isClickedInsideSidebar && !isClickedOnMenuBtn && !isClickedOnToggleBtn &&
                sidebar && sidebar.classList && sidebar.classList.contains('mobile-open')) {
                sidebar.classList.remove('mobile-open');
            }
        } catch (err) {
            console.log('Error in sidebar click handler:', err);
            // Silently catch errors to prevent breaking the page
        }
    });

    // Active state management is now handled by shared-utils.js

    // Setup submenu functionality
    try {
        // Toggle submenu
        const hasSubmenu = document.querySelectorAll('.has-submenu');

        hasSubmenu.forEach(item => {
            item.addEventListener('click', function(e) {
                if (e.target.closest('.submenu')) return;

                e.preventDefault();
                const submenu = this.querySelector('.submenu');

                if (submenu) {
                    submenu.classList.toggle('active');

                    // Toggle icon
                    const icon = this.querySelector('.submenu-icon i');
                    if (icon) {
                        icon.classList.toggle('fa-chevron-down');
                        icon.classList.toggle('fa-chevron-up');
                    }
                }
            });
        });
    } catch (err) {
        console.log('Error setting up sidebar:', err);
        // Silently catch errors to prevent breaking the page
    }

    // Initialize incident filtering
    initIncidentFilter();

    // Initialize maps if the element exists
    if (document.getElementById('dispatch-map')) {
        initMap();
    }

    // Initialize data tables if they exist
    if (document.querySelector('.data-table')) {
        initDataTables();
    }

    // Initialize form validation
    initFormValidation();

    try {
        // Initialize tooltips (using try-catch to prevent errors if initTooltips is not defined)
        if (typeof initTooltips === 'function') {
            initTooltips();
        } else {
            console.warn('initTooltips function is not available');
        }
    } catch (e) {
        console.error('Error initializing tooltips:', e);
    }

    // Check if we're on the incidents page
    if (document.querySelector('.incidents-container')) {
        loadIncidents();

        // Setup filter form
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const filters = {
                    search: document.getElementById('search-input')?.value || '',
                    type: document.getElementById('type-filter')?.value || '',
                    status: document.getElementById('status-filter')?.value || ''
                };

                loadIncidents(filters);
            });
        }
    }

    fixWaterIncidentStyling();
    setupOwnerDataRefresh();
});

// Incident filtering functionality
function initIncidentFilter() {
    const filterContainer = document.querySelector('.filter-container');

    // Only proceed if the filter container exists on this page
    if (!filterContainer) return;

    const filterToggle = document.querySelector('.filter-toggle');
    const filterForm = document.getElementById('filter-form');
    const clearFiltersBtn = document.querySelector('.clear-filters');

    // Toggle filter visibility
    if (filterToggle) {
        filterToggle.addEventListener('click', function() {
            filterContainer.classList.toggle('show');

            // Toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-filter');
                icon.classList.toggle('fa-times');
            }
        });
    }

    // Clear filters
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Reset all filter form inputs
            if (filterForm) {
                filterForm.reset();

                // If we're on the incidents page, reload without filters
                if (document.querySelector('.incidents-container')) {
                    loadIncidents();
                }
            }
        });
    }

    // Set up date range pickers if they exist
    const dateRangeInputs = document.querySelectorAll('.date-range');
    dateRangeInputs.forEach(input => {
        if (window.flatpickr) {
            window.flatpickr(input, {
                dateFormat: "Y-m-d",
                allowInput: true
            });
        }
    });
}

// Function to initialize form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');

    if (!forms.length) return;

    forms.forEach(form => {
        // Add submit handler
        form.addEventListener('submit', function(e) {
            let isValid = true;

            // Validate required fields
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');

                    // Show error message if exists
                    const errorMsg = document.getElementById(`${field.id}-error`);
                    if (errorMsg) {
                        errorMsg.classList.add('show');
                    }
                }
            });

            // Validate email fields
            const emailFields = form.querySelectorAll('[type="email"]');
            emailFields.forEach(field => {
                if (field.value.trim() && !validateEmail(field.value.trim())) {
                    isValid = false;
                    field.classList.add('error');

                    // Show error message if exists
                    const errorMsg = document.getElementById(`${field.id}-error`);
                    if (errorMsg) {
                        errorMsg.classList.add('show');
                    }
                }
            });

            // If form is invalid, prevent submission
            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();

                // Show validation message if available
                const validationMsg = form.querySelector('.validation-message');
                if (validationMsg) {
                    validationMsg.classList.add('show');

                    // Hide after 5 seconds
                    setTimeout(() => {
                        validationMsg.classList.remove('show');
                    }, 5000);
                }
            }
        });

        // Add input handlers to clear errors on input
        const formFields = form.querySelectorAll('input, select, textarea');
        formFields.forEach(field => {
            field.addEventListener('input', function() {
                this.classList.remove('error');

                // Hide error message if exists
                const errorMsg = document.getElementById(`${field.id}-error`);
                if (errorMsg) {
                    errorMsg.classList.remove('show');
                }
            });
        });
    });
}

// Helper function to validate email
function validateEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

// Helper function to format dates
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid

    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };

    return date.toLocaleString('en-US', options);
}

// Helper function to get the authentication token
// Added to fix undefined reference errors
function getAuthToken() {
    return localStorage.getItem('token') || sessionStorage.getItem('token') || null;
}

/**
 * Incident Management Functions
 */

// Function to load incident data
function loadIncidents(filters = {}) {
    // Show loading state
    const tableBody = document.querySelector('.data-table tbody');
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">Loading incidents...</td></tr>';
    }

    // Use the API interface instead of direct fetch
    API.incidents.getAll(filters)
        .then(data => {
            console.log('Dispatch incidents data received:', data);

            // Handle standardized API response format
            let incidents = [];
            if (data && data.success && data.data && Array.isArray(data.data)) {
                incidents = data.data;
            } else if (data && data.data && Array.isArray(data.data)) {
                incidents = data.data;
            } else if (data && data.incidents && Array.isArray(data.incidents)) {
                incidents = data.incidents;
            } else if (Array.isArray(data)) {
                incidents = data;
            }

            if (incidents && incidents.length > 0) {
                updateIncidentsTable(incidents);
            } else {
                showEmptyState('No incidents found matching your criteria.');
            }
        })
        .catch(error => {
            console.error('Error fetching incidents:', error);
            showEmptyState('Error loading incidents. Please try again.');

            // For development purposes, show error in console but don't use mock data in production
            console.error('API Error:', error);
        });
}

// Function to update the incidents table with data
function updateIncidentsTable(incidents) {
    const tableBody = document.querySelector('.data-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    incidents.forEach(incident => {
        // Get incident type and status from their respective objects
        const incidentType = incident.incidentType ? incident.incidentType.name : 'Unknown';
        const incidentCategory = incident.incidentType ? incident.incidentType.category : 'other';
        const statusName = incident.status ? incident.status.name : 'Unknown';
        const statusColor = incident.status ? incident.status.color : '#999';

        const row = document.createElement('tr');

        row.innerHTML = `
            <td data-column="id">${incident.id}</td>
            <td data-column="type">
                <span class="type-badge type-${incidentCategory}">${incidentType}</span>
            </td>
            <td data-column="title">${incident.title}</td>
            <td data-column="location">${incident.address}, ${incident.city}</td>
            <td data-column="date">${formatDate(incident.incidentDate)}</td>
            <td data-column="status">
                <span class="status-badge" style="background-color: ${statusColor};">${statusName}</span>
            </td>
            <td>
                <div class="incident-actions">
                    <a href="edit-incident.html?id=${incident.id}" class="btn-icon" data-tooltip="Edit">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="view-incident.html?id=${incident.id}" class="btn-icon" data-tooltip="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn-icon send-notification-btn" data-id="${incident.id}" data-tooltip="Send Notification">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="btn-icon delete-incident-btn" data-id="${incident.id}" data-tooltip="Delete">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Add event listeners for action buttons
    document.querySelectorAll('.send-notification-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const incidentId = this.getAttribute('data-id');
            sendIncidentNotification(incidentId);
        });
    });

    document.querySelectorAll('.delete-incident-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const incidentId = this.getAttribute('data-id');
            deleteIncident(incidentId);
        });
    });
}

// Function to show empty state when no incidents are found
function showEmptyState(message = 'No incidents found.') {
    const tableBody = document.querySelector('.data-table tbody');
    if (tableBody) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px;">
                    <div style="color: var(--text-secondary); font-size: 16px;">
                        <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>${message}</p>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Function to send notification for an incident
function sendIncidentNotification(incidentId) {
    // Show confirmation dialog
    if (!confirm('Send notification to all subscribers for this incident?')) {
        return;
    }

    // Show loading state
    const btn = document.querySelector(`.send-notification-btn[data-id="${incidentId}"]`);
    if (btn) {
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    }

    // Use API interface consistently with the rest of the application
    // instead of direct fetch calls
    API.incidents.notify(incidentId)
        .then(data => {
            showNotification('Notification sent to subscribers!', 'success');
        })
        .catch(error => {
            console.error('Error sending notification:', error);
            showNotification('Failed to send notification. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            const btn = document.querySelector(`.send-notification-btn[data-id="${incidentId}"]`);
            if (btn) {
                btn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                btn.disabled = false;
            }
        });
}

// Function to delete an incident
function deleteIncident(incidentId) {
    // Show confirmation dialog
    if (!confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
        return;
    }

    // Show loading state
    const btn = document.querySelector(`.delete-incident-btn[data-id="${incidentId}"]`);
    if (btn) {
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    }

    // Use API interface to delete the incident
    API.incidents.delete(incidentId)
        .then(data => {
            showNotification('Incident deleted successfully!', 'success');
            loadIncidents(); // Reload incidents to update the list
        })
        .catch(error => {
            console.error('Error deleting incident:', error);
            showNotification('Failed to delete incident. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state if the element still exists
            const btn = document.querySelector(`.delete-incident-btn[data-id="${incidentId}"]`);
            if (btn) {
                btn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                btn.disabled = false;
            }
        });
}

// Function to capitalize first letter
function capitalize(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

// Function to handle refreshing owner data
function setupOwnerDataRefresh() {
    // If we have the new integration script loaded, don't double-bind
    if (window.fireAlerts && window.fireAlerts.maps) {
        return;
    }

    const refreshBtn = document.getElementById('refreshOwnerDataBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            const address = document.getElementById('address').value;
            const city = document.getElementById('city').value;
            const state = document.getElementById('state').value;
            const zip = document.getElementById('zip_code').value;

            if (!address || !city || !state) {
                showNotification('Please complete the address fields first', 'warning');
                return;
            }

            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            this.disabled = true;

            // Use API interface to get property data
            fetch(`/api/locations/property`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify({
                    address,
                    city,
                    state,
                    zip
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to retrieve owner data');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Populate owner fields with null checks
                    const owner1Field = document.getElementById('owner1');
                    if (owner1Field) owner1Field.value = data.property.owner1 || '';

                    const owner2Field = document.getElementById('owner2');
                    if (owner2Field) owner2Field.value = data.property.owner2 || '';

                    const ownerAddrField = document.getElementById('ownerAddr');
                    if (ownerAddrField) ownerAddrField.value = data.property.ownerAddress || '';

                    const ownerPhoneField = document.getElementById('ownerPhone');
                    if (ownerPhoneField) ownerPhoneField.value = data.property.ownerPhone || '';

                    const ownerEmailField = document.getElementById('ownerEmail');
                    if (ownerEmailField) ownerEmailField.value = data.property.ownerEmail || '';

                    const dwellingTypeField = document.getElementById('dwellingType');
                    if (dwellingTypeField) dwellingTypeField.value = data.property.dwellingType || '';

                    // Display the owner info card (with null checks)
                    const ownerInfoCard = document.getElementById('ownerInfoCard');
                    if (ownerInfoCard) ownerInfoCard.style.display = 'block';

                    const ownerInfoDescription = document.querySelector('.owner-info-description');
                    if (ownerInfoDescription) ownerInfoDescription.style.display = 'none';

                    const ownerInfoDetails = document.querySelector('.owner-info-details');
                    if (ownerInfoDetails) ownerInfoDetails.style.display = 'block';

                    showNotification('Owner information loaded successfully', 'success');
                } else {
                    showNotification('Failed to load owner information', 'error');
                }
            })
            .catch(error => {
                console.error('Error fetching owner data:', error);
                showNotification('Failed to retrieve property data from server', 'error');

                // Do not fallback to mock data in production
            })
            .finally(() => {
                // Reset button state
                this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Owner Data';
                this.disabled = false;
            });
        });
    }
}

// Function to fix styling for water incident page
function fixWaterIncidentStyling() {
    if (window.location.href.includes('add-water-incident.html')) {
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.className = 'btn btn-primary';
            submitBtn.style.backgroundColor = 'var(--accent-blue)';
            submitBtn.style.borderColor = 'var(--accent-blue)';
        }
    }
}

// Fix sidebar active states
function fixSidebarActiveState() {
    const currentPath = window.location.pathname;
    const pageName = currentPath.split('/').pop();

    // Remove active class from all sidebar items
    document.querySelectorAll('.sidebar .nav-menu .nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Active class management is now handled by shared-utils.js
}

/**
 * Gets a street view image for a given address
 * Uses backend API to generate Google Street View URL
 */
function getPropertyStreetView(address, city, state, callback) {
    // If we have the new integration script loaded, use that instead
    if (window.fireAlerts && window.fireAlerts.maps && window.fireAlerts.maps.getPropertyStreetView) {
        console.log('Using Leaflet map implementation');
        // Use the Leaflet implementation
        window.fireAlerts.maps.getPropertyStreetView(address, city, state, callback);
        return;
    }

    fetch('/api/locations/streetview', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
            address,
            city,
            state
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to retrieve street view image');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            callback(data.imageUrl);
        } else {
            throw new Error('No image URL provided');
        }
    })
    .catch(error => {
        console.error('Error fetching street view:', error);
        // Inform user of the error
        callback(null);
        showNotification('Unable to load property image', 'error');
    });
}

// Datatable initialization
function initDataTables() {
    const dataTables = document.querySelectorAll('.data-table');
    if (!dataTables.length) return;

    // Simple data tables
    dataTables.forEach(table => {
        // Add sorting functionality if needed
        if (table.classList.contains('sortable')) {
            const headers = table.querySelectorAll('thead th[data-sort]');
            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const sortDir = this.getAttribute('data-sort-dir') || 'asc';
                    const sortBy = this.getAttribute('data-sort');

                    // Clear all sort indicators
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });

                    // Set new sort direction
                    this.classList.add(sortDir === 'asc' ? 'sort-asc' : 'sort-desc');
                    this.setAttribute('data-sort-dir', sortDir === 'asc' ? 'desc' : 'asc');

                    // Custom sort if we're on incidents page
                    if (document.querySelector('.incidents-container')) {
                        const filters = {
                            sort: sortBy,
                            dir: sortDir
                        };
                        loadIncidents(filters);
                    }
                });
            });
        }

        // Add row selection functionality
        if (table.classList.contains('selectable')) {
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger on button clicks within the row
                    if (e.target.closest('button') || e.target.closest('a')) {
                        return;
                    }

                    // Toggle selected class
                    this.classList.toggle('selected');

                    // Update selected count if there's a counter element
                    const counter = document.querySelector('.selected-count');
                    if (counter) {
                        const selectedCount = table.querySelectorAll('tbody tr.selected').length;
                        counter.textContent = selectedCount;
                        counter.style.display = selectedCount > 0 ? 'inline-block' : 'none';

                        // Show/hide bulk actions
                        const bulkActions = document.querySelector('.bulk-actions');
                        if (bulkActions) {
                            bulkActions.style.display = selectedCount > 0 ? 'flex' : 'none';
                        }
                    }
                });
            });
        }
    });
}

// Function to show success/error message
function showNotification(message, type = 'info') {
    // Check if we already have a notification container in the DOM
    let notification = document.getElementById('notification');

    // If existing notification element exists, use it
    if (notification) {
        const messageElement = document.getElementById('notification-message');
        const iconElement = notification.querySelector('i');

        if (!messageElement || !iconElement) {
            console.error('Notification elements not found in the DOM');
            return;
        }

        // Update notification content
        messageElement.textContent = message;

        // Reset classes
        notification.className = 'notification';
        notification.classList.add(type);

        // Set icon based on notification type
        if (type === 'success') {
            iconElement.className = 'fas fa-check-circle';
        } else if (type === 'error') {
            iconElement.className = 'fas fa-exclamation-circle';
        } else if (type === 'warning') {
            iconElement.className = 'fas fa-exclamation-triangle';
        } else {
            iconElement.className = 'fas fa-info-circle';
        }

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }, 100);
    }
    // Otherwise create a new notification element
    else {
        notification = document.createElement('div');
        notification.id = 'notification';
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
            <span id="notification-message">${message}</span>
        `;

        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '12px 20px';
        notification.style.color = '#fff';
        notification.style.fontSize = '14px';
        notification.style.maxWidth = '350px';
        notification.style.boxShadow = '0 3px 6px rgba(0, 0, 0, 0.16)';
        notification.style.transform = 'translateX(120%)';
        notification.style.transition = 'transform 0.3s ease-in-out';
        notification.style.zIndex = '1000';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';

        // Add type-specific styles
        if (type === 'success') {
            notification.style.backgroundColor = '#4caf50';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#f44336';
        } else if (type === 'warning') {
            notification.style.backgroundColor = '#ff9800';
        } else {
            notification.style.backgroundColor = '#2196f3';
        }

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(120%)';

                // Remove after transition
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }, 100);
    }

    // Also log to console for debugging
    console.log(`Notification (${type}):`, message);
}

// Expose the notification function globally so it can be used by other scripts
window.modernDispatchShowNotification = showNotification;

/**
 * Utility function to set the current user as admin
 * This helps fix admin panel visibility without requiring logout/login
 */
function setCurrentUserAsAdmin() {
    try {
        // Get current user info
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) {
            showNotification('No user information found. Please log in first.', 'warning');
            return false;
        }

        const userInfo = JSON.parse(userInfoStr);

        // Update role to admin
        userInfo.role = 'admin';

        // Save back to localStorage
        localStorage.setItem('userInfo', JSON.stringify(userInfo));

        // Update the sidebar
        renderRoleBasedSidebar();

        showNotification('Admin role granted. Admin Panel should now be visible.', 'success');
        return true;
    } catch (e) {
        console.error('Error setting admin role:', e);
        showNotification('Error updating user role.', 'error');
        return false;
    }
}

// Make the function globally available
window.setCurrentUserAsAdmin = setCurrentUserAsAdmin;

// Legacy renderRoleBasedSidebar function removed - now using shared-utils.js

// Initialize maps for the dispatch system
function initMap() {
  // Check if we have a Leaflet implementation
  if (window.fireAlerts && window.fireAlerts.maps && typeof window.fireAlerts.maps.initMap === 'function') {
    console.log('Using Leaflet map implementation');
    // Use the Leaflet implementation
    window.fireAlerts.maps.initMap();
    return;
  }

  // Only reach here if Leaflet isn't available
  console.log('No Leaflet implementation found, map may not initialize properly');

  // Make sure the map container exists before trying to initialize
  const mapContainer = document.getElementById('dispatch-map');
  if (!mapContainer) {
    console.log('Map container not found on this page.');
    return;
  }

  // Show fallback message
  mapContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: var(--text-secondary);"><i class="fas fa-map-marked-alt" style="font-size: 48px; margin-bottom: 15px;"></i><p>Map implementation not available. Please check that the correct map library is loaded.</p></div>';
}

// System Logs Tab: Dynamic Loading with Pagination
// Use conditional declaration to prevent redeclaration errors
if (typeof currentLogsPage === 'undefined') {
    var currentLogsPage = 1;
}
if (typeof logsPagination === 'undefined') {
    var logsPagination = null;
}

function loadSystemLogs(page = 1) {
    const tableBody = document.querySelector('#logs .data-table tbody');
    const logsCount = document.getElementById('logsCount');
    const paginationDiv = document.getElementById('logsPagination');

    if (!tableBody) return;

    // Show loading state
    tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">Loading logs...</td></tr>';
    if (logsCount) logsCount.textContent = 'Loading logs...';

    const limit = 20;
    const offset = (page - 1) * limit;

    fetch(`/api/logs?limit=${limit}&offset=${offset}`, { credentials: 'include' })
        .then(res => res.json())
        .then(response => {
            tableBody.innerHTML = ''; // Clear loading message

            const logs = response.data || response || [];
            logsPagination = response.pagination || null;
            currentLogsPage = page;

            // Update logs count display
            if (logsPagination && logsCount) {
                const start = logsPagination.offset + 1;
                const end = Math.min(logsPagination.offset + logsPagination.limit, logsPagination.total);
                logsCount.textContent = `Showing ${start}-${end} of ${logsPagination.total} logs`;
            } else if (logsCount) {
                logsCount.textContent = `${logs.length} logs`;
            }

            if (!Array.isArray(logs) || logs.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">No logs found.</td></tr>';
                if (paginationDiv) paginationDiv.style.display = 'none';
                return;
            }

            logs.forEach(log => {
                const user = log.user ? (log.user.email || log.user.username || log.user.id) : 'System';
                const level = log.severity ? log.severity.charAt(0).toUpperCase() + log.severity.slice(1) : '';
                const badgeClass = {
                    'error': 'status-critical',
                    'warning': 'status-pending',
                    'info': 'status-active',
                    'debug': 'status-debug',
                    'critical': 'status-critical'
                }[log.severity] || 'status-active';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${log.created_at ? new Date(log.created_at).toLocaleString() : ''}</td>
                    <td><span class="status-badge ${badgeClass}">${level}</span></td>
                    <td>${log.action || ''} ${log.details ? `<br><small>${log.details}</small>` : ''}</td>
                    <td>${log.module || ''}</td>
                    <td>${user}</td>
                    <td><div class="incident-actions"><button class="btn-icon" data-tooltip="Details"><i class="fas fa-search"></i></button></div></td>
                `;
                tableBody.appendChild(row);
            });

            // Show pagination if there are multiple pages
            if (logsPagination && logsPagination.totalPages > 1 && paginationDiv) {
                renderLogsPagination();
                paginationDiv.style.display = 'block';
            } else if (paginationDiv) {
                paginationDiv.style.display = 'none';
            }

            if (typeof window.initTooltips === 'function') window.initTooltips();
        })
        .catch(err => {
            tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center; color: red;">Failed to load logs.</td></tr>';
            if (logsCount) logsCount.textContent = 'Error loading logs';
            console.error('Error loading system logs:', err);
        });
}

// Function to render logs pagination controls
function renderLogsPagination() {
    if (!logsPagination) return;

    const pageNumbers = document.getElementById('logsPageNumbers');
    const prevPage = document.getElementById('prevLogsPage');
    const nextPage = document.getElementById('nextLogsPage');

    if (!pageNumbers || !prevPage || !nextPage) return;

    // Clear existing page numbers
    pageNumbers.innerHTML = '';

    const currentPage = logsPagination.currentPage;
    const totalPages = logsPagination.totalPages;

    // Previous button state
    if (currentPage <= 1) {
        prevPage.style.opacity = '0.5';
        prevPage.style.pointerEvents = 'none';
    } else {
        prevPage.style.opacity = '1';
        prevPage.style.pointerEvents = 'auto';
    }

    // Next button state
    if (currentPage >= totalPages) {
        nextPage.style.opacity = '0.5';
        nextPage.style.pointerEvents = 'none';
    } else {
        nextPage.style.opacity = '1';
        nextPage.style.pointerEvents = 'auto';
    }

    // Calculate page range to show
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // Adjust range if we're near the beginning or end
    if (endPage - startPage < 4) {
        if (startPage === 1) {
            endPage = Math.min(totalPages, startPage + 4);
        } else if (endPage === totalPages) {
            startPage = Math.max(1, endPage - 4);
        }
    }

    // Add page numbers
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `btn btn-outline ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.style.padding = '8px 12px';
        pageBtn.style.minWidth = '40px';

        if (i === currentPage) {
            pageBtn.style.backgroundColor = 'var(--accent-blue)';
            pageBtn.style.color = 'white';
        }

        pageBtn.addEventListener('click', () => {
            if (i !== currentPage) {
                loadSystemLogs(i);
            }
        });

        pageNumbers.appendChild(pageBtn);
    }
}

// Hook: Load logs when System Logs tab is activated
if (document.querySelector('.tab-button[data-tab="logs"]')) {
    document.querySelector('.tab-button[data-tab="logs"]').addEventListener('click', loadSystemLogs);
}
// Optionally, load logs immediately if logs tab is active on page load
if (document.querySelector('.tab-button[data-tab="logs"].active')) {
    loadSystemLogs();
}

// Add event listeners for logs pagination
document.addEventListener('DOMContentLoaded', function() {
    const prevLogsPage = document.getElementById('prevLogsPage');
    const nextLogsPage = document.getElementById('nextLogsPage');

    if (prevLogsPage) {
        prevLogsPage.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentLogsPage > 1) {
                loadSystemLogs(currentLogsPage - 1);
            }
        });
    }

    if (nextLogsPage) {
        nextLogsPage.addEventListener('click', function(e) {
            e.preventDefault();
            if (logsPagination && currentLogsPage < logsPagination.totalPages) {
                loadSystemLogs(currentLogsPage + 1);
            }
        });
    }
});

// Dispatcher Management Functions
// Use conditional declaration to prevent redeclaration errors
if (typeof currentDispatchersPage === 'undefined') {
    var currentDispatchersPage = 1;
}
if (typeof dispatchersPagination === 'undefined') {
    var dispatchersPagination = null;
}

function loadDispatchers(page = 1) {
    const tableBody = document.getElementById('dispatchersTableBody');
    const dispatcherCount = document.getElementById('dispatcherCount');
    const paginationDiv = document.getElementById('dispatchersPagination');

    if (!tableBody) return;

    // Show loading state
    tableBody.innerHTML = '<tr><td colspan="7" style="text-align:center;">Loading dispatchers...</td></tr>';
    if (dispatcherCount) dispatcherCount.textContent = 'Loading dispatchers...';

    const limit = 20;
    const offset = (page - 1) * limit;

    // Fetch dispatchers using the existing users API with role filter
    fetch(`/api/users?role=dispatcher&limit=${limit}&offset=${offset}`, { credentials: 'include' })
        .then(res => res.json())
        .then(response => {
            tableBody.innerHTML = ''; // Clear loading message

            const dispatchers = Array.isArray(response) ? response : (response.data || []);
            // Create pagination info if not provided
            if (!response.pagination && Array.isArray(response)) {
                dispatchersPagination = {
                    total: dispatchers.length,
                    currentPage: 1,
                    totalPages: 1,
                    limit: limit,
                    offset: 0
                };
            } else {
                dispatchersPagination = response.pagination || null;
            }
            currentDispatchersPage = page;

            // Update dispatcher count display
            if (dispatchersPagination && dispatcherCount) {
                const start = dispatchersPagination.offset + 1;
                const end = Math.min(dispatchersPagination.offset + dispatchersPagination.limit, dispatchersPagination.total);
                dispatcherCount.textContent = `Showing ${start}-${end} of ${dispatchersPagination.total} FireAlerts911 dispatchers`;
            } else if (dispatcherCount) {
                dispatcherCount.textContent = `${dispatchers.length} FireAlerts911 dispatchers`;
            }

            if (!Array.isArray(dispatchers) || dispatchers.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7">
                            <div style="text-align: center; padding: 40px;">
                                <i class="fas fa-headset" style="font-size: 48px; color: var(--text-secondary); margin-bottom: 15px;"></i>
                                <h4 style="margin: 0 0 10px 0; color: var(--text-light);">No FireAlerts911 Dispatchers Found</h4>
                                <p style="margin: 0 0 20px 0; color: var(--text-secondary);">No FireAlerts911 dispatcher employees have been created yet.</p>
                                <button class="btn btn-primary" onclick="showAddDispatcherModal()">
                                    <i class="fas fa-user-plus"></i> Add First Dispatcher
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                if (paginationDiv) paginationDiv.style.display = 'none';
                return;
            }

            dispatchers.forEach(dispatcher => {
                const fullName = `${dispatcher.firstName || ''} ${dispatcher.lastName || ''}`.trim();
                const lastLogin = dispatcher.lastLogin ? new Date(dispatcher.lastLogin).toLocaleString() : 'Never';
                const status = dispatcher.status ? 'active' : 'inactive';
                const employeeId = dispatcher.username || dispatcher.id || 'N/A';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${fullName || 'N/A'}</td>
                    <td>${dispatcher.email || 'N/A'}</td>
                    <td>${dispatcher.phone || 'N/A'}</td>
                    <td>${employeeId}</td>
                    <td>${lastLogin}</td>
                    <td><span class="status-badge status-${status}">${status === 'active' ? 'Active' : 'Inactive'}</span></td>
                    <td>
                        <div class="incident-actions">
                            <button class="btn-icon" data-tooltip="View Details" onclick="viewDispatcher(${dispatcher.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-icon" data-tooltip="Edit" onclick="editDispatcher(${dispatcher.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon" data-tooltip="Reset Password" onclick="resetDispatcherPassword(${dispatcher.id})">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn-icon" data-tooltip="Delete" onclick="deleteDispatcher(${dispatcher.id}, '${fullName}')" style="color: var(--accent-red);">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // Show pagination if there are multiple pages
            if (dispatchersPagination && dispatchersPagination.totalPages > 1 && paginationDiv) {
                renderDispatchersPagination();
                paginationDiv.style.display = 'block';
            } else if (paginationDiv) {
                paginationDiv.style.display = 'none';
            }

            if (typeof window.initTooltips === 'function') window.initTooltips();
        })
        .catch(err => {
            tableBody.innerHTML = '<tr><td colspan="7" style="text-align:center; color: red;">Failed to load dispatchers.</td></tr>';
            if (dispatcherCount) dispatcherCount.textContent = 'Error loading dispatchers';
            console.error('Error loading dispatchers:', err);
        });
}

// Function to render dispatchers pagination controls
function renderDispatchersPagination() {
    if (!dispatchersPagination) return;

    const pageNumbers = document.getElementById('dispatchersPageNumbers');
    const prevPage = document.getElementById('prevDispatchersPage');
    const nextPage = document.getElementById('nextDispatchersPage');

    if (!pageNumbers || !prevPage || !nextPage) return;

    // Clear existing page numbers
    pageNumbers.innerHTML = '';

    const currentPage = dispatchersPagination.currentPage;
    const totalPages = dispatchersPagination.totalPages;

    // Previous button state
    if (currentPage <= 1) {
        prevPage.style.opacity = '0.5';
        prevPage.style.pointerEvents = 'none';
    } else {
        prevPage.style.opacity = '1';
        prevPage.style.pointerEvents = 'auto';
    }

    // Next button state
    if (currentPage >= totalPages) {
        nextPage.style.opacity = '0.5';
        nextPage.style.pointerEvents = 'none';
    } else {
        nextPage.style.opacity = '1';
        nextPage.style.pointerEvents = 'auto';
    }

    // Calculate page range to show
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // Adjust range if we're near the beginning or end
    if (endPage - startPage < 4) {
        if (startPage === 1) {
            endPage = Math.min(totalPages, startPage + 4);
        } else if (endPage === totalPages) {
            startPage = Math.max(1, endPage - 4);
        }
    }

    // Add page numbers
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `btn btn-outline ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.style.padding = '8px 12px';
        pageBtn.style.minWidth = '40px';

        if (i === currentPage) {
            pageBtn.style.backgroundColor = 'var(--accent-blue)';
            pageBtn.style.color = 'white';
        }

        pageBtn.addEventListener('click', () => {
            if (i !== currentPage) {
                loadDispatchers(i);
            }
        });

        pageNumbers.appendChild(pageBtn);
    }
}

// Modal management functions
function showAddDispatcherModal() {
    const modal = document.getElementById('dispatcherModal');
    if (!modal) return;

    // Set modal to add mode
    document.getElementById('dispatcherMode').value = 'add';
    document.getElementById('dispatcherId').value = '';
    document.getElementById('dispatcherModalTitle').textContent = 'Add New FireAlerts911 Dispatcher';
    document.getElementById('dispatcherSaveButtonText').textContent = 'Save Dispatcher';

    // Clear form
    document.getElementById('dispatcherFirstName').value = '';
    document.getElementById('dispatcherLastName').value = '';
    document.getElementById('dispatcherEmail').value = '';
    document.getElementById('dispatcherPhone').value = '';
    document.getElementById('dispatcherRole').value = 'dispatcher';
    const employeeIdField = document.getElementById('dispatcherEmployeeId');
    if (employeeIdField) employeeIdField.value = '';
    document.getElementById('dispatcherStatus').checked = true;
    document.getElementById('sendWelcomeEmail').checked = true;

    // Show welcome email option for new dispatchers
    document.getElementById('sendWelcomeEmailGroup').style.display = 'block';

    // Show modal
    modal.style.display = 'flex';
}

function closeDispatcherModal() {
    const modal = document.getElementById('dispatcherModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function viewDispatcher(dispatcherId) {
    fetch(`/api/users/${dispatcherId}`, { credentials: 'include' })
        .then(res => res.json())
        .then(dispatcher => {
            const modal = document.getElementById('dispatcherViewModal');
            const content = document.getElementById('dispatcherViewContent');

            if (!modal || !content) return;

            const fullName = `${dispatcher.firstName || ''} ${dispatcher.lastName || ''}`.trim();
            const lastLogin = dispatcher.lastLogin ? new Date(dispatcher.lastLogin).toLocaleString() : 'Never';
            const status = dispatcher.status ? 'Active' : 'Inactive';
            const employeeId = dispatcher.username || dispatcher.id || 'N/A';
            const createdDate = dispatcher.createdAt ? new Date(dispatcher.createdAt).toLocaleDateString() : 'N/A';

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4 style="margin-top: 0; color: var(--text-light);">Personal Information</h4>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Full Name</label>
                            <div style="color: var(--text-light); font-weight: 500;">${fullName || 'N/A'}</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Email Address</label>
                            <div style="color: var(--text-light); font-weight: 500;">${dispatcher.email || 'N/A'}</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Phone Number</label>
                            <div style="color: var(--text-light); font-weight: 500;">${dispatcher.phone || 'N/A'}</div>
                        </div>
                    </div>
                    <div>
                        <h4 style="margin-top: 0; color: var(--text-light);">Employment Information</h4>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Role</label>
                            <div style="color: var(--text-light); font-weight: 500;">FireAlerts911 ${dispatcher.role || 'Dispatcher'}</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Employee ID</label>
                            <div style="color: var(--text-light); font-weight: 500;">${employeeId}</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Status</label>
                            <div><span class="status-badge status-${dispatcher.status ? 'active' : 'inactive'}">${status}</span></div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Hire Date</label>
                            <div style="color: var(--text-light); font-weight: 500;">${createdDate}</div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="color: var(--text-secondary); font-size: 12px; text-transform: uppercase;">Last Login</label>
                            <div style="color: var(--text-light); font-weight: 500;">${lastLogin}</div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 20px; padding: 15px; background-color: rgba(30, 136, 229, 0.1); border-radius: 8px; border-left: 4px solid var(--accent-blue);">
                    <h5 style="margin: 0 0 10px 0; color: var(--accent-blue);">System Access</h5>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 14px;">This dispatcher has system-wide access to manage incidents across all companies and can perform emergency dispatch operations.</p>
                </div>
            `;

            // Set up edit button
            const editBtn = document.getElementById('editDispatcherFromViewBtn');
            if (editBtn) {
                editBtn.onclick = () => {
                    closeDispatcherViewModal();
                    editDispatcher(dispatcherId);
                };
            }

            modal.style.display = 'flex';
        })
        .catch(err => {
            console.error('Error loading dispatcher details:', err);
            if (typeof showNotification === 'function') {
                showNotification('Error loading dispatcher details', 'error');
            }
        });
}

function closeDispatcherViewModal() {
    const modal = document.getElementById('dispatcherViewModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function editDispatcher(dispatcherId) {
    fetch(`/api/users/${dispatcherId}`, { credentials: 'include' })
        .then(res => res.json())
        .then(dispatcher => {
            const modal = document.getElementById('dispatcherModal');
            if (!modal) return;

            // Set modal to edit mode
            document.getElementById('dispatcherMode').value = 'edit';
            document.getElementById('dispatcherId').value = dispatcherId;
            document.getElementById('dispatcherModalTitle').textContent = 'Edit FireAlerts911 Dispatcher';
            document.getElementById('dispatcherSaveButtonText').textContent = 'Update Dispatcher';

            // Fill form with existing values
            document.getElementById('dispatcherFirstName').value = dispatcher.firstName || '';
            document.getElementById('dispatcherLastName').value = dispatcher.lastName || '';
            document.getElementById('dispatcherEmail').value = dispatcher.email || '';
            document.getElementById('dispatcherPhone').value = dispatcher.phone || '';
            document.getElementById('dispatcherRole').value = dispatcher.role || 'dispatcher';
            const employeeIdField = document.getElementById('dispatcherEmployeeId');
            if (employeeIdField) employeeIdField.value = dispatcher.username || '';
            document.getElementById('dispatcherStatus').checked = dispatcher.status !== false;

            // Hide welcome email option for existing dispatchers
            document.getElementById('sendWelcomeEmailGroup').style.display = 'none';

            // Show modal
            modal.style.display = 'flex';
        })
        .catch(err => {
            console.error('Error loading dispatcher for edit:', err);
            if (typeof showNotification === 'function') {
                showNotification('Error loading dispatcher details', 'error');
            }
        });
}

// Dispatcher deletion and password reset functions

// Delete dispatcher function
function deleteDispatcher(dispatcherId, dispatcherName) {
    if (!confirm(`Are you sure you want to delete dispatcher "${dispatcherName}"? This action cannot be undone.`)) {
        return;
    }

    fetch(`/api/users/${dispatcherId}`, {
        method: 'DELETE',
        credentials: 'include'
    })
        .then(res => res.json())
        .then(response => {
            if (response.success) {
                if (typeof showNotification === 'function') {
                    showNotification('Dispatcher deleted successfully', 'success');
                }
                loadDispatchers(currentDispatchersPage); // Reload current page
            } else {
                throw new Error(response.msg || 'Failed to delete dispatcher');
            }
        })
        .catch(err => {
            console.error('Error deleting dispatcher:', err);
            if (typeof showNotification === 'function') {
                showNotification('Error deleting dispatcher: ' + err.message, 'error');
            }
        });
}

// Reset dispatcher password function
function resetDispatcherPassword(dispatcherId) {
    if (!confirm('Are you sure you want to reset this dispatcher\'s password? A new password will be sent to their email.')) {
        return;
    }

    fetch(`/api/users/${dispatcherId}/reset-password`, {
        method: 'POST',
        credentials: 'include'
    })
        .then(res => res.json())
        .then(response => {
            if (response.success) {
                if (typeof showNotification === 'function') {
                    showNotification('Password reset successfully. New password sent to dispatcher\'s email.', 'success');
                }
            } else {
                throw new Error(response.msg || 'Failed to reset password');
            }
        })
        .catch(err => {
            console.error('Error resetting password:', err);
            if (typeof showNotification === 'function') {
                showNotification('Error resetting password: ' + err.message, 'error');
            }
        });
}

// Hook: Load dispatchers when Users tab is activated
if (document.querySelector('.tab-button[data-tab="users"]')) {
    document.querySelector('.tab-button[data-tab="users"]').addEventListener('click', loadDispatchers);
}
// Optionally, load dispatchers immediately if users tab is active on page load
if (document.querySelector('.tab-button[data-tab="users"].active')) {
    loadDispatchers();
}

// Add event listeners for dispatcher pagination and form submission
document.addEventListener('DOMContentLoaded', function() {
    // Dispatcher pagination event handlers
    const prevDispatchersPage = document.getElementById('prevDispatchersPage');
    const nextDispatchersPage = document.getElementById('nextDispatchersPage');

    if (prevDispatchersPage) {
        prevDispatchersPage.addEventListener('click', function(e) {
            e.preventDefault();
            if (currentDispatchersPage > 1) {
                loadDispatchers(currentDispatchersPage - 1);
            }
        });
    }

    if (nextDispatchersPage) {
        nextDispatchersPage.addEventListener('click', function(e) {
            e.preventDefault();
            if (dispatchersPagination && currentDispatchersPage < dispatchersPagination.totalPages) {
                loadDispatchers(currentDispatchersPage + 1);
            }
        });
    }

    // Add dispatcher button event handler
    const addDispatcherBtn = document.getElementById('addDispatcherBtn');
    if (addDispatcherBtn) {
        addDispatcherBtn.addEventListener('click', showAddDispatcherModal);
    }

    // Dispatcher form submission
    const dispatcherForm = document.getElementById('dispatcherForm');
    if (dispatcherForm) {
        dispatcherForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const mode = document.getElementById('dispatcherMode').value;
            const dispatcherId = document.getElementById('dispatcherId').value;

            const formData = {
                firstName: document.getElementById('dispatcherFirstName').value.trim(),
                lastName: document.getElementById('dispatcherLastName').value.trim(),
                email: document.getElementById('dispatcherEmail').value.trim(),
                phone: document.getElementById('dispatcherPhone').value.trim(),
                role: document.getElementById('dispatcherRole').value,
                status: document.getElementById('dispatcherStatus').checked ? 'active' : 'inactive'
            };

            // Add employee ID if provided
            const employeeIdField = document.getElementById('dispatcherEmployeeId');
            if (employeeIdField && employeeIdField.value.trim()) {
                formData.username = employeeIdField.value.trim();
            }

            // Add welcome email flag for new dispatchers
            if (mode === 'add') {
                formData.sendWelcomeEmail = document.getElementById('sendWelcomeEmail').checked;
            }

            const saveBtn = document.getElementById('dispatcherSaveBtn');
            const originalText = saveBtn.innerHTML;

            // Show loading state
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            saveBtn.disabled = true;

            const url = mode === 'add' ? '/api/users' : `/api/users/${dispatcherId}`;
            const method = mode === 'add' ? 'POST' : 'PUT';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(formData)
            })
                .then(res => res.json())
                .then(response => {
                    if (response.success) {
                        if (typeof showNotification === 'function') {
                            const action = mode === 'add' ? 'created' : 'updated';
                            showNotification(`Dispatcher ${action} successfully`, 'success');
                        }
                        closeDispatcherModal();
                        loadDispatchers(currentDispatchersPage); // Reload current page
                    } else {
                        throw new Error(response.msg || `Failed to ${mode} dispatcher`);
                    }
                })
                .catch(err => {
                    console.error(`Error ${mode}ing dispatcher:`, err);
                    if (typeof showNotification === 'function') {
                        showNotification(`Error ${mode}ing dispatcher: ` + err.message, 'error');
                    }
                })
                .finally(() => {
                    // Restore button state
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                });
        });
    }

    // Modal close on background click
    const dispatcherModal = document.getElementById('dispatcherModal');
    const dispatcherViewModal = document.getElementById('dispatcherViewModal');

    if (dispatcherModal) {
        dispatcherModal.addEventListener('click', function(e) {
            if (e.target === dispatcherModal) {
                closeDispatcherModal();
            }
        });
    }

    if (dispatcherViewModal) {
        dispatcherViewModal.addEventListener('click', function(e) {
            if (e.target === dispatcherViewModal) {
                closeDispatcherViewModal();
            }
        });
    }
});