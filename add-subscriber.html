<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Add Company</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <style>
        /* County Selector Improvements - Exact match from subscriber-details.html */
        .county-selector-container {
            background-color: rgba(39, 46, 72, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }

        /* Search-first Interface Styles */
        .search-first {
            position: relative;
            margin-bottom: 15px;
        }

        .location-search {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
            color: var(--text-light);
            font-size: 16px;
        }

        .location-search:focus {
            border-color: var(--accent-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            pointer-events: none;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: var(--secondary-dark);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 10;
            display: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .search-result {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.2s;
        }

        .search-result:hover {
            background-color: var(--hover-bg);
        }

        .search-result:last-child {
            border-bottom: none;
        }

        .result-county {
            font-weight: 500;
            color: var(--text-light);
        }

        .result-state {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        /* Tree View Styles */
        .tree-fallback {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 15px;
        }

        .fallback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .toggle-browse {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .toggle-browse:hover {
            background-color: var(--hover-bg);
            color: var(--text-light);
        }

        .toggle-browse i {
            transition: transform 0.2s;
        }

        .toggle-browse.expanded i {
            transform: rotate(180deg);
        }

        .tree-selector {
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
        }

        .tree-node {
            padding: 10px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: none;
        }

        .tree-node:hover {
            background-color: var(--hover-bg);
        }

        .tree-state {
            font-weight: 500;
            background-color: rgba(30, 136, 229, 0.1);
            border-left: 3px solid var(--accent-blue);
            display: flex;
            align-items: center;
        }

        .tree-state .expand-icon {
            margin-right: 8px;
            transition: transform 0.2s;
            font-size: 12px;
        }

        .tree-state.collapsed .expand-icon {
            transform: rotate(-90deg);
        }

        .tree-state .state-checkbox {
            margin-right: 8px;
            margin-left: auto;
        }

        .tree-counties {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .tree-state.collapsed + .tree-counties {
            display: none;
        }

        .tree-county {
            padding-left: 35px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .tree-county input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Selected Counties Display */
        .selected-counties {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(30, 136, 229, 0.1);
            border-radius: 4px;
            border: 1px solid var(--accent-blue);
        }

        .county-tag {
            display: inline-block;
            background-color: var(--accent-blue);
            color: white;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 12px;
            position: relative;
        }

        .county-tag .remove {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
            font-weight: bold;
        }

        .county-tag .remove:hover {
            opacity: 1;
            color: #ffcccc;
        }

        /* Loading states */
        .loading-message {
            padding: 15px;
            text-align: center;
            color: var(--text-secondary);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .county-selector-container {
                padding: 15px;
            }

            .location-search {
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .search-results {
                max-height: 200px;
            }

            .tree-selector {
                max-height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Add Subscriber Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-building" style="margin-right: 10px;"></i>
                        Add New Subscriber Company
                    </div>
                    <div class="card-actions">
                        <a href="subscribers.html" class="btn btn-outline btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Subscribers
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <form id="addSubscriberForm" action="#" method="post" data-validate="true">
                        <!-- Company Information -->
                        <h3 class="section-title">Company Information</h3>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Company Name *</label>
                                    <input type="text" class="form-control" name="company_name" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Company Type</label>
                                    <select class="form-control" name="company_type" id="company-type-select">
                                        <option value="">Loading company types...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" name="company_phone" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="company_email" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Address *</label>
                            <input type="text" class="form-control" name="company_address" required>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">City *</label>
                                    <input type="text" class="form-control" name="company_city" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">State *</label>
                                    <input type="text" class="form-control" name="company_state" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">ZIP Code *</label>
                                    <input type="text" class="form-control" name="company_zip" required>
                                </div>
                            </div>
                        </div>

                        <!-- Primary Administrator (First User) -->
                        <h3 class="section-title">Primary Administrator</h3>
                        <p class="text-muted">This user will be the main administrator for the company account.</p>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">First Name *</label>
                                    <input type="text" class="form-control" name="admin_firstname" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" name="admin_lastname" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="admin_email" required>
                                    <small style="color: var(--text-secondary);">This will be used for login and notifications.</small>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" name="admin_phone" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Job Title</label>
                                    <input type="text" class="form-control" name="admin_title">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <input type="text" class="form-control" name="admin_department">
                                </div>
                            </div>
                        </div>

                        <!-- Subscription Settings -->
                        <h3 class="section-title">Subscription Settings</h3>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Subscription Plan *</label>
                                    <select class="form-control" name="subscription_plan" id="subscription-plan-select" required>
                                        <option value="">Loading plans...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Maximum Users</label>
                                    <input type="number" class="form-control" name="max_users" value="5" min="1" max="100">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Counties of Interest *</label>

                            <!-- Search-first Interface (Option 4) - Exact match from subscriber-details.html -->
                            <div class="county-selector-container">
                                <div class="search-first">
                                    <input type="text" class="location-search" id="location-search" placeholder="🔍 Start typing county or state name (fastest way)..." />
                                    <i class="fas fa-search search-icon"></i>
                                    <div class="search-results" id="search-results">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>

                                <!-- Tree View Browser (Option 2) -->
                                <div class="tree-fallback" style="margin-top: 20px;">
                                    <div class="fallback-header">
                                        <span>Or browse by state:</span>
                                        <button type="button" class="toggle-browse" id="toggle-browse">
                                            <i class="fas fa-chevron-down"></i> Show State Tree
                                        </button>
                                    </div>
                                    <div class="tree-selector" id="tree-browser" style="display: none;">
                                        <div class="loading-message">Loading states and counties...</div>
                                    </div>
                                </div>

                                <!-- Selected Counties Display -->
                                <div class="selected-counties" id="selected-counties-display">
                                    <strong>Selected Counties:</strong>
                                    <div id="county-tags-display">None selected</div>
                                </div>
                            </div>

                            <small style="color: var(--text-secondary);">Select all counties you want to receive alerts for. Search is fastest, or browse using the expandable state tree below.</small>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Alert Preferences *</label>
                                    <div style="margin-top: 10px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="pref_fire" checked>
                                            <span style="margin-left: 10px;">Fire Incidents</span>
                                        </label>
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="pref_water" checked>
                                            <span style="margin-left: 10px;">Water Incidents</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Notification Methods</label>
                                    <div style="margin-top: 10px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="notify_email" checked>
                                            <span style="margin-left: 10px;">Email Notifications</span>
                                        </label>
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="notify_sms" checked>
                                            <span style="margin-left: 10px;">SMS Notifications</span>
                                        </label>
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="notify_app" checked>
                                            <span style="margin-left: 10px;">Mobile App Notifications</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>

                        <!-- Account Status -->
                        <div class="form-group">
                            <label class="form-label">Account Status</label>
                            <select class="form-control" name="status">
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="trial">Trial</option>
                                <option value="blocked">Blocked</option>
                            </select>
                        </div>

                        <!-- Temporary Password Option -->
                        <div class="form-group">
                            <label class="form-check">
                                <input type="checkbox" name="generate_password" checked>
                                <span style="margin-left: 10px;">Generate temporary password and send to administrator's email</span>
                            </label>
                        </div>

                        <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                            <a href="subscribers.html" class="btn btn-outline">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle"></i> Create Subscriber
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Removed custom logout handler - now using standard navigation system

        // Function to initialize county selection - Exact match from subscriber-details.html
        function loadCountiesData(selectedCounties = []) {
            const treeBrowser = document.getElementById('tree-browser');
            if (!treeBrowser) return;

            treeBrowser.innerHTML = '<div class="loading-message">Loading states and counties...</div>';

            // Clear any previous search results
            const searchResults = document.getElementById('search-results');
            if (searchResults) {
                searchResults.innerHTML = '';
                searchResults.style.display = 'none';
            }

            // Initialize selected counties display
            updateSelectedCountiesDisplay(selectedCounties);

            // Fetch states
            apiRequest('/locations/states')
                .then(states => {
                    if (!states || !states.length) throw new Error('No states found');
                    // Fetch counties for each state
                    return Promise.all(states.map(state =>
                        apiRequest(`/locations/counties?stateId=${state.id}`)
                            .then(counties => ({ state, counties: counties || [] }))
                            .catch(() => ({ state, counties: [] }))
                    ));
                })
                .then(statesWithCounties => {
                    // Store data globally for search functionality
                    allStatesWithCounties = statesWithCounties;

                    // Filter out states with no counties
                    const valid = statesWithCounties.filter(item => item.counties.length > 0);
                    if (!valid.length) {
                        treeBrowser.innerHTML = '<div class="loading-message">No counties found.</div>';
                        return;
                    }

                    // Build tree view HTML
                    let html = '';
                    valid.forEach(item => {
                        // Create state node
                        html += `
                            <div class="tree-node tree-state collapsed" data-state-id="${item.state.id}" onclick="toggleStateNode(event, ${item.state.id})">
                                <i class="fas fa-chevron-down expand-icon"></i>
                                ${item.state.name}
                                <input type="checkbox" class="state-checkbox"
                                    data-state-id="${item.state.id}"
                                    onclick="event.stopPropagation(); toggleAllCounties(${item.state.id}, this.checked)">
                            </div>
                            <div class="tree-counties" id="counties-${item.state.id}" style="display: none;">`;

                        // Add county nodes
                        item.counties.forEach(county => {
                            const isChecked = selectedCounties.includes(String(county.id)) ? 'checked' : '';
                            html += `
                                <div class="tree-node tree-county">
                                    <input type="checkbox" id="county-${county.id}"
                                        name="counties"
                                        value="${county.id}"
                                        data-state-id="${item.state.id}"
                                        data-county-name="${county.name}"
                                        data-state-name="${item.state.name}"
                                        ${isChecked}
                                        onchange="updateStateCheckbox(${item.state.id}); updateSelectedCounty(this);">
                                    <label for="county-${county.id}">${county.name}</label>
                                </div>`;
                        });

                        html += '</div>';
                    });

                    treeBrowser.innerHTML = html;

                    // After building the tree, update the selected counties display and state checkboxes
                    if (selectedCounties.length > 0) {
                        // Update the selected counties display
                        updateSelectedCountiesDisplay(selectedCounties);

                        // Update state checkboxes for all states that have selected counties
                        const selectedStates = new Set();
                        selectedCounties.forEach(countyId => {
                            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
                            if (checkbox) {
                                selectedStates.add(checkbox.dataset.stateId);
                            }
                        });

                        selectedStates.forEach(stateId => {
                            updateStateCheckbox(stateId);
                        });
                    }

                    // Setup search functionality
                    setupCountySearch(valid);

                    // Setup toggle browse functionality
                    const toggleBrowse = document.getElementById('toggle-browse');
                    if (toggleBrowse) {
                        toggleBrowse.addEventListener('click', function() {
                            const treeSelector = document.getElementById('tree-browser');
                            if (treeSelector) {
                                const isVisible = treeSelector.style.display !== 'none';
                                treeSelector.style.display = isVisible ? 'none' : 'block';
                                this.classList.toggle('expanded', !isVisible);
                                this.innerHTML = isVisible ?
                                    '<i class="fas fa-chevron-down"></i> Show State Tree' :
                                    '<i class="fas fa-chevron-up"></i> Hide State Tree';
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading counties:', error);
                    treeBrowser.innerHTML = '<div class="loading-message">Error loading counties. Please try again.</div>';
                });
        }

        // Global variable to store all states and counties data for search
        let allStatesWithCounties = [];

        // Toggle state node expand/collapse - Exact match from subscriber-details.html
        window.toggleStateNode = function(event, stateId) {
            const stateElement = event.currentTarget;
            const countiesElement = document.getElementById(`counties-${stateId}`);

            if (!countiesElement) return;

            stateElement.classList.toggle('collapsed');
            countiesElement.style.display = stateElement.classList.contains('collapsed') ? 'none' : 'block';
        }

        // Toggle all counties in a state - Exact match from subscriber-details.html
        window.toggleAllCounties = function(stateId, checked) {
            const countyCheckboxes = document.querySelectorAll(`input[name="counties"][data-state-id="${stateId}"]`);

            countyCheckboxes.forEach(checkbox => {
                if (checkbox.checked !== checked) {
                    checkbox.checked = checked;
                    updateSelectedCounty(checkbox);
                }
            });
        }

        // Update state checkbox based on county selections - Exact match from subscriber-details.html
        window.updateStateCheckbox = function(stateId) {
            const counties = document.querySelectorAll(`input[name="counties"][data-state-id="${stateId}"]`);
            const stateCheckbox = document.querySelector(`.tree-state[data-state-id="${stateId}"] input[type="checkbox"]`);

            if (!stateCheckbox || counties.length === 0) return;

            const checkedCount = Array.from(counties).filter(cb => cb.checked).length;

            if (checkedCount === 0) {
                stateCheckbox.checked = false;
                stateCheckbox.indeterminate = false;
            } else if (checkedCount === counties.length) {
                stateCheckbox.checked = true;
                stateCheckbox.indeterminate = false;
            } else {
                stateCheckbox.checked = false;
                stateCheckbox.indeterminate = true;
            }
        }



        // Update selected counties display - Exact match from subscriber-details.html
        window.updateSelectedCountiesDisplay = function(selectedCounties = []) {
            const tagsContainer = document.getElementById('county-tags-display');
            if (!tagsContainer) return;

            if (!Array.isArray(selectedCounties)) {
                selectedCounties = [];
            }

            if (selectedCounties.length === 0) {
                tagsContainer.innerHTML = 'None selected';
                return;
            }

            // Get county names from checkboxes
            let html = '';
            selectedCounties.forEach(countyId => {
                const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
                if (checkbox) {
                    const countyName = checkbox.dataset.countyName || 'Unknown County';
                    const stateName = checkbox.dataset.stateName || '';

                    html += `
                        <span class="county-tag" data-id="${countyId}">
                            ${countyName}${stateName ? `, ${stateName}` : ''}
                            <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                        </span>`;
                }
            });

            tagsContainer.innerHTML = html;
        }

        // Update the selected counties when a checkbox changes - Exact match from subscriber-details.html
        window.updateSelectedCounty = function(checkbox) {
            const countyId = checkbox.value;
            const countyName = checkbox.dataset.countyName || 'Unknown County';
            const stateName = checkbox.dataset.stateName || '';
            const tagsContainer = document.getElementById('county-tags-display');

            if (!tagsContainer) return;

            if (checkbox.checked) {
                // Add county
                if (tagsContainer.innerHTML === 'None selected') {
                    tagsContainer.innerHTML = '';
                }

                // Skip if already exists
                if (document.querySelector(`.county-tag[data-id="${countyId}"]`)) {
                    return;
                }

                const tag = document.createElement('span');
                tag.className = 'county-tag';
                tag.dataset.id = countyId;
                tag.innerHTML = `
                    ${countyName}${stateName ? `, ${stateName}` : ''}
                    <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                `;

                tagsContainer.appendChild(tag);
            } else {
                // Remove county
                const existingTag = document.querySelector(`.county-tag[data-id="${countyId}"]`);
                if (existingTag) {
                    existingTag.remove();
                }

                if (tagsContainer.children.length === 0) {
                    tagsContainer.innerHTML = 'None selected';
                }
            }
        }

        // Remove a county from the selected display - Exact match from subscriber-details.html
        window.removeSelectedCounty = function(countyId) {
            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
            if (checkbox) {
                checkbox.checked = false;
                updateStateCheckbox(checkbox.dataset.stateId);
            }

            const tag = document.querySelector(`.county-tag[data-id="${countyId}"]`);
            if (tag) {
                tag.remove();
            }

            const tagsContainer = document.getElementById('county-tags-display');
            if (tagsContainer && tagsContainer.children.length === 0) {
                tagsContainer.innerHTML = 'None selected';
            }
        }

        // Setup county search functionality - Exact match from subscriber-details.html
        function setupCountySearch(statesWithCounties) {
            const searchInput = document.getElementById('location-search');
            const searchResults = document.getElementById('search-results');

            if (!searchInput || !searchResults) return;

            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.toLowerCase().trim();

                if (query.length < 2) {
                    searchResults.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    // Search through all counties
                    const results = [];

                    statesWithCounties.forEach(item => {
                        item.counties.forEach(county => {
                            if (county.name.toLowerCase().includes(query) ||
                                item.state.name.toLowerCase().includes(query)) {
                                results.push({
                                    id: county.id,
                                    county: county.name,
                                    state: item.state.name,
                                    stateId: item.state.id
                                });
                            }
                        });
                    });

                    // Display results
                    if (results.length === 0) {
                        searchResults.innerHTML = '<div class="search-result">No results found</div>';
                    } else {
                        searchResults.innerHTML = results.map(result => `
                            <div class="search-result" onclick="selectSearchResult('${result.id}', '${result.stateId}', '${result.county.replace(/'/g, "\\'")}', '${result.state.replace(/'/g, "\\'")}')">
                                <div class="result-county">${result.county}</div>
                                <div class="result-state">${result.state}</div>
                            </div>
                        `).join('');
                    }

                    searchResults.style.display = 'block';
                }, 300);
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-first')) {
                    searchResults.style.display = 'none';
                }
            });
        }

        // Select a county from search results - Exact match from subscriber-details.html
        window.selectSearchResult = function(countyId, stateId, countyName, stateName) {
            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);

            if (checkbox) {
                // County exists in the tree - check it
                checkbox.checked = true;
                updateStateCheckbox(stateId);
                updateSelectedCounty(checkbox);

                // Expand the state node to show the selection
                const stateNode = document.querySelector(`.tree-state[data-state-id="${stateId}"]`);
                const countiesNode = document.getElementById(`counties-${stateId}`);

                if (stateNode && countiesNode) {
                    stateNode.classList.remove('collapsed');
                    countiesNode.style.display = 'block';
                }

                // Scroll to the selected county
                const treeBrowser = document.getElementById('tree-browser');
                if (treeBrowser && checkbox.parentElement) {
                    treeBrowser.scrollTop = checkbox.parentElement.offsetTop - 100;
                }
            } else {
                // County doesn't exist in the tree - create a custom tag
                const tagsContainer = document.getElementById('county-tags-display');

                if (tagsContainer) {
                    if (tagsContainer.innerHTML === 'None selected') {
                        tagsContainer.innerHTML = '';
                    }

                    // Skip if already exists
                    if (document.querySelector(`.county-tag[data-id="${countyId}"]`)) {
                        return;
                    }

                    const tag = document.createElement('span');
                    tag.className = 'county-tag';
                    tag.dataset.id = countyId;
                    tag.innerHTML = `
                        ${countyName}${stateName ? `, ${stateName}` : ''}
                        <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                    `;

                    tagsContainer.appendChild(tag);
                }
            }

            // Clear search input and hide results
            const searchInput = document.getElementById('location-search');
            const searchResults = document.getElementById('search-results');

            if (searchInput) searchInput.value = '';
            if (searchResults) searchResults.style.display = 'none';
        }

        // Get selected counties for form submission - Updated to match subscriber-details.html
        function getSelectedCounties() {
            // Get all selected counties from checkboxes
            const selectedCheckboxes = document.querySelectorAll('input[name="counties"]:checked');
            const checkboxValues = Array.from(selectedCheckboxes).map(cb => cb.value);

            // Also get any custom tags (counties that might not be in the tree but were added via search)
            const customTags = document.querySelectorAll('.county-tag');
            const tagValues = Array.from(customTags).map(tag => tag.dataset.id);

            // Combine and remove duplicates
            const allCounties = [...new Set([...checkboxValues, ...tagValues])];
            return allCounties;
        }

          // Use the standard apiRequest function from api.js instead of a local override

        // Function to load company types
        function loadCompanyTypes() {
            const companyTypeSelect = document.getElementById('company-type-select');
            if (!companyTypeSelect) return;

            // Add loading option
            companyTypeSelect.innerHTML = '<option value="">Loading company types...</option>';

            // Fetch company types from the API
            apiRequest('/company-types')
                .then(data => {
                    // Clear loading message
                    companyTypeSelect.innerHTML = '<option value="">Select Company Type</option>';

                    // Add each company type as an option
                    if (data && data.length > 0) {
                        data.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type.id || type.code;
                            option.textContent = type.name;
                            companyTypeSelect.appendChild(option);
                        });
                    } else {
                        // If no data, add fallback options
                        const fallbackTypes = [
                            { id: 'fire_department', name: 'Fire Department' },
                            { id: 'police_department', name: 'Police Department' },
                            { id: 'emergency_services', name: 'Emergency Services' },
                            { id: 'government', name: 'Government Agency' },
                            { id: 'private_security', name: 'Private Security' },
                            { id: 'insurance', name: 'Insurance Company' },
                            { id: 'other', name: 'Other' }
                        ];

                        fallbackTypes.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type.id;
                            option.textContent = type.name;
                            companyTypeSelect.appendChild(option);
                        });

                        console.warn('Using fallback company types');
                    }
                })
                .catch(error => {
                    console.error('Error loading company types:', error);
                    companyTypeSelect.innerHTML = '<option value="">Error loading types</option>';
                });
        }

        // Function to load subscription plans
        function loadSubscriptionPlans() {
            const planSelect = document.getElementById('subscription-plan-select');
            if (!planSelect) return;

            // Add loading option
            planSelect.innerHTML = '<option value="">Loading subscription plans...</option>';
              // Fetch subscription plans from the API
            apiRequest('/subscription-plans')
                .then(data => {
                    console.log('Subscription plans data:', data); // Debug log to see what data comes back
                    // Clear loading message
                    planSelect.innerHTML = '<option value="">Select Subscription Plan</option>';

                    // Add each plan as an option
                    if (data && data.length > 0) {
                        data.forEach(plan => {
                            const option = document.createElement('option');
                            option.value = plan.id || plan.code;
                            option.textContent = `${plan.name}${plan.price ? ' - $' + plan.price : ''}`;
                            planSelect.appendChild(option);
                        });
                    } else {
                        // If no data, add fallback options
                        const fallbackPlans = [
                            { id: 'basic', name: 'Basic Plan' },
                            { id: 'standard', name: 'Standard Plan' },
                            { id: 'premium', name: 'Premium Plan' },
                            { id: 'enterprise', name: 'Enterprise Plan' }
                        ];

                        fallbackPlans.forEach(plan => {
                            const option = document.createElement('option');
                            option.value = plan.id;
                            option.textContent = plan.name;
                            planSelect.appendChild(option);
                        });

                        console.warn('Using fallback subscription plans');
                    }
                })
                .catch(error => {
                    console.error('Error loading subscription plans:', error);
                    planSelect.innerHTML = '<option value="">Error loading plans</option>';
                });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('add-subscriber');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Removed custom authentication display logic - now using standard navigation system

            // Load data
            loadCountiesData([]);  // Initialize with empty array for new subscriber
            loadCompanyTypes();
            loadSubscriptionPlans();

            // Form submission
            document.getElementById('addSubscriberForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Verify token exists before submitting
                if (!localStorage.getItem('token') && !sessionStorage.getItem('token')) {
                    showNotification('Authentication required. Please log in.', 'error');
                    setTimeout(function() {
                        window.location.href = 'login.html?reason=token_missing';
                    }, 1500);
                    return;
                }

                // Validate that at least one county is selected
                const selectedCountyValues = getSelectedCounties();
                if (selectedCountyValues.length === 0) {
                    showNotification('Please select at least one county of interest', 'warning');
                    return;
                }

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
                submitBtn.disabled = true;

                // Collect form data
                const formData = new FormData(this);

                const subscriberData = {
                    company: {
                        name: formData.get('company_name'),
                        type: formData.get('company_type'),
                        phone: formData.get('company_phone'),
                        email: formData.get('company_email'),
                        address: formData.get('company_address'),
                        city: formData.get('company_city'),
                        state: formData.get('company_state'),
                        zip: formData.get('company_zip'),
                        status: formData.get('status') || 'active',
                        notes: formData.get('notes')
                    },
                    admin: {
                        firstName: formData.get('admin_firstname'),
                        lastName: formData.get('admin_lastname'),
                        email: formData.get('admin_email'),
                        phone: formData.get('admin_phone'),
                        title: formData.get('admin_title'),
                        department: formData.get('admin_department'),
                        generatePassword: formData.has('generate_password')
                    },
                    subscription: {
                        plan: formData.get('subscription_plan'),
                        maxUsers: formData.get('max_users') || 5,
                        counties: selectedCountyValues
                    },
                    preferences: {
                        fireAlerts: formData.has('pref_fire'),
                        waterAlerts: formData.has('pref_water'),
                        emailNotify: formData.has('notify_email'),
                        smsNotify: formData.has('notify_sms'),
                        appNotify: formData.has('notify_app')
                    }
                };

                // Submit to API
                API.companies.create(subscriberData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Company created successfully!', 'success');

                            // Redirect after success
                            setTimeout(function() {
                                window.location.href = 'subscribers.html';
                            }, 1500);
                        } else {
                            showNotification('Failed to create company: ' + (response?.message || 'Unknown error'), 'error');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error creating company:', error);
                        showNotification('Error connecting to server', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });
        });
    </script>
</body>
</html>
