module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('notification', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    incidentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'incident_id'
    },
    type: {
      type: DataTypes.ENUM('email', 'sms', 'push'),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'sent', 'failed', 'delivered'),
      defaultValue: 'pending'
    },
    sentAt: {
      type: DataTypes.DATE,
      field: 'sent_at'
    },
    deliveredAt: {
      type: DataTypes.DATE,
      field: 'delivered_at'
    },
    content: {
      type: DataTypes.TEXT
    },
    errorMessage: {
      type: DataTypes.TEXT,
      field: 'error_message'
    },
    retryCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      field: 'retry_count'
    },
    meta: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    notificationType: {
      type: DataTypes.ENUM('new', 'update'),
      defaultValue: 'new',
      field: 'notification_type'
    },
    templateType: {
      type: DataTypes.STRING,
      defaultValue: 'incident',
      field: 'template_type'
    }
  }, {
    tableName: 'notifications',
    underscored: true,
    indexes: [
      {
        fields: ['incident_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['type']
      }
    ]
  });

  return Notification;
};
