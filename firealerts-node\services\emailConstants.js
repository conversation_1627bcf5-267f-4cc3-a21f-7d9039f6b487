/**
 * Email Constants
 * 
 * Centralized constants for email types, priorities, and categories
 */

const EMAIL_TYPES = {
    // Authentication & User Management
    WELCOME: 'welcome',
    PASSWORD_RESET: 'password_reset',
    ACCOUNT_VERIFICATION: 'account_verification',
    
    // Emergency & Incident Notifications
    INCIDENT_ALERT: 'incident_alert',
    EMERGENCY_BROADCAST: 'emergency_broadcast',
    INCIDENT_UPDATE: 'incident_update',
    
    // System & Administrative
    TEST: 'test',
    BULK_NOTIFICATION: 'bulk_notification',
    SYSTEM_MAINTENANCE: 'system_maintenance',
    
    // User Communications
    NEWSLETTER: 'newsletter',
    SUBSCRIPTION_EXPIRY: 'subscription_expiry',
    BILLING_REMINDER: 'billing_reminder'
};

const EMAIL_CATEGORIES = {
    AUTHENTICATION: 'authentication',
    EMERGENCY: 'emergency',
    SYSTEM: 'system',
    ADMINISTRATIVE: 'administrative',
    USER_COMMUNICATION: 'user_communication'
};

const EMAIL_PRIORITIES = {
    CRITICAL: 'critical',    // Emergency alerts, security issues
    HIGH: 'high',           // Important notifications, password resets
    NORMAL: 'normal',       // Regular notifications, welcome emails
    LOW: 'low'             // Newsletters, marketing content
};

const EMAIL_PROVIDERS = {
    MAILGUN: 'mailgun',
    SMTP: 'smtp',
    AUTO: 'auto'
};

// Email type to category mapping
const TYPE_CATEGORY_MAP = {
    [EMAIL_TYPES.WELCOME]: EMAIL_CATEGORIES.AUTHENTICATION,
    [EMAIL_TYPES.PASSWORD_RESET]: EMAIL_CATEGORIES.AUTHENTICATION,
    [EMAIL_TYPES.ACCOUNT_VERIFICATION]: EMAIL_CATEGORIES.AUTHENTICATION,
    
    [EMAIL_TYPES.INCIDENT_ALERT]: EMAIL_CATEGORIES.EMERGENCY,
    [EMAIL_TYPES.EMERGENCY_BROADCAST]: EMAIL_CATEGORIES.EMERGENCY,
    [EMAIL_TYPES.INCIDENT_UPDATE]: EMAIL_CATEGORIES.EMERGENCY,
    
    [EMAIL_TYPES.TEST]: EMAIL_CATEGORIES.SYSTEM,
    [EMAIL_TYPES.BULK_NOTIFICATION]: EMAIL_CATEGORIES.ADMINISTRATIVE,
    [EMAIL_TYPES.SYSTEM_MAINTENANCE]: EMAIL_CATEGORIES.SYSTEM,
    
    [EMAIL_TYPES.NEWSLETTER]: EMAIL_CATEGORIES.USER_COMMUNICATION,
    [EMAIL_TYPES.SUBSCRIPTION_EXPIRY]: EMAIL_CATEGORIES.USER_COMMUNICATION,
    [EMAIL_TYPES.BILLING_REMINDER]: EMAIL_CATEGORIES.USER_COMMUNICATION
};

// Email type to priority mapping
const TYPE_PRIORITY_MAP = {
    [EMAIL_TYPES.INCIDENT_ALERT]: EMAIL_PRIORITIES.CRITICAL,
    [EMAIL_TYPES.EMERGENCY_BROADCAST]: EMAIL_PRIORITIES.CRITICAL,
    [EMAIL_TYPES.PASSWORD_RESET]: EMAIL_PRIORITIES.HIGH,
    [EMAIL_TYPES.ACCOUNT_VERIFICATION]: EMAIL_PRIORITIES.HIGH,
    [EMAIL_TYPES.WELCOME]: EMAIL_PRIORITIES.NORMAL,
    [EMAIL_TYPES.INCIDENT_UPDATE]: EMAIL_PRIORITIES.NORMAL,
    [EMAIL_TYPES.BULK_NOTIFICATION]: EMAIL_PRIORITIES.NORMAL,
    [EMAIL_TYPES.TEST]: EMAIL_PRIORITIES.LOW,
    [EMAIL_TYPES.NEWSLETTER]: EMAIL_PRIORITIES.LOW,
    [EMAIL_TYPES.BILLING_REMINDER]: EMAIL_PRIORITIES.LOW
};

module.exports = {
    EMAIL_TYPES,
    EMAIL_CATEGORIES,
    EMAIL_PRIORITIES,
    EMAIL_PROVIDERS,
    TYPE_CATEGORY_MAP,
    TYPE_PRIORITY_MAP
};
