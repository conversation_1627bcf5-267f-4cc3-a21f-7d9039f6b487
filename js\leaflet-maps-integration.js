/**
 * Leaflet Maps Integration for FireAlerts911
 */

// Global map variable and configuration
let leafletMap = null;
const mapConfig = {
  maxZoom: 19,
  defaultZoom: 4,
  defaultMaxBoundsZoom: 15,
  stateBasedZoom: {
    // Different zoom levels for different geographical situations
    singlePoint: 15,
    singleState: 8,
    multiState: 5,
    countryWide: 4,
    default: 5
  }
};

/**
 * Initialize Leaflet Map for displaying incidents
 * This function initializes a Leaflet Map and displays incidents on it
 */
function initMap() {
  console.log('Initializing Leaflet map...');

  // Prevent duplicate initialization
  if (window.leafletMapInitialized) {
    console.log('Map already initialized, skipping...');
    return;
  }

  // Check for both possible map element IDs to support both implementations
  const mapElement = document.getElementById('map') || document.getElementById('dispatch-map');
  const mapPlaceholder = document.getElementById('map-placeholder');

  if (!mapElement) {
    console.error('Map element not found');
    return;
  }

  // Check if map already exists and remove it
  if (leafletMap) {
    leafletMap.remove();
  }

  // Mark as initialized
  window.leafletMapInitialized = true;

  try {
    // Create map instance with default options and explicit maxZoom
    leafletMap = L.map(mapElement.id, {
      center: [39.8283, -98.5795], // Center of US by default
      zoom: mapConfig.defaultZoom,
      zoomControl: true,
      fullscreenControl: true,
      maxZoom: mapConfig.maxZoom,
      preferCanvas: true // Better performance for many markers
    });

    // Add tile layer (OpenStreetMap)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: mapConfig.maxZoom
    }).addTo(leafletMap);

    // Add additional maps as options
    const baseMaps = {
      "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: mapConfig.maxZoom
      }),
      "Satellite": L.tileLayer('https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}', {
        maxZoom: 20,
        subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
        attribution: '&copy; Google Maps'
      }),
      "Terrain": L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
        maxZoom: 17,
        attribution: '&copy; OpenTopoMap contributors'
      })
    };

    // Add layer control to switch between maps
    L.control.layers(baseMaps, {}, {position: 'topright'}).addTo(leafletMap);

    // Add fullscreen control
    if (L.control.fullscreen) {
      L.control.fullscreen({
        position: 'topleft',
        title: 'Show fullscreen',
        titleCancel: 'Exit fullscreen'
      }).addTo(leafletMap);
    }

    // Hide the placeholder once map has loaded
    if (mapPlaceholder) {
      mapPlaceholder.style.display = 'none';
    }

    // Fetch and display incidents
    fetchAndDisplayIncidents(leafletMap);
  } catch (error) {
    console.error('Error initializing Leaflet map:', error);
    if (mapPlaceholder) {
      mapPlaceholder.innerHTML = `
        <div style="text-align:center;">
          <i class="fas fa-exclamation-triangle" style="font-size:48px; margin-bottom:10px; color: #f44336;"></i>
          <p>Error loading map. Please refresh the page.</p>
        </div>
      `;
    }
  }
}

/**
 * Fetch incidents from the API and display them as markers on the map
 * @param {L.Map} map - The Leaflet Map instance
 */
function fetchAndDisplayIncidents(map) {
  // Check if API object exists
  if (typeof API === 'undefined' || !API.incidents) {
    console.error('API object not found or incidents API not available');
    showNotification('Error loading incident data for map.', 'error');
    return;
  }

  try {
    // Create marker clusters group with specific options to prevent maxZoom errors
    const markers = typeof L.markerClusterGroup === 'function'
      ? L.markerClusterGroup({
          maxClusterRadius: 50,
          spiderfyOnMaxZoom: true,
          showCoverageOnHover: false,
          zoomToBoundsOnClick: true,
          maxZoom: mapConfig.maxZoom || 19, // Explicitly set maxZoom here
          disableClusteringAtZoom: 16
        })
      : L.layerGroup();

    // Add loading indicator to the map using direct DOM manipulation
    const mapContainer = map.getContainer();
    if (mapContainer) {
      // Ensure map container has relative positioning
      if (mapContainer.style.position !== 'relative' && mapContainer.style.position !== 'absolute') {
        mapContainer.style.position = 'relative';
      }

      const loadingIndicator = document.createElement('div');
      loadingIndicator.id = 'map-loading-indicator';
      loadingIndicator.className = 'map-loading-indicator';
      loadingIndicator.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.9);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        color: #333;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        pointer-events: none;
      `;
      loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading incidents...';

      mapContainer.appendChild(loadingIndicator);
    }

    // Use optimized endpoint for map data with location filtering
    const apiMethod = API.incidents.getLocations || (() => API.incidents.getAll({ includeLocation: true }));

    // Use fallback data if we've previously failed
    const cachedIncidents = tryGetCachedIncidents();
    if (cachedIncidents && cachedIncidents.length > 0) {
      // Process the cached data first while we wait for fresh data
      processIncidentsData(cachedIncidents, false);
    }

    // Fetch incidents with location data and a longer timeout
    apiMethod()
      .then(response => {
        console.log('Map incidents data received:', response);
        let incidents = [];

        // Remove loading indicator
        const loadingIndicator = document.getElementById('map-loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.parentNode.removeChild(loadingIndicator);
        }

        // Handle standardized API response format
        if (response && response.success && response.data && Array.isArray(response.data)) {
          incidents = response.data;
        } else if (response && response.data && Array.isArray(response.data)) {
          incidents = response.data;
        } else if (Array.isArray(response)) {
          incidents = response;
        } else if (response && response.incidents && Array.isArray(response.incidents)) {
          incidents = response.incidents;
        } else {
          console.error('Unexpected API response format:', response);
          throw new Error('Unexpected response format');
        }

        // Cache valid incident data for fallback on future failures
        if (incidents.length > 0) {
          localStorage.setItem('map_incidents_cache', JSON.stringify(incidents));
          localStorage.setItem('map_incidents_timestamp', Date.now().toString());
        }

        // IMPORTANT: Limit the number of incidents to avoid performance issues
        const MAX_INCIDENTS = 100;
        if (incidents.length > MAX_INCIDENTS) {
          console.log(`Limiting map to ${MAX_INCIDENTS} incidents out of ${incidents.length} total`);

          // Sort by date first (if available) to get most recent
          incidents.sort((a, b) => {
            const dateA = new Date(a.incidentDate || a.createdAt || 0);
            const dateB = new Date(b.incidentDate || b.createdAt || 0);
            return dateB - dateA; // Most recent first
          });

          // Take only the most recent incidents
          incidents = incidents.slice(0, MAX_INCIDENTS);
        }

        processIncidentsData(incidents, true);
      })
      .catch(error => {
        console.error('Error fetching incidents for map:', error);

        // Remove loading indicator
        const loadingIndicator = document.getElementById('map-loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.parentNode.removeChild(loadingIndicator);
        }

        // Check if we're online before showing an error
        if (!navigator.onLine) {
          showNotification('Unable to load map data. You appear to be offline.', 'warning');
          console.log('Device is offline, using cached data if available');
        }

        // Try to use cached incidents data as fallback
        const fallbackData = tryGetCachedIncidents();
        if (fallbackData && fallbackData.length > 0) {
          // Use cached data without showing error message
          console.log(`Using ${fallbackData.length} cached incidents as fallback`);
          processIncidentsData(fallbackData, false);

          // Show a less alarming message
          showNotification('Using cached incident data. Refresh to try updating.', 'info');
        } else {
          // Only show error if we have no cached data to use
          showNotification('Failed to load incidents on map. Please check your connection.', 'error');
          showEmptyStateOnMap(map);

          // Store a flag to avoid showing the same error repeatedly
          if (!window.mapErrorShown) {
            window.mapErrorShown = true;
            console.warn('No cached data available as fallback');
          }
        }
      });

    // Inner function to process and display incidents on the map
    function processIncidentsData(incidents, isFreshData) {
      // Clear empty state flag when processing new data
      window.mapEmptyStateShown = false;

      // Clear any existing empty state message
      const existingMessage = document.getElementById('map-empty-message');
      if (existingMessage && existingMessage.parentNode) {
        existingMessage.parentNode.removeChild(existingMessage);
      }

      // Filter out incidents without location data
      const geoIncidents = incidents.filter(incident => {
        return incident.latitude && incident.longitude &&
               !isNaN(parseFloat(incident.latitude)) &&
               !isNaN(parseFloat(incident.longitude));
      });

      if (geoIncidents.length === 0) {
        console.warn('No incidents with valid coordinates found');
        if (isFreshData) { // Only show this for fresh data, not cached data
          showNotification('No incidents with location data available.', 'info');
        }
        showEmptyStateOnMap(map);
        return;
      }

      // Clear previous markers if this is fresh data
      if (isFreshData) {
        markers.clearLayers();
      }

      // Track states for multi-state determination
      const states = new Set();

      // Create markers for each incident with location data
      const bounds = L.latLngBounds();

      geoIncidents.forEach(incident => {
        const position = L.latLng(
          parseFloat(incident.latitude),
          parseFloat(incident.longitude)
        );

        // Extend map bounds to include this point
        bounds.extend(position);

        // Track unique states
        if (incident.state) {
          states.add(incident.state);
        }

        // Determine marker icon based on incident type
        const isFireIncident = incident.incidentType &&
                              (incident.incidentType.category === 'fire' ||
                               incident.incidentType.name?.toLowerCase().includes('fire'));

        // Create custom icon
        const customIcon = L.divIcon({
          className: 'custom-map-marker',
          html: `<div class="marker-icon ${isFireIncident ? 'fire' : 'water'}">
                  <i class="fas fa-${isFireIncident ? 'fire' : 'water'} fa-sm"></i>
                 </div>`,
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        });

        // Create the marker
        const marker = L.marker(position, {
          icon: customIcon,
          title: incident.title || 'Incident',
          alt: incident.title || 'Incident',
          riseOnHover: true
        });

        // Generate popup content
        const popupContent = `
          <div class="marker-info-content">
            <h3>${incident.title || 'Untitled Incident'}</h3>
            <p>${incident.incidentType?.name || 'Unknown Type'}</p>
            <p>${incident.address || ''}${incident.address && incident.city ? ', ' : ''}${incident.city || ''}</p>
            <p>${incident.status?.name || 'Unknown Status'}</p>
            <div class="marker-actions">
              <a href="view-incident.html?id=${incident.id}" class="info-btn">View Details</a>
            </div>
          </div>
        `;

        // Add popup to marker
        marker.bindPopup(popupContent, {
          maxWidth: 300
        });

        // Add marker to the cluster/layer group
        markers.addLayer(marker);
      });

      // Add marker cluster group to map
      map.addLayer(markers);

      // Determine appropriate zoom level based on geographic distribution
      // Default to a safe value if we can't determine it
      let maxZoom = 15; // Set a safe default value

      if (typeof mapConfig !== 'undefined' && typeof mapConfig.defaultMaxBoundsZoom !== 'undefined') {
        maxZoom = mapConfig.defaultMaxBoundsZoom;

        if (geoIncidents.length === 1 && mapConfig.stateBasedZoom && mapConfig.stateBasedZoom.singlePoint) {
          // Single incident - zoom in closely
          maxZoom = mapConfig.stateBasedZoom.singlePoint;
        } else if (states.size === 1 && mapConfig.stateBasedZoom && mapConfig.stateBasedZoom.singleState) {
          // All incidents in one state - medium zoom
          maxZoom = mapConfig.stateBasedZoom.singleState;
        } else if (states.size > 1 && states.size <= 3 && mapConfig.stateBasedZoom && mapConfig.stateBasedZoom.multiState) {
          // Incidents in 2-3 states - wider view
          maxZoom = mapConfig.stateBasedZoom.multiState;
        } else if ((states.size > 3 || states.size === 0) && mapConfig.stateBasedZoom && mapConfig.stateBasedZoom.countryWide) {
          // Nationwide incidents or unable to determine states - nationwide view
          maxZoom = mapConfig.stateBasedZoom.countryWide;
        }
      }

      console.log(`Map showing incidents across ${states.size} states, using maxZoom: ${maxZoom}`);

      // Adjust map to fit all markers with appropriate zoom
      if (markers.getLayers().length > 0) {
        try {
          console.log(`Using new direct approach with ${markers.getLayers().length} markers`);

          // Explicitly set the map's maxZoom to avoid any issues
          map.options.maxZoom = mapConfig.maxZoom || 19;

          // Determine the best center point and zoom level
          const centerPoint = bounds.getCenter();
          let zoomLevel = mapConfig.defaultZoom;

          // Set zoom based on number of incidents and their geographic spread
          if (markers.getLayers().length === 1) {
            // Single incident - zoom in closely
            zoomLevel = mapConfig.stateBasedZoom?.singlePoint || 15;

            // Get the exact position of the single marker
            const singleMarker = markers.getLayers()[0];

            // Use the single marker's position directly
            map.setView(singleMarker.getLatLng(), zoomLevel);
            console.log('Set view to single marker at zoom level', zoomLevel);
          } else {
            // Multiple incidents
            if (states.size === 1) {
              zoomLevel = mapConfig.stateBasedZoom?.singleState || 8;
            } else if (states.size > 1 && states.size <= 3) {
              zoomLevel = mapConfig.stateBasedZoom?.multiState || 5;
            } else {
              zoomLevel = mapConfig.stateBasedZoom?.countryWide || 4;
            }

            // Set the view directly without using fitBounds
            map.setView(centerPoint, zoomLevel);
            console.log('Set view to center point', centerPoint, 'at zoom level', zoomLevel);
          }

        } catch (error) {
          console.error('Error setting map view:', error);
          // Last resort fallback
          map.setView([39.8283, -98.5795], mapConfig.defaultZoom);
        }
      }

      // Add timestamp indicator if using cached data
      if (!isFreshData) {
        addCachedDataIndicator(map);
      }
    }
  } catch (error) {
    console.error('Error setting up map markers:', error);
    showNotification('Error displaying incident data on map.', 'error');
  }
}

// Helper function to try getting cached incidents
function tryGetCachedIncidents() {
  try {
    const cachedData = localStorage.getItem('map_incidents_cache');
    const cacheTimestamp = localStorage.getItem('map_incidents_timestamp');

    if (cachedData && cacheTimestamp) {
      // Check if cache is less than 30 minutes old
      const cacheAge = Date.now() - parseInt(cacheTimestamp);
      const MAX_CACHE_AGE = 30 * 60 * 1000; // 30 minutes

      if (cacheAge < MAX_CACHE_AGE) {
        const incidents = JSON.parse(cachedData);
        console.log('Using cached incident data:', incidents.length, 'incidents');
        return incidents;
      }
    }
  } catch (e) {
    console.error('Error retrieving cached incidents:', e);
  }
  return null;
}

// Helper function to show empty state on map
function showEmptyStateOnMap(map) {
  // Prevent multiple calls by checking if empty state is already shown
  if (window.mapEmptyStateShown) {
    return;
  }

  // Check if map is valid and properly initialized
  if (!map || typeof map.getContainer !== 'function') {
    console.error('Invalid or uninitialized map object passed to showEmptyStateOnMap');
    return;
  }

  // Clear any existing message first
  const existingMessage = document.getElementById('map-empty-message');
  if (existingMessage && existingMessage.parentNode) {
    existingMessage.parentNode.removeChild(existingMessage);
  }

  try {
    // Get map container and validate it exists
    const mapContainer = map.getContainer();
    if (!mapContainer) {
      console.error('Map container not found');
      return;
    }

    // Create a simple overlay without using Leaflet controls to avoid errors
    const overlay = document.createElement('div');
    overlay.id = 'map-empty-message';
    overlay.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1000;
      pointer-events: none;
    `;

    overlay.innerHTML = `
      <div style="
        text-align: center;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 20px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        pointer-events: auto;
        border: 1px solid rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        max-width: 300px;
      ">
        <i class="fas fa-map-marker-alt" style="font-size: 36px; color: #64b5f6; margin-bottom: 12px; text-shadow: 0 2px 4px rgba(0,0,0,0.3);"></i>
        <h4 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: white;">No Incidents Found</h4>
        <p style="margin: 0; font-size: 14px; color: rgba(255,255,255,0.9); line-height: 1.4;">No incidents with location data are currently available to display on the map.</p>
      </div>
    `;

    // Ensure map container has relative positioning
    if (mapContainer.style.position !== 'relative' && mapContainer.style.position !== 'absolute') {
      mapContainer.style.position = 'relative';
    }

    // Add overlay to map container
    mapContainer.appendChild(overlay);

    // Set flag to prevent multiple calls
    window.mapEmptyStateShown = true;

    console.log('Empty state overlay added to map successfully');

  } catch (error) {
    console.error('Error adding empty state overlay to map:', error);
    // Fallback: try to show message in map placeholder if it exists
    const mapPlaceholder = document.getElementById('map-placeholder');
    if (mapPlaceholder) {
      mapPlaceholder.innerHTML = `
        <div style="text-align:center; padding: 20px;">
          <i class="fas fa-map-marked-alt" style="font-size:48px; margin-bottom:10px; color: #666;"></i>
          <p style="color: #666;">No incidents with location data available.</p>
        </div>
      `;
    }
  }
}

// Helper function to add cached data indicator
function addCachedDataIndicator(map) {
  // Check if map is valid and properly initialized
  if (!map || typeof map.getContainer !== 'function') {
    console.error('Invalid or uninitialized map object passed to addCachedDataIndicator');
    return;
  }

  try {
    // Check if indicator already exists
    const existingIndicator = document.querySelector('.map-cache-indicator');
    if (existingIndicator) {
      return; // Don't add duplicate indicators
    }

    // Get map container for direct DOM manipulation (safer than Leaflet controls)
    const mapContainer = map.getContainer();
    if (!mapContainer) {
      console.error('Map container not found for cache indicator');
      return;
    }

    // Create indicator element directly
    const indicator = document.createElement('div');
    indicator.className = 'map-cache-indicator';
    indicator.style.cssText = `
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: rgba(255,255,255,0.9);
      padding: 5px 8px;
      border-radius: 4px;
      font-size: 11px;
      color: #666;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      pointer-events: none;
    `;

    indicator.innerHTML = `
      <i class="fas fa-history"></i> Showing cached data
    `;

    // Ensure map container has relative positioning
    if (mapContainer.style.position !== 'relative' && mapContainer.style.position !== 'absolute') {
      mapContainer.style.position = 'relative';
    }

    // Add indicator to map container
    mapContainer.appendChild(indicator);

    console.log('Cache indicator added to map successfully');

  } catch (error) {
    console.error('Error adding cached data indicator to map:', error);
  }
}

/**
 * Initialize Leaflet address autocomplete (using OpenStreetMap Nominatim)
 * This replaces the Google Maps autocomplete functionality
 */
function initializeLeafletAddressAutocomplete() {
  // Get all address input fields that should have autocomplete
  const addressInputs = document.querySelectorAll('#google_address');

  if (!addressInputs || addressInputs.length === 0) {
    return; // No inputs to enhance
  }

  addressInputs.forEach(input => {
    // Create a wrapper for the autocomplete
    const autocompleteWrapper = document.createElement('div');
    autocompleteWrapper.className = 'leaflet-autocomplete-wrapper';
    autocompleteWrapper.style.position = 'relative';
    autocompleteWrapper.style.width = '100%';

    // Create results container
    const resultsContainer = document.createElement('div');
    resultsContainer.className = 'leaflet-autocomplete-results';
    resultsContainer.style.display = 'none';
    resultsContainer.style.position = 'absolute';
    resultsContainer.style.width = '100%';
    resultsContainer.style.maxHeight = '200px';
    resultsContainer.style.overflowY = 'auto';
    resultsContainer.style.backgroundColor = '#fff';
    resultsContainer.style.border = '1px solid #ccc';
    resultsContainer.style.borderTop = 'none';
    resultsContainer.style.zIndex = '1000';

    // Replace the input with the wrapper and put the input inside it
    input.parentNode.insertBefore(autocompleteWrapper, input);
    autocompleteWrapper.appendChild(input);
    autocompleteWrapper.appendChild(resultsContainer);

    // Set up event listeners for the input
    let debounceTimeout = null;

    input.addEventListener('input', function() {
      clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => {
        const query = input.value.trim();
        if (query.length < 3) {
          resultsContainer.style.display = 'none';
          return;
        }

        // Search for addresses using Nominatim
        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=us&limit=5`)
          .then(response => response.json())
          .then(data => {
            resultsContainer.innerHTML = '';
            if (data && data.length > 0) {
              data.forEach(place => {
                const resultItem = document.createElement('div');
                resultItem.className = 'autocomplete-item';
                resultItem.style.padding = '10px';
                resultItem.style.cursor = 'pointer';
                resultItem.style.borderBottom = '1px solid #eee';
                resultItem.textContent = place.display_name;

                // Highlight the item on hover
                resultItem.addEventListener('mouseenter', function() {
                  resultItem.style.backgroundColor = '#f5f5f5';
                });
                resultItem.addEventListener('mouseleave', function() {
                  resultItem.style.backgroundColor = '';
                });

                // Handle click to select this address
                resultItem.addEventListener('click', function() {
                  input.value = place.display_name;
                  resultsContainer.style.display = 'none';

                  // Extract address components
                  const addressData = parseOSMAddress(place);

                  // Fill the form fields
                  fillAddressForm(addressData, input);
                });

                resultsContainer.appendChild(resultItem);
              });

              resultsContainer.style.display = 'block';
            } else {
              resultsContainer.style.display = 'none';
            }
          })
          .catch(error => {
            console.error('Error fetching address suggestions:', error);
            resultsContainer.style.display = 'none';
          });
      }, 300); // 300ms debounce
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
      if (!autocompleteWrapper.contains(e.target)) {
        resultsContainer.style.display = 'none';
      }
    });
  });
}

/**
 * Parse OpenStreetMap address data into components
 * @param {Object} place - OSM place object
 * @returns {Object} Structured address data
 */
function parseOSMAddress(place) {
  const addressData = {
    latitude: place.lat,
    longitude: place.lon,
    fullAddress: place.display_name
  };

  // Parse the address components from the OSM address
  if (place.address) {
    addressData.streetNumber = place.address.house_number;
    addressData.street = place.address.road;
    addressData.city = place.address.city || place.address.town || place.address.village;
    addressData.state = place.address.state;
    addressData.zip = place.address.postcode;
    addressData.county = place.address.county ? place.address.county.replace(' County', '') : '';
  }

  return addressData;
}

/**
 * Fill the address form fields with data
 * @param {Object} addressData - Parsed address data
 * @param {HTMLElement} input - The input element that triggered the autocomplete
 */
function fillAddressForm(addressData, input) {
  // Find related inputs to fill
  const formGroup = findParentForm(input);
  if (formGroup) {
    // Fill address field with street number and name if available
    if (addressData.streetNumber && addressData.street) {
      document.getElementById('address').value = `${addressData.streetNumber} ${addressData.street}`;
    } else {
      // Otherwise use the fullAddress as a fallback
      document.getElementById('address').value = addressData.fullAddress.split(',')[0];
    }

    // Fill city field
    if (addressData.city && document.getElementById('city')) {
      document.getElementById('city').value = addressData.city;
    }

    // Handle the state dropdown - set both the dropdown and hidden field
    if (addressData.state) {
      const stateDropdown = document.getElementById('state_select');
      const stateHidden = document.getElementById('state');

      if (stateDropdown && stateHidden) {
        // State dropdown needs to be set by value, and we need to find the matching option
        setDropdownByText(stateDropdown, addressData.state);
        stateHidden.value = addressData.state;

        // Trigger a change event to load counties
        const event = new Event('change');
        stateDropdown.dispatchEvent(event);

        // After state is selected and counties are loaded, we need to set the county
        setTimeout(() => {
          if (addressData.county) {
            const countyDropdown = document.getElementById('county_select');
            const countyHidden = document.getElementById('county');

            if (countyDropdown && countyHidden) {
              setDropdownByText(countyDropdown, addressData.county);
              countyHidden.value = addressData.county;

              // Trigger a change event to load zip codes
              const event = new Event('change');
              countyDropdown.dispatchEvent(event);

              // After county is selected and zip codes are loaded, we need to set the zip
              setTimeout(() => {
                if (addressData.zip) {
                  const zipDropdown = document.getElementById('zip_select');
                  if (zipDropdown) {
                    setDropdownByValue(zipDropdown, addressData.zip);

                    // Trigger a change event to load any zip-related fields
                    const event = new Event('change');
                    zipDropdown.dispatchEvent(event);
                  }
                }
              }, 1000); // Wait for zip codes to load
            }
          }
        }, 1000); // Wait for counties to load
      } else {
        // Fallback to the old text input if dropdown isn't available
        if (document.getElementById('state')) {
          document.getElementById('state').value = addressData.state;
        }
        if (document.getElementById('county')) {
          document.getElementById('county').value = addressData.county || '';
        }
        if (document.getElementById('zip_code')) {
          document.getElementById('zip_code').value = addressData.zip || '';
        }
      }
    }

    // Store lat/lng in hidden fields if they exist
    if (document.getElementById('latitude')) {
      document.getElementById('latitude').value = addressData.latitude;
    }
    if (document.getElementById('longitude')) {
      document.getElementById('longitude').value = addressData.longitude;
    }

    // Trigger owner lookup if button exists
    const lookupBtn = document.getElementById('refreshOwnerDataBtn');
    if (lookupBtn && addressData.city && addressData.state) {
      lookupBtn.removeAttribute('disabled');
      // Auto-fetch property data
      if (typeof fetchPropertyData === 'function') {
        fetchPropertyData();
      }
    }
  }
}

// Reuse some utility functions from the Google Maps integration
// Helper function to set dropdown by option value
function setDropdownByValue(dropdown, value) {
  if (!dropdown) return false;

  for (let i = 0; i < dropdown.options.length; i++) {
    if (dropdown.options[i].value === value) {
      dropdown.selectedIndex = i;
      return true;
    }
  }
  return false;
}

// Helper function to set dropdown by option text
function setDropdownByText(dropdown, text) {
  if (!dropdown) return false;

  for (let i = 0; i < dropdown.options.length; i++) {
    if (dropdown.options[i].text.indexOf(text) !== -1) {
      dropdown.selectedIndex = i;
      return true;
    }
  }
  return false;
}

// Find the parent form of an element
function findParentForm(element) {
  let parent = element.parentElement;
  while (parent && parent.tagName !== 'FORM') {
    parent = parent.parentElement;
  }
  return parent;
}

// Initialize all Leaflet Maps integrations when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing Leaflet integrations');

  // Initialize address autocomplete
  initializeLeafletAddressAutocomplete();

  // Initialize map if we're on a page that needs it
  if (document.getElementById('map') || document.getElementById('dispatch-map')) {
    initMap();
  }

  // Hook up refresh button
  const refreshBtn = document.getElementById('refreshOwnerDataBtn');
  if (refreshBtn && typeof fetchPropertyData === 'function') {
    refreshBtn.addEventListener('click', fetchPropertyData);
  }
});

// Function to show success/error message (backup in case modern-dispatch.js isn't loaded)
function showNotification(message, type = 'success') {
  // Use the global function if available
  if (typeof window.modernDispatchShowNotification === 'function') {
    window.modernDispatchShowNotification(message, type);
    return;
  }

  // Simple default implementation if no global function exists
  const notificationContainer = document.getElementById('notificationContainer') ||
    (() => {
      const container = document.createElement('div');
      container.id = 'notificationContainer';
      container.style.position = 'fixed';
      container.style.top = '20px';
      container.style.right = '20px';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
      return container;
    })();

  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.backgroundColor = type === 'success' ? '#4caf50' :
                                     type === 'error' ? '#f44336' :
                                     type === 'warning' ? '#ff9800' : '#2196f3';
  notification.style.color = 'white';
  notification.style.padding = '15px';
  notification.style.marginBottom = '10px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
  notification.style.minWidth = '250px';
  notification.textContent = message;

  notificationContainer.appendChild(notification);

  // Remove notification after 5 seconds
  setTimeout(() => {
    if (notification.parentNode === notificationContainer) {
      notificationContainer.removeChild(notification);
    }
  }, 5000);
}

// Function to reset map initialization flag (useful for testing)
function resetMapInitialization() {
  window.leafletMapInitialized = false;
  if (leafletMap) {
    leafletMap.remove();
    leafletMap = null;
  }
}

// Export functions for use in other scripts
window.fireAlerts = window.fireAlerts || {};
window.fireAlerts.maps = {
  initializeAutocomplete: initializeLeafletAddressAutocomplete,
  initMap: initMap,
  fetchAndDisplayIncidents: fetchAndDisplayIncidents,
  resetMapInitialization: resetMapInitialization
};