/**
 * <PERSON><PERSON>t to seed subscription plans into the database.
 * This creates a company with a specific subscription plan type.
 */
require('dotenv').config();
const db = require('../models');

const subscriptionPlans = [
  {
    name: 'Fire Department Basic',
    address: '123 Main Street',
    city: 'Anytown',
    state: 'CA',
    zip: '90210',
    phone: '************',
    email: '<EMAIL>',
    contactPerson: '<PERSON>',
    status: true,
    subscriptionType: 'basic',
    subscriptionExpiry: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
  },
  {
    name: 'Fire Department Standard',
    address: '456 Oak Avenue',
    city: 'Anytown',
    state: 'CA',
    zip: '90210',
    phone: '************',
    email: '<EMAIL>',
    contactPerson: '<PERSON>',
    status: true,
    subscriptionType: 'standard',
    subscriptionExpiry: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
  },
  {
    name: 'Fire Department Premium',
    address: '789 Elm Boulevard',
    city: 'Anytown',
    state: 'CA',
    zip: '90210',
    phone: '************',
    email: '<EMAIL>',
    contact<PERSON>erson: '<PERSON>',
    status: true,
    subscriptionType: 'premium',
    subscriptionExpiry: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
  },
  {
    name: 'Fire Department Enterprise',
    address: '101 Pine Street',
    city: 'Anytown',
    state: 'CA',
    zip: '90210',
    phone: '************',
    email: '<EMAIL>',
    contactPerson: 'Sarah Williams',
    status: true,
    subscriptionType: 'enterprise',
    subscriptionExpiry: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
  }
];

/**
 * Seeds subscription plans into the companies table
 */
async function seedSubscriptionPlans() {
  try {
    console.log('Checking for existing subscription plans...');
    
    // Process each plan
    for (const plan of subscriptionPlans) {
      // Check if we have a company with this subscription type
      const existingPlan = await db.company.findOne({
        where: { subscriptionType: plan.subscriptionType }
      });
      
      if (existingPlan) {
        console.log(`✅ '${plan.subscriptionType}' subscription plan already exists.`);
      } else {
        // Create a new company with the subscription plan type
        const newPlan = await db.company.create(plan);
        console.log(`✅ Successfully created '${plan.subscriptionType}' subscription plan for company: ${newPlan.name}`);
        console.log(`   - ID: ${newPlan.id}`);
        console.log(`   - Expires: ${newPlan.subscriptionExpiry}`);
      }
    }
    
    console.log('Subscription plan seeding complete.');
  } catch (error) {
    console.error('Error seeding subscription plans:', error);
    throw error;
  }
}

// Run if executed directly
if (require.main === module) {
  seedSubscriptionPlans()
    .then(() => {
      process.exit(0);
    })
    .catch(error => {
      console.error('Fatal error during subscription plan seeding:', error);
      process.exit(1);
    });
}

module.exports = seedSubscriptionPlans;
