require('dotenv').config();
const express = require('express');
const cors = require('cors');
const db = require('./models');
const path = require('path');
const cookieParser = require('cookie-parser');

// Run security validation for production
if (process.env.NODE_ENV === 'production') {
  const validateProductionSecurity = require('./scripts/validate-production-security');
  validateProductionSecurity();
}

// Initialize the app
const app = express();

// Health check endpoints (before other middleware)
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: require('./package.json').version
  });
});

app.get('/api/health', async (req, res) => {
  try {
    await db.sequelize.authenticate();
    res.status(200).json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Initialize notification scheduler
if (process.env.DISABLE_NOTIFICATION_WORKER !== 'true') {
  console.log('Initializing notification processing system...');
  require('./workers/scheduler');
}

// Database seeding will be handled in the startup sequence below

// Enhanced CORS configuration to handle cookies
app.use(cors({
  origin: process.env.CORS_ORIGIN || true,
  credentials: true
}));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser()); // Add cookie parser

// Serve static files from the parent directory (HTML, CSS, JS)
app.use(express.static(path.join(__dirname, '..')));

// Page authentication middleware for HTML pages
app.use(require('./middleware/page-auth'));

// API Routes - these will be protected by the API auth middleware
app.use('/api/auth', require('./routes/auth'));
app.use('/api/incidents', require('./routes/incidents'));
app.use('/api/locations', require('./routes/locations'));
app.use('/api/companies', require('./routes/companies')); // Companies route
app.use('/api/settings', require('./routes/settings'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/company-types', require('./routes/companyTypes'));
app.use('/api/subscription-plans', require('./routes/subscriptionPlans'));
app.use('/api/users', require('./routes/users')); // Importing users route
app.use('/api/logs', require('./routes/logs')); // Registering logs route
app.use('/api/account', require('./routes/account')); // Account management routes

// API index route
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to FireAlerts911 API' });
});

// Catch-all route for unmatched API routes (must be after all API routes)
app.get('/api/*', (req, res) => {
  res.status(404).json({ message: 'API endpoint not found' });
});

// Catch-all route to serve index.html for any route not matched
app.get('*', (req, res) => {
  // Use a regular expression to test if the path ends with a common file extension
  if (/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i.test(req.path)) {
    return res.status(404).send('File not found');
  }

  // Check if index.html exists before trying to serve it (index.html now handles redirects)
  const indexPath = path.join(__dirname, '..', 'index.html');
  if (require('fs').existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    // If index.html doesn't exist, return a simple response
    res.status(404).json({
      message: 'Page not found',
      note: 'This is a development API server. Frontend files may not be available.'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Set port and start server
const PORT = process.env.PORT || 5000;

// Startup sequence: Database connection -> Seeding -> Server start
async function startServer() {
  try {
    // Step 1: Test database connection
    console.log('🔌 Testing database connection...');
    await db.sequelize.authenticate();
    console.log('✅ Database connected successfully');

    // Step 2: Run complete database seeding
    console.log('🌱 Checking and seeding all required application data...');
    const seedAllData = require('./scripts/seed-all');

    try {
      await seedAllData();
      console.log('✅ All application data verified/seeded successfully');
    } catch (err) {
      console.error('⚠️  Warning: Could not complete full seeding:', err.message);
      // Fallback to location data only if complete seeding fails
      console.log('🔄 Falling back to location data seeding only...');
      const seedLocationData = require('./scripts/seed-location-data');
      await seedLocationData();
      console.log('✅ Minimum required data seeded');
    }

    // Step 3: Start the server
    console.log('🚀 Starting HTTP server...');
    app.listen(PORT, () => {
      console.log(`✅ Server is running on port ${PORT}`);
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔑 Development mode: Default admin username is "admin"');
      }
      console.log('🌐 Application ready at: http://localhost:' + (PORT === 5000 ? '' : PORT));
    });

  } catch (err) {
    console.error('❌ Failed to start server:', err);

    // More detailed error logging
    if (err.original) {
      console.error('SQL Error:', err.original.sqlMessage);
      console.error('SQL Query:', err.sql);
    }

    console.error('🚨 Server startup failed. Exiting...');
    process.exit(1);
  }
}

// Start the server
startServer();
