/**
 * Production Seeding Validation Script
 * 
 * This script validates that the database contains only production-appropriate data
 * and no demo/test content after seeding.
 */

require('dotenv').config();
const { sequelize } = require('../models');

/**
 * Validate admin user configuration
 */
async function validateAdminUser() {
  console.log('🔍 Validating admin user...');
  
  try {
    const { user } = require('../models');
    
    const adminUser = await user.findOne({
      where: { username: 'admin', role: 'admin' }
    });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return false;
    }
    
    console.log('✅ Admin user exists');
    console.log(`   - Username: ${adminUser.username}`);
    console.log(`   - Email: ${adminUser.email}`);
    console.log(`   - Role: ${adminUser.role}`);
    
    return true;
  } catch (error) {
    console.log('❌ Error validating admin user:', error.message);
    return false;
  }
}

/**
 * Validate location data
 */
async function validateLocationData() {
  console.log('🔍 Validating location data...');
  
  try {
    const { state, county } = require('../models');
    
    const stateCount = await state.count();
    const countyCount = await county.count();
    
    console.log(`✅ Location data present`);
    console.log(`   - States: ${stateCount}`);
    console.log(`   - Counties: ${countyCount}`);
    
    // Check for proper county naming
    const countiesWithoutSuffix = await county.count({
      where: {
        name: {
          [sequelize.Op.notLike]: '%County%',
          [sequelize.Op.notLike]: '%Parish%',
          [sequelize.Op.notLike]: '%Borough%'
        }
      }
    });
    
    if (countiesWithoutSuffix > 0) {
      console.log(`⚠️  ${countiesWithoutSuffix} counties without proper suffix`);
    } else {
      console.log('✅ All counties have proper naming conventions');
    }
    
    return stateCount >= 50 && countyCount > 3000;
  } catch (error) {
    console.log('❌ Error validating location data:', error.message);
    return false;
  }
}

/**
 * Validate incident types and statuses
 */
async function validateReferenceData() {
  console.log('🔍 Validating reference data...');
  
  try {
    const { incidentType, status, companyType } = require('../models');
    
    const incidentTypeCount = await incidentType.count();
    const statusCount = await status.count();
    const companyTypeCount = await companyType.count();
    
    console.log('✅ Reference data present');
    console.log(`   - Incident types: ${incidentTypeCount}`);
    console.log(`   - Statuses: ${statusCount}`);
    console.log(`   - Company types: ${companyTypeCount}`);
    
    return incidentTypeCount >= 7 && statusCount >= 5 && companyTypeCount >= 8;
  } catch (error) {
    console.log('❌ Error validating reference data:', error.message);
    return false;
  }
}

/**
 * Check for demo/test data
 */
async function validateNoDemoData() {
  console.log('🔍 Checking for demo/test data...');
  
  try {
    const { incident, company, user } = require('../models');
    
    // Check for test incidents
    const testIncidents = await incident.count({
      where: {
        [sequelize.Op.or]: [
          { description: { [sequelize.Op.like]: '%test%' } },
          { address: { [sequelize.Op.like]: '%test%' } },
          { title: { [sequelize.Op.like]: '%test%' } },
          { description: { [sequelize.Op.like]: '%demo%' } },
          { address: { [sequelize.Op.like]: '%demo%' } },
          { title: { [sequelize.Op.like]: '%demo%' } }
        ]
      }
    });
    
    // Check for demo companies
    const demoCompanies = await company.count({
      where: {
        [sequelize.Op.or]: [
          { name: { [sequelize.Op.like]: '%test%' } },
          { name: { [sequelize.Op.like]: '%demo%' } },
          { name: { [sequelize.Op.like]: '%sample%' } },
          { name: { [sequelize.Op.like]: '%Fire Department%' } }
        ]
      }
    });
    
    // Check for test users (excluding admin)
    const testUsers = await user.count({
      where: {
        username: { [sequelize.Op.ne]: 'admin' },
        [sequelize.Op.or]: [
          { username: { [sequelize.Op.like]: '%test%' } },
          { email: { [sequelize.Op.like]: '%test%' } },
          { firstName: { [sequelize.Op.like]: '%test%' } }
        ]
      }
    });
    
    let isClean = true;
    
    if (testIncidents > 0) {
      console.log(`❌ Found ${testIncidents} test incidents`);
      isClean = false;
    } else {
      console.log('✅ No test incidents found');
    }
    
    if (demoCompanies > 0) {
      console.log(`❌ Found ${demoCompanies} demo companies`);
      isClean = false;
    } else {
      console.log('✅ No demo companies found');
    }
    
    if (testUsers > 0) {
      console.log(`❌ Found ${testUsers} test users`);
      isClean = false;
    } else {
      console.log('✅ No test users found');
    }
    
    return isClean;
  } catch (error) {
    console.log('❌ Error checking for demo data:', error.message);
    return false;
  }
}

/**
 * Validate system settings
 */
async function validateSystemSettings() {
  console.log('🔍 Validating system settings...');
  
  try {
    const { setting } = require('../models');
    
    const settingCount = await setting.count();
    
    // Check for essential settings
    const essentialSettings = [
      'site_name',
      'contact_email',
      'sms_provider',
      'email_from'
    ];
    
    let foundSettings = 0;
    for (const settingName of essentialSettings) {
      const exists = await setting.findOne({
        where: { name: settingName }
      });
      if (exists) foundSettings++;
    }
    
    console.log(`✅ System settings present: ${settingCount} total`);
    console.log(`✅ Essential settings found: ${foundSettings}/${essentialSettings.length}`);
    
    return foundSettings === essentialSettings.length;
  } catch (error) {
    console.log('❌ Error validating system settings:', error.message);
    return false;
  }
}

/**
 * Main validation function
 */
async function validateProductionSeeding() {
  console.log('🔍 PRODUCTION SEEDING VALIDATION');
  console.log('=================================');
  
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established\n');
    
    // Run all validations
    const results = {
      adminUser: await validateAdminUser(),
      locationData: await validateLocationData(),
      referenceData: await validateReferenceData(),
      noDemoData: await validateNoDemoData(),
      systemSettings: await validateSystemSettings()
    };
    
    console.log('\n📋 VALIDATION SUMMARY');
    console.log('====================');
    
    let allPassed = true;
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${test.padEnd(15)}: ${status}`);
      if (!passed) allPassed = false;
    });
    
    console.log('\n' + '='.repeat(40));
    
    if (allPassed) {
      console.log('🎉 PRODUCTION SEEDING VALIDATION PASSED');
      console.log('✅ Database is ready for production use');
      console.log('✅ No demo/test data found');
      console.log('✅ All essential data present');
    } else {
      console.log('❌ PRODUCTION SEEDING VALIDATION FAILED');
      console.log('⚠️  Database may not be ready for production');
      console.log('⚠️  Review failed validations above');
    }
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ Validation error:', error.message);
    return false;
  } finally {
    await sequelize.close();
  }
}

// Run if executed directly
if (require.main === module) {
  validateProductionSeeding()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(err => {
      console.error('❌ Unhandled error:', err);
      process.exit(1);
    });
}

module.exports = validateProductionSeeding;
