const mysql = require('mysql2');
const dotenv = require('dotenv');

dotenv.config();

const maxRetries = 30;
let retries = 0;

const config = {
  host: process.env.DB_HOST || 'sqlserver',
  user: process.env.DB_USER || 'dispatchuser',
  password: process.env.DB_PASSWORD || 'dispatchpassword',
  database: process.env.DB_NAME || 'dispatchdb',
};

console.log(`Waiting for MySQL database at ${config.host}...`);

function tryConnection() {
  let connection;
  try {
    connection = mysql.createConnection({
      host: config.host,
      user: config.user,
      password: config.password
    });
    
    connection.connect(err => {
      if (err) {
        handleConnectionFailure(err, connection);
        return;
      }
      
      console.log('Connected to database server!');
      
      // Check if database exists
      connection.query(`SHOW DATABASES LIKE '${config.database}'`, (err, results) => {
        if (err || !results || results.length === 0) {
          console.log(`Database '${config.database}' doesn't exist. Creating it...`);
          
          connection.query(`CREATE DATABASE IF NOT EXISTS ${config.database}`, err => {
            // Safely close connection when done (success or failure)
            safelyCloseConnection(connection);
            
            if (err) {
              console.error('Failed to create database:', err);
              process.exit(1);
            }
            
            console.log(`Database '${config.database}' created successfully!`);
            console.log('Database is ready!');
            process.exit(0);
          });
        } else {
          console.log(`Database '${config.database}' already exists`);
          console.log('Database is ready!');
          
          // Safely close connection when done
          safelyCloseConnection(connection);
          process.exit(0);
        }
      });
    });
    
    // Handle connection error events
    connection.on('error', err => {
      handleConnectionFailure(err, connection);
    });
    
  } catch (error) {
    console.error('Error creating connection:', error);
    
    retries++;
    if (retries > maxRetries) {
      console.error('Max retries reached. Database connection failed.');
      process.exit(1);
    }
    
    console.log(`Connection attempt ${retries}/${maxRetries} failed. Retrying in 1 second...`);
    setTimeout(tryConnection, 1000);
  }
}

function handleConnectionFailure(err, connection) {
  retries++;
  
  if (retries > maxRetries) {
    safelyCloseConnection(connection);
    console.error('Max retries reached. Database connection failed:', err);
    process.exit(1);
  }
  
  console.log(`Connection attempt ${retries}/${maxRetries} failed. Retrying in 1 second...`);
  safelyCloseConnection(connection);
  setTimeout(tryConnection, 1000);
}

function safelyCloseConnection(connection) {
  if (connection) {
    try {
      // Check if connection is still valid before ending
      if (connection.state !== 'disconnected' && connection.state !== 'protocol_error') {
        connection.end(err => {
          if (err) {
            console.error('Error closing connection:', err.message);
          }
        });
      }
    } catch (e) {
      console.error('Error while closing connection:', e.message);
    }
  }
}

// Start trying to connect
tryConnection();
