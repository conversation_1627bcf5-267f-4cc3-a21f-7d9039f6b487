/**
 * Permission-based access control middleware
 * This middleware checks if the user's role has the required permission
 */
const { hasPermission } = require('../models/permissions');

/**
 * Middleware that checks if the user has the specified permission
 * @param {String} requiredPermission - The permission required to access the route
 * @returns {Function} Express middleware function
 */
module.exports = function(requiredPermission) {
  return function(req, res, next) {
    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({ msg: 'No user authentication found' });
    }
      // Special case for 'admin' role - grant all permissions
    if (req.user.role === 'admin') {
      console.log('Admin role detected - bypassing permission check for:', requiredPermission);
      next();
      return;
    }
    
    // Enhanced logging for permission checks
    const hasRequiredPermission = hasPermission(req.user.role, requiredPermission);
    console.log('Permission check:', { 
      userId: req.user.id,
      userRole: req.user.role,
      requiredPermission,
      hasPermission: hasRequiredPermission,
      userPermissions: req.user.permissions || []
    });
    
    // Check if user's role has the required permission
    if (!hasRequiredPermission) {
      console.warn(`Permission denied: User ${req.user.id} with role ${req.user.role} tried to access ${req.originalUrl} which requires ${requiredPermission}`);
      return res.status(403).json({
        msg: `Permission denied. Required permission: ${requiredPermission}`,
        requiredPermission: requiredPermission,
        yourRole: req.user.role
      });
    }
    
    // User has the required permission, proceed
    next();
  };
};

/**
 * Middleware that checks if the user has ALL the specified permissions
 * @param {Array} requiredPermissions - Array of permissions required to access the route
 * @returns {Function} Express middleware function
 */
module.exports.all = function(requiredPermissions) {
  return function(req, res, next) {
    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({ msg: 'No user authentication found' });
    }
    
    // Check if user's role has ALL the required permissions
    const missingPermissions = requiredPermissions.filter(
      permission => !hasPermission(req.user.role, permission)
    );
    
    if (missingPermissions.length > 0) {
      return res.status(403).json({ 
        msg: `Permission denied. Missing permissions: ${missingPermissions.join(', ')}`,
        missingPermissions,
        yourRole: req.user.role
      });
    }
    
    // User has all the required permissions, proceed
    next();
  };
};

/**
 * Middleware that checks if the user has ANY of the specified permissions
 * @param {Array} requiredPermissions - Array of permissions, any of which grants access
 * @returns {Function} Express middleware function
 */
module.exports.any = function(requiredPermissions) {
  return function(req, res, next) {
    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({ msg: 'No user authentication found' });
    }
    
    // Check if user's role has ANY of the required permissions
    const hasAnyPermission = requiredPermissions.some(
      permission => hasPermission(req.user.role, permission)
    );
    
    if (!hasAnyPermission) {
      return res.status(403).json({ 
        msg: `Permission denied. Required any of: ${requiredPermissions.join(', ')}`,
        requiredPermissions,
        yourRole: req.user.role
      });
    }
    
    // User has at least one of the required permissions, proceed
    next();
  };
};