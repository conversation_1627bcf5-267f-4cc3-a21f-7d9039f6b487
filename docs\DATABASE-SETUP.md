# FireAlerts911 Database Setup Guide

This comprehensive guide covers database initialization, seeding, and management for the FireAlerts911 application.

## 📋 **Table of Contents**

1. [Overview](#overview)
2. [Database Schema](#database-schema)
3. [Seeding Options](#seeding-options)
4. [Production Seeding](#production-seeding)
5. [Development Seeding](#development-seeding)
6. [Location Data Management](#location-data-management)
7. [Migration Scripts](#migration-scripts)
8. [Troubleshooting](#troubleshooting)

---

## 🎯 **Overview**

The FireAlerts911 system uses **JavaScript-based seeding** optimized for production deployments. This provides better reliability, error handling, and ensures no demo/test data is created in production environments.

### **Database Architecture**
- **Primary Database**: MySQL 8.0+
- **Schema Management**: SQL files + Sequelize models
- **Seeding**: JavaScript-based with environment validation
- **Migrations**: Automated schema updates

---

## 🏗️ **Database Schema**

### **Schema Creation**
The database schema is created through multiple methods:

#### **Docker Initialization**
- **Schema Creation**: `docker/mysql-init/01-schema.sql` - Creates all tables and relationships
- **Bootstrap Data**: `docker/mysql-init/02-seed-data.sql` - Minimal admin user and essential settings
- **Full Seeding**: JavaScript scripts handle comprehensive data population

#### **Schema Consistency**
✅ **Docker SQL schema includes all migrated fields** (Estated API fields, property data, etc.)
✅ **Sequelize models match Docker schema exactly**
✅ **No schema drift between environments**

### **Core Tables**
- **users** - Unified user management (admin, company_admin, dispatcher)
- **companies** - Organization management (formerly subscribers)
- **incidents** - Emergency incident records
- **incident_details** - Extended incident information
- **notifications** - Notification delivery tracking
- **states** - US states and territories
- **counties** - US counties with proper naming
- **incident_types** - Fire, water, emergency categories
- **statuses** - Incident status tracking

---

## 🚀 **Seeding Options**

### **Quick Start Commands**

#### **For Production Deployments:**
```bash
npm run seed-production
```
This command populates your database with **only essential system data** - no demo content.

#### **For Development/Testing:**
```bash
npm run seed
```
This command provides comprehensive seeding including all location data.

### **Available Seed Scripts**

#### **Production Scripts (Recommended)**

1. **Production Seeding (`npm run seed-production`)**
   - **Recommended for all production deployments**
   - Enhanced validation and safety checks
   - Environment validation (warns if running in development)
   - No demo/test data creation
   - Comprehensive logging and rollback capability
   - Populates: admin user, incident types, statuses, complete US locations, company types, and system settings

2. **Standard Seeding (`npm run seed` or `npm run seed-all`)**
   - **Recommended for development/testing environments**
   - Runs all seed scripts in the correct dependency order
   - Includes error handling and validation
   - Populates: admin user, incident types, statuses, complete locations, company types, and system settings

3. **Core Data Only (`npm run seed-core`)**
   - Seeds only essential data: admin user, incident types, statuses, and system settings
   - Use when you want to add location and company data separately

#### **Individual Component Scripts**

4. **Location Data (`npm run seed-locations`)**
   - Seeds comprehensive location data using the complete seeding script
   - Includes all 50 US states + DC and ~3,100+ counties

5. **Comprehensive Location Data (`npm run seed-complete-locations`)**
   - **NEW: Complete US coverage** - All 50 states + DC and ~3,100+ counties
   - Batch processing for optimal performance
   - Progress tracking and validation
   - Recommended for production deployments

#### **Advanced Location Seeding Options**

6. **States Only (`npm run seed-all-states`)**
   - Seeds all 50 US states + DC only
   - Fast execution for state-only requirements

7. **Counties Only (`npm run seed-all-counties`)**
   - Seeds all counties (requires states to exist first)
   - Use after seeding states separately

8. **Reseed Locations (`npm run reseed-locations`)**
   - Clears existing location data and reseeds everything
   - Use for data refresh or corrections

#### **Other Component Scripts**

9. **Company Types (`npm run seed-company-types`)**
   - Seeds company types for subscriber registration forms

10. **Subscription Plans (`npm run seed-subscription-plans`)**
    - Seeds available subscription plans and pricing tiers

---

## 🔒 **Production Seeding**

### **Production Seeding Overview**
The production seeding configuration creates only the essential data needed for the FireAlerts911 system to function in a production environment.

### **✅ What Gets Seeded in Production:**

1. **Admin User Account**
   - Username: `admin`
   - Password: `FireAdmin2025!` (must be changed immediately)
   - Role: `admin`
   - Email: `<EMAIL>`

2. **Complete US Location Data**
   - All 50 US states + District of Columbia
   - ~3,100+ counties with proper naming conventions
   - Consistent "County" suffix (except Louisiana parishes, Alaska boroughs)
   - No duplicate or conflicting location data

3. **Incident Types**
   - Residential Structure Fire
   - Commercial Structure Fire
   - Vehicle Fire
   - Wildland Fire
   - Flood
   - Water Rescue
   - Water Main Break

4. **System Statuses**
   - Active
   - Resolved
   - Pending
   - Critical
   - False Alarm

5. **Company Types**
   - Fire Department
   - EMS
   - Police Department
   - Emergency Management
   - Dispatch Center
   - Hospital
   - Government Agency
   - Private Company
   - Other

6. **Essential System Settings**
   - Site configuration
   - Email settings
   - Map defaults
   - Notification settings
   - API key placeholders (empty, to be configured by admin)

### **❌ What is NOT Seeded in Production:**

1. **No Demo Incidents**
   - No sample fire incidents
   - No test water emergencies
   - No fake incident data

2. **No Test Companies/Subscribers**
   - No sample fire departments
   - No demo subscription plans
   - No test organizations

3. **No Mock User Accounts**
   - Only the admin user is created
   - No test subscribers
   - No demo dispatchers

4. **No Sample Data**
   - No test notifications
   - No demo media files
   - No fake activity logs

### **Production Deployment Commands**

#### **Recommended Production Seeding:**
```bash
npm run seed-production
```

This command includes:
- Environment validation
- Safety checks
- Comprehensive logging
- Transaction rollback on errors
- No demo data creation

#### **Alternative Commands:**
```bash
# Standard seeding (also production-safe)
npm run seed

# Core data only (minimal seeding)
npm run seed-core

# Force production seeding (skip environment checks)
npm run seed-production --force

# Dry run (validation only, no changes)
npm run seed-production --dry-run
```

### **Security Considerations**

#### **Default Admin Account:**
- **Username:** `admin`
- **Password:** `FireAdmin2025!`
- **⚠️ CRITICAL:** Change password immediately after first login

#### **API Keys:**
- All API keys are seeded as empty placeholders
- Must be configured through admin panel:
  - Twilio (SMS notifications)
  - Google Maps (geocoding)
  - Mailgun (email notifications)
  - Estated (property information)

#### **Environment Validation:**
- Production seeding script validates environment
- Warns if running in development mode
- Checks for existing demo data
- Provides rollback capability

---

## 🛠️ **Development Seeding**

### **Setup Process for New Installations:**

1. **Start the system** (Docker will create schema and bootstrap data):
   ```bash
   docker-compose up -d
   ```

2. **Populate comprehensive data**:
   ```bash
   npm run seed
   ```

3. **Verify installation**:
   - Login with admin credentials: `admin` / `FireAdmin2025!`
   - Check that all dropdown menus are populated (states, counties, company types)

### **For Existing Installations:**
```bash
# Add missing location data (legacy - limited coverage)
npm run seed-locations

# Add comprehensive location data (recommended)
npm run seed-complete-locations

# Add missing company types
npm run seed-company-types

# Add missing subscription plans
npm run seed-subscription-plans
```

### **What Gets Seeded in Development**
The comprehensive seeding process populates:

- ✅ **Admin User**: username=`admin`, password=`FireAdmin2025!`
- ✅ **Incident Types**: Fire, water, and emergency incident categories
- ✅ **Status Types**: Active, resolved, pending, critical, false alarm
- ✅ **Location Data**: 8 major US states and their counties (legacy)
- 🆕 **Comprehensive Location Data**: All 50 US states + DC and ~3,100+ counties
- ✅ **Company Types**: Various business categories for subscriber registration
- ✅ **Subscription Plans**: Available service tiers and pricing
- ✅ **System Settings**: Default configuration values

---

## 🌍 **Location Data Management**

### **Comprehensive Location Seeding Features**
The new comprehensive location seeding system provides complete US coverage with advanced features:

#### **Coverage**
- **States**: All 50 US states + District of Columbia
- **Counties**: ~3,100+ counties from all states
- **Territories**: Optional US territories (use `--include-territories` flag)

#### **Performance Features**
- **Batch Processing**: Processes data in chunks of 500 records for optimal performance
- **Progress Tracking**: Real-time progress bars and status updates
- **Transaction Safety**: Database transactions ensure data consistency
- **Error Handling**: Comprehensive error handling with detailed messages

#### **Usage Examples**
```bash
# Complete seeding (recommended for new installations)
npm run seed-complete-locations

# Seed only states (fast)
npm run seed-all-states

# Seed only counties (requires states to exist)
npm run seed-all-counties

# Clear existing data and reseed
npm run reseed-locations

# Include US territories
node scripts/seed-complete-locations.js --include-territories

# Verbose mode for debugging
node scripts/seed-complete-locations.js --verbose

# Get help
node scripts/seed-complete-locations.js --help
```

#### **Data Sources**
- **States**: GitHub repository with official state names and abbreviations
- **Counties**: Comprehensive county database with state relationships
- **Validation**: Automatic data validation and cleanup
- **FIPS Codes**: Support for FIPS codes where available

#### **Complete Location Coverage**
The `npm run seed-locations` command now uses the comprehensive seeding script:

1. **Backup existing data** (optional)
2. **Run comprehensive seeding**: `npm run seed-complete-locations`
3. **Verify data**: The script includes built-in validation
4. **Update applications**: No code changes needed - same database schema

The new system is backward compatible and will detect existing data to avoid duplicates.

---

## 🔄 **Migration Scripts**

### **Database Migrations**
The system includes database migrations for schema updates:
- **Property fields migration**: Adds Estated API fields to `incident_details` table
- **Migration files**: Located in `firealerts-node/migrations/`
- **Migration execution**: Handled automatically by the application

### **Manual Database Fixes**
If you need to manually fix specific database issues:

- **Fix database columns**: `npm run fix-columns`
- **Test database connection**: `npm run wait-for-db`
- **Migrate subscribers to users**: `npm run migrate-subscribers`

### **Docker Integration**
The Docker initialization has been updated for production:

#### **Bootstrap Data (docker/mysql-init/02-seed-data.sql):**
- Only creates admin user
- Only essential system settings
- API key placeholders (empty)
- No location data (handled by JavaScript)
- No demo content

#### **Comprehensive Seeding (docker-entrypoint.sh):**
- Automatically runs `npm run seed` on container startup
- Falls back to core seeding if comprehensive seeding fails
- Uses complete location seeding for full US coverage

---

## 🔧 **Troubleshooting**

### **Common Issues and Solutions**

#### **Database Connection Issues**
```bash
npm run wait-for-db  # Test database connectivity
```

**Symptoms**: Connection refused, timeout errors
**Solutions**:
1. Verify `.env` file has correct database credentials
2. Ensure `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME` are set
3. Check database server is running
4. Verify firewall/network connectivity

#### **Seeding Failures**
**Symptoms**: Scripts fail with errors, incomplete data
**Solutions**:
1. **Run Individual Scripts**:
   ```bash
   npm run seed-core        # Try core data only
   npm run seed-locations   # Then add locations
   npm run seed-company-types # Then add company types
   ```

2. **Check Environment Variables**:
   - Verify database connection settings
   - Ensure NODE_ENV is set appropriately
   - Check for missing required variables

3. **Database Column Issues**:
   ```bash
   npm run fix-columns      # Fix any column mismatches
   ```

#### **Data Integrity Issues**
**Symptoms**: Missing dropdowns, broken relationships
**Solutions**:
1. **Verify Data Integrity**:
   ```bash
   npm run validate-production-seeding
   ```

2. **Reseed Specific Components**:
   ```bash
   npm run reseed-locations     # Refresh location data
   npm run seed-company-types   # Add missing company types
   ```

3. **Check Logs**: Look for specific error messages in the console output

### **Seeding Process Flow**
1. **Environment Validation**
   - Check NODE_ENV
   - Validate database host
   - Detect Docker environment
   - Warn about development mode

2. **Database Connection**
   - Test connectivity
   - Start transaction for rollback capability

3. **Demo Data Check**
   - Scan for existing test incidents
   - Check for demo companies
   - Report any found demo content

4. **Core Data Seeding**
   - Admin user account
   - Incident types and statuses
   - Essential system settings

5. **Location Data Seeding**
   - All US states with proper naming
   - Complete county data with consistent suffixes
   - Batch processing for performance

6. **Reference Data Seeding**
   - Company types for organization classification
   - Subscription plan validation (disabled)

7. **Validation & Commit**
   - Verify data integrity
   - Commit transaction
   - Provide success summary

### **Testing Production Seeding**

#### **Dry Run Testing:**
```bash
npm run seed-production -- --dry-run
```

#### **Validation Checks:**
- No demo incidents created
- No test companies created
- Only admin user exists
- All states and counties present
- Proper naming conventions applied

#### **Post-Seeding Verification:**
1. Login with admin credentials
2. Check dropdown menus are populated
3. Verify no demo data in incident list
4. Confirm company management is empty
5. Review system settings

### **Migration from Previous Versions**
If upgrading from a version with demo data:

1. **Backup existing database**
2. **Clean demo data:** `node cleanup-test-data.js`
3. **Run production seeding:** `npm run seed-production`
4. **Verify clean installation**

---

## 🔐 **Default Admin Credentials**

After seeding, you can log in with the default admin account:
- **Username**: `admin`
- **Password**: `FireAdmin2025!`

**🚨 Important Security Note**: Change the default admin password immediately after first login!

---

## 🎯 **Best Practices**

1. **Always use `npm run seed` for new installations**
2. **Test seeding in development before production**
3. **Backup your database before running seeds on existing data**
4. **Use individual scripts only when you need specific data sets**
5. **Monitor the console output for any warnings or errors**
6. **Use production seeding for all production deployments**
7. **Validate seeding results with the validation scripts**

---

## 📊 **Production Readiness Checklist**

- [ ] Admin password changed from default
- [ ] API keys configured in admin panel
- [ ] No demo incidents in system
- [ ] No test companies created
- [ ] Location dropdowns populated
- [ ] System settings reviewed
- [ ] Email notifications configured
- [ ] SMS notifications configured (if needed)
- [ ] Map integration working
- [ ] User roles and permissions verified

---

**This configuration ensures FireAlerts911 is production-ready with only essential system data and no demo content. Need help? Check the application logs or run individual seed scripts to isolate any issues.**
