module.exports = (sequelize, DataTypes) => {
  const UserPreference = sequelize.define('userPreference', {
    notifyByEmail: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'notify_by_email'
    },
    notifyBySms: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'notify_by_sms'
    },
    notifyByPush: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'notify_by_push'
    },
    fireIncidentAlert: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'fire_incident_alert'
    },
    waterIncidentAlert: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'water_incident_alert'
    },
    otherIncidentAlert: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'other_incident_alert'
    },
    dailyDigest: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'daily_digest'
    },
    alertRadius: {
      type: DataTypes.INTEGER,
      defaultValue: 10,
      field: 'alert_radius'
    },
    quietHoursStart: {
      type: DataTypes.TIME,
      field: 'quiet_hours_start'
    },
    quietHoursEnd: {
      type: DataTypes.TIME,
      field: 'quiet_hours_end'
    }
  }, {
    tableName: 'user_preferences',
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id']
      }
    ]
  });

  return UserPreference;
};
