/**
 * Complete Database Seed Script - MINIMAL VERSION (Admin User + Required Types Only)
 *
 * This script replaces the SQL seeding with a more reliable JavaScript approach
 * for seeding all data in the FireAlerts911 application.
 *
 * This version has been modified to only seed:
 * 1. Admin user
 * 2. Incident types
 * 3. Statuses
 * 4. Essential system settings
 */

const { Sequelize, DataTypes, Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// Note: Table creation is handled by the master seed-all.js script

// Load environment variables
require('dotenv').config();

// Configuration - Use Railway database settings
const config = {
  dialect: 'mysql',
  host: process.env.DB_HOST || 'crossover.proxy.rlwy.net',
  port: parseInt(process.env.DB_PORT) || 54883,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'MDtfvbfCgmyaIIvFvShfqXWwCbKEIlgH',
  database: process.env.DB_NAME || 'railway',
  logging: false, // Set to console.log for verbose output
  dialectOptions: {
    ssl: false // Railway doesn't require SSL
  },
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
};

// Check if we're in Docker environment
function isInDocker() {
  try {
    return fs.existsSync('/.dockerenv');
  } catch (err) {
    return false;
  }
}

// Helper function to hash passwords
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

// Main seed function
async function seedDatabase() {
  let sequelize;
  try {
    console.log(`🔌 Using environment: ${isInDocker() ? 'Docker' : 'Local'}`);
    console.log(`🔌 Connecting to database ${config.database} at ${config.host}...`);

    sequelize = new Sequelize(config);
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Define all models
    console.log('📋 Defining models...');

    // States - FIXED: Match models/state.js exactly
    const State = sequelize.define('state', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: { type: DataTypes.STRING(100), allowNull: false },
      abbreviation: { type: DataTypes.STRING(2), allowNull: false },
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true },
      created_at: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW },
      updated_at: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW }
    }, { tableName: 'states', timestamps: true, underscored: true });

    // Counties - FIXED: Match models/county.js exactly
    const County = sequelize.define('county', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: { type: DataTypes.STRING, allowNull: false },
      state: { type: DataTypes.STRING, allowNull: false },
      state_id: { type: DataTypes.INTEGER, allowNull: true },
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true },
      created_at: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW },
      updated_at: { type: DataTypes.DATE, allowNull: false, defaultValue: DataTypes.NOW }
    }, { tableName: 'counties', timestamps: true, underscored: true });

    // Zip Codes
    const ZipCode = sequelize.define('zipcode', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      zip: DataTypes.STRING,
      city: DataTypes.STRING,
      county_id: DataTypes.INTEGER,
      state_id: DataTypes.INTEGER,
      latitude: DataTypes.FLOAT,
      longitude: DataTypes.FLOAT,
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true }
    }, { tableName: 'zip_codes', timestamps: false });

    // Companies - FIXED: Match models/company.js exactly
    const Company = sequelize.define('company', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: { type: DataTypes.STRING(255), allowNull: false },
      address: DataTypes.STRING(255),
      city: DataTypes.STRING(100),
      state: DataTypes.STRING(2),
      zip: DataTypes.STRING(10),
      phone: DataTypes.STRING(20),
      email: DataTypes.STRING(255),
      contactPerson: { type: DataTypes.STRING(100), field: 'contact_person' },
      status: { type: DataTypes.BOOLEAN, defaultValue: true },
      subscriptionType: { type: DataTypes.STRING(50), field: 'subscription_type' },
      subscriptionExpiry: { type: DataTypes.DATE, field: 'subscription_expiry' },
      companyTypeId: { type: DataTypes.INTEGER, allowNull: true, field: 'company_type_id' }
    }, { tableName: 'companies', underscored: true });

    // Users - FIXED: Match models/user.js exactly
    const User = sequelize.define('user', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      firstName: { type: DataTypes.STRING(100), allowNull: false, field: 'first_name' },
      lastName: { type: DataTypes.STRING(100), allowNull: false, field: 'last_name' },
      email: { type: DataTypes.STRING(255), allowNull: false, unique: true },
      username: { type: DataTypes.STRING(50), allowNull: false, unique: true },
      password: { type: DataTypes.STRING(255), allowNull: false },
      role: { type: DataTypes.ENUM('admin', 'company_admin', 'dispatcher', 'subscriber'), defaultValue: 'subscriber' },
      phone: { type: DataTypes.STRING(20), allowNull: true },
      cellPhone: { type: DataTypes.STRING(20), allowNull: true, field: 'cell_phone' },
      cellProvider: { type: DataTypes.STRING(50), allowNull: true, field: 'cell_provider' },
      address: { type: DataTypes.STRING(255), allowNull: true },
      city: { type: DataTypes.STRING(100), allowNull: true },
      state: { type: DataTypes.STRING(2), allowNull: true },
      zip: { type: DataTypes.STRING(10), allowNull: true },
      organization: { type: DataTypes.STRING(255), allowNull: true },
      companyId: { type: DataTypes.INTEGER, allowNull: true, field: 'company_id' },
      lastLogin: { type: DataTypes.DATE, allowNull: true, field: 'last_login' },
      passwordResetToken: { type: DataTypes.STRING(255), allowNull: true, field: 'password_reset_token' },
      passwordResetExpires: { type: DataTypes.DATE, allowNull: true, field: 'password_reset_expires' },
      status: { type: DataTypes.BOOLEAN, defaultValue: true }
    }, {
      tableName: 'users',
      underscored: true
    });

    // User Preferences
    const UserPreference = sequelize.define('user_preference', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      user_id: DataTypes.INTEGER,
      notify_by_email: { type: DataTypes.BOOLEAN, defaultValue: true },
      notify_by_sms: { type: DataTypes.BOOLEAN, defaultValue: false },
      notify_by_push: { type: DataTypes.BOOLEAN, defaultValue: false },
      fire_incident_alert: { type: DataTypes.BOOLEAN, defaultValue: true },
      water_incident_alert: { type: DataTypes.BOOLEAN, defaultValue: true },
      other_incident_alert: { type: DataTypes.BOOLEAN, defaultValue: true },
      alert_radius: { type: DataTypes.INTEGER, defaultValue: 10 }
    }, { tableName: 'user_preferences', timestamps: false });

    // User Locations
    const UserLocation = sequelize.define('user_location', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      user_id: DataTypes.INTEGER,
      address: DataTypes.STRING,
      city: DataTypes.STRING,
      county: DataTypes.STRING,
      state: DataTypes.STRING,
      zip: DataTypes.STRING,
      latitude: DataTypes.FLOAT,
      longitude: DataTypes.FLOAT,
      location_name: DataTypes.STRING,
      is_primary: { type: DataTypes.BOOLEAN, defaultValue: false }
    }, { tableName: 'user_locations', timestamps: false });

    // User Subscriptions
    const UserSubscription = sequelize.define('user_subscription', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      user_id: DataTypes.INTEGER,
      county_id: DataTypes.INTEGER
    }, { tableName: 'user_subscriptions', timestamps: false });

    // Incident Types
    const IncidentType = sequelize.define('incident_type', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: DataTypes.STRING,
      category: DataTypes.STRING,
      icon: DataTypes.STRING,
      description: DataTypes.STRING
    }, { tableName: 'incident_types', underscored: true });

    // Statuses
    const Status = sequelize.define('status', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      name: DataTypes.STRING,
      color: DataTypes.STRING,
      description: DataTypes.TEXT
    }, { tableName: 'statuses', underscored: true });

    // Incidents
    const Incident = sequelize.define('incident', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      title: DataTypes.STRING,
      description: DataTypes.TEXT,
      address: DataTypes.STRING,
      city: DataTypes.STRING,
      county: DataTypes.STRING,
      state: DataTypes.STRING,
      zip: DataTypes.STRING,
      latitude: DataTypes.FLOAT,
      longitude: DataTypes.FLOAT,
      incident_date: DataTypes.DATE,
      cross_street: DataTypes.STRING,
      alarm_level: DataTypes.INTEGER,
      severity: DataTypes.STRING,
      user_id: DataTypes.INTEGER,
      incident_type_id: DataTypes.INTEGER,
      status_id: DataTypes.INTEGER,
      created_at: DataTypes.DATE,
      updated_at: DataTypes.DATE
    }, {
      tableName: 'incidents',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    });

    // Incident Details
    const IncidentDetail = sequelize.define('incident_detail', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      incident_id: DataTypes.INTEGER,
      structure_type: DataTypes.STRING,
      smoke_type: DataTypes.STRING,
      fire_type: DataTypes.STRING,
      water_type: DataTypes.STRING,
      water_level: DataTypes.STRING,
      damage_extent: DataTypes.STRING,
      area_affected: DataTypes.STRING,
      people_affected: DataTypes.INTEGER,
      evacuation_status: DataTypes.STRING,
      estimated_damage: DataTypes.DECIMAL,
      responder_count: DataTypes.INTEGER,
      property_owner: DataTypes.STRING,
      notes: DataTypes.TEXT
    }, { tableName: 'incident_details', timestamps: false });

    // Media
    const Media = sequelize.define('media', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      incident_id: DataTypes.INTEGER,
      type: DataTypes.STRING,
      file_name: DataTypes.STRING,
      file_size: DataTypes.INTEGER,
      file_type: DataTypes.STRING,
      url: DataTypes.STRING,
      thumbnail_url: DataTypes.STRING,
      description: DataTypes.TEXT,
      is_public: { type: DataTypes.BOOLEAN, defaultValue: true },
      uploaded_by: DataTypes.INTEGER,
      created_at: DataTypes.DATE,
      updated_at: DataTypes.DATE
    }, {
      tableName: 'media',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    });

    // Notifications
    const Notification = sequelize.define('notification', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      incident_id: DataTypes.INTEGER,
      user_id: DataTypes.INTEGER,
      type: DataTypes.STRING,
      status: DataTypes.STRING,
      content: DataTypes.TEXT,
      sent_at: DataTypes.DATE
    }, { tableName: 'notifications', timestamps: false });

    // Activities (audit trail)
    const Activity = sequelize.define('activity', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      user_id: DataTypes.INTEGER,
      action: DataTypes.STRING,
      entity_type: DataTypes.STRING,
      entity_id: DataTypes.INTEGER,
      details: DataTypes.TEXT,
      ip_address: DataTypes.STRING,
      module: DataTypes.STRING,
      severity: DataTypes.STRING,
      created_at: DataTypes.DATE
    }, {
      tableName: 'activities',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: false
    });

    // System Settings with enhanced security features
    const SystemSetting = sequelize.define('systemSetting', {
      key: { type: DataTypes.STRING, primaryKey: true, allowNull: false },
      value: DataTypes.TEXT,
      category: { type: DataTypes.STRING, allowNull: false, defaultValue: 'general' },
      description: DataTypes.TEXT,
      isSecret: { type: DataTypes.BOOLEAN, defaultValue: false, field: 'is_secret' },
      expiresAt: { type: DataTypes.DATE, allowNull: true, field: 'expires_at' },
      lastAccessedAt: { type: DataTypes.DATE, allowNull: true, field: 'last_accessed_at' },
      accessCount: { type: DataTypes.INTEGER, defaultValue: 0, field: 'access_count' }
    }, { tableName: 'system_settings', underscored: true });

    // API Keys
    const ApiKey = sequelize.define('api_key', {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      user_id: DataTypes.INTEGER,
      api_key: DataTypes.STRING,
      name: DataTypes.STRING,
      permissions: DataTypes.TEXT,
      is_active: { type: DataTypes.BOOLEAN, defaultValue: true },
      created_at: DataTypes.DATE,
      updated_at: DataTypes.DATE
    }, {
      tableName: 'api_keys',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    });

    // Create tables first
    console.log('🏗️ Creating database tables...');
    await sequelize.sync({ force: false }); // Create tables if they don't exist
    console.log('✅ Database tables created/verified successfully.');

    // Start seeding data
    console.log('🌱 Starting data seeding...');

    // 1. LOCATION DATA SEEDING
    // Note: Location data is handled by a separate script
    const stateCount = await State.count();
    if (stateCount === 0) {
      console.log('⚠️ No states found in the database.');
      console.log('ℹ️ Run "npm run seed-locations" to populate location data (states, counties, zip codes).');
    } else {
      console.log(`✅ Location data already exists: ${stateCount} states found.`);
    }

    // 2. SEED INCIDENT TYPES
    console.log('🌱 Seeding incident types...');
    const incidentTypes = [
      {id: 1, name: 'Residential Structure Fire', category: 'fire', icon: 'fa-home', description: 'Fire in a residential building'},
      {id: 2, name: 'Commercial Structure Fire', category: 'fire', icon: 'fa-building', description: 'Fire in a commercial building'},
      {id: 3, name: 'Vehicle Fire', category: 'fire', icon: 'fa-car', description: 'Vehicle fire'},
      {id: 4, name: 'Wildland Fire', category: 'fire', icon: 'fa-tree', description: 'Wildland or vegetation fire'},
      {id: 5, name: 'Flood', category: 'water', icon: 'fa-water', description: 'Flooding incident'},
      {id: 6, name: 'Water Rescue', category: 'water', icon: 'fa-life-ring', description: 'Water rescue operation'},
      {id: 7, name: 'Water Main Break', category: 'water', icon: 'fa-wrench', description: 'Water main break or pipe burst'}
    ];

    const incidentTypeCount = await IncidentType.count();
    if (incidentTypeCount === 0) {
      await IncidentType.bulkCreate(incidentTypes);
      console.log(`✅ Added ${incidentTypes.length} incident types`);
    } else {
      console.log(`✅ Incident types already exist (${incidentTypeCount} records)`);
    }

    // 3. SEED STATUSES
    console.log('🌱 Seeding statuses...');
    const statuses = [
      {id: 1, name: 'Active', color: '#2196f3', description: 'Incident is currently active'},
      {id: 2, name: 'Resolved', color: '#4caf50', description: 'Incident has been resolved'},
      {id: 3, name: 'Pending', color: '#ff9800', description: 'Incident is pending investigation'},
      {id: 4, name: 'Critical', color: '#e53935', description: 'Critical incident requires immediate attention'},
      {id: 5, name: 'False Alarm', color: '#9e9e9e', description: 'False alarm reported'}
    ];

    const statusCount = await Status.count();
    if (statusCount === 0) {
      await Status.bulkCreate(statuses);
      console.log(`✅ Added ${statuses.length} statuses`);
    } else {
      console.log(`✅ Statuses already exist (${statusCount} records)`);
    }

    // 4. SEED ADMIN USER ONLY
    console.log('🌱 Seeding admin user...');
    // Create password hash
    const adminPasswordHash = await hashPassword('FireAdmin2025!');

    const users = [
      {
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        username: 'admin',
        password: adminPasswordHash,
        role: 'admin',
        phone: '************',
        city: 'Los Angeles',
        state: 'CA',
        status: 1
      }
    ];

    const userCount = await User.count();
    if (userCount === 0) {
      await User.bulkCreate(users);
      console.log(`✅ Added admin user`);
    } else {
      // Update admin user password if it exists
      const adminUser = await User.findOne({ where: { username: 'admin' } });
      if (adminUser) {
        adminUser.password = adminPasswordHash;
        await adminUser.save();
        console.log(`✅ Updated admin user password`);
      } else {
        // Create admin if it doesn't exist but other users do
        await User.create(users[0]);
        console.log(`✅ Added admin user`);
      }
      console.log(`✅ Users already exist (${userCount} records)`);
    }

    // 5. SEED SYSTEM SETTINGS
    console.log('🌱 Seeding system settings...');
    const systemSettings = [
      {key: 'site_name', category: 'general', value: 'FireAlerts911', description: 'Name of the website'},
      {key: 'contact_email', category: 'general', value: '<EMAIL>', description: 'Support email address'},
      {key: 'sms_provider', category: 'notifications', value: 'twilio', description: 'SMS gateway provider'},
      {key: 'email_from', category: 'notifications', value: '<EMAIL>', description: 'From email for notifications'},
      {key: 'max_daily_notifications', category: 'notifications', value: '100', description: 'Maximum daily notifications per user'},
      {key: 'default_latitude', category: 'map', value: '39.8283', description: 'Default map center latitude'},
      {key: 'default_longitude', category: 'map', value: '-98.5795', description: 'Default map center longitude'},
      {key: 'default_zoom', category: 'map', value: '4', description: 'Default map zoom level'}
    ];

    const systemSettingCount = await SystemSetting.count();
    if (systemSettingCount === 0) {
      await SystemSetting.bulkCreate(systemSettings);
      console.log(`✅ Added ${systemSettings.length} system settings`);
    } else {
      console.log(`✅ System settings already exist (${systemSettingCount} records)`);
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('✅ You can now log in with:');
    console.log('   - Admin: username=admin, password=FireAdmin2025!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    if (sequelize) {
      await sequelize.close();
      console.log('🔌 Database connection closed');
    }
  }
}

// Export the seed function for use in other scripts
module.exports = seedDatabase;

// Run the seed function if executed directly
if (require.main === module) {
  seedDatabase().catch(err => {
    console.error('❌ Unhandled error:', err);
    process.exit(1);
  });
}