/**
 * Production Database Seeding Script with Enhanced Validation
 *
 * This script is specifically designed for production deployments.
 * It includes additional safety checks and validation to ensure only
 * appropriate data is seeded in production environments.
 *
 * Features:
 * - Environment validation
 * - Data integrity checks
 * - No demo/test data creation
 * - Comprehensive logging
 * - Rollback capability on errors
 *
 * Usage:
 * npm run seed-production
 * node scripts/seed-production.js
 * node scripts/seed-production.js --force (skip environment checks)
 */

require('dotenv').config();
const { sequelize } = require('../models');
const fs = require('fs');

// Import table creation utility
const { ensureTablesExist, loadModels } = require('./utils/table-creation');

// Import individual seed functions
const seedMainData = require('./seed-data');
const seedCompleteLocationData = require('./seed-complete-locations');
const seedCompanyTypes = require('./seed-company-types');
// const seedSubscriptionPlans = require('./seed-subscription-plans'); // REMOVED: No default subscribers

// Command line arguments
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  verbose: args.includes('--verbose') || args.includes('-v'),
  dryRun: args.includes('--dry-run')
};

/**
 * Check if we're in a production-like environment
 */
function validateEnvironment() {
  const nodeEnv = process.env.NODE_ENV;
  const dbHost = process.env.DB_HOST;

  console.log('🔍 Environment validation:');
  console.log(`   NODE_ENV: ${nodeEnv || 'not set'}`);
  console.log(`   DB_HOST: ${dbHost || 'not set'}`);

  // Check for Docker environment
  const isDocker = fs.existsSync('/.dockerenv');
  console.log(`   Docker: ${isDocker ? 'yes' : 'no'}`);

  // Warn if this looks like a development environment
  if (!options.force) {
    if (nodeEnv === 'development' || dbHost === 'localhost') {
      console.log('⚠️  WARNING: This appears to be a development environment.');
      console.log('   Use --force flag if you want to proceed anyway.');
      return false;
    }
  }

  return true;
}

/**
 * Validate that no demo data exists
 */
async function validateNoExistingDemoData() {
  console.log('🔍 Checking for existing demo data...');

  try {
    // Check for test incidents
    const { incident } = require('../models');
    const testIncidents = await incident.count({
      where: {
        [sequelize.Op.or]: [
          { description: { [sequelize.Op.like]: '%test%' } },
          { address: { [sequelize.Op.like]: '%test%' } },
          { title: { [sequelize.Op.like]: '%test%' } }
        ]
      }
    });

    if (testIncidents > 0) {
      console.log(`⚠️  Found ${testIncidents} test incidents in database`);
      console.log('   Consider running cleanup before production seeding');
    }

    // Check for demo companies
    const { company } = require('../models');
    const demoCompanies = await company.count({
      where: {
        [sequelize.Op.or]: [
          { name: { [sequelize.Op.like]: '%test%' } },
          { name: { [sequelize.Op.like]: '%demo%' } },
          { name: { [sequelize.Op.like]: '%sample%' } }
        ]
      }
    });

    if (demoCompanies > 0) {
      console.log(`⚠️  Found ${demoCompanies} demo companies in database`);
    }

    return { testIncidents, demoCompanies };

  } catch (error) {
    console.log('ℹ️  Could not check for demo data (tables may not exist yet)');
    return { testIncidents: 0, demoCompanies: 0 };
  }
}

/**
 * Main production seeding function
 */
async function runProductionSeeding() {
  const startTime = Date.now();
  let transaction;

  try {
    console.log('🚀 PRODUCTION DATABASE SEEDING');
    console.log('================================');
    console.log('📋 This will create ONLY essential system data');
    console.log('🚫 NO demo data, test content, or sample records');
    console.log('');

    // Environment validation
    if (!validateEnvironment()) {
      console.log('❌ Environment validation failed. Use --force to override.');
      process.exit(1);
    }

    // Database connection
    console.log('🔌 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Ensure all required tables exist
    console.log('🔧 Ensuring database tables exist...');
    try {
      const models = loadModels(sequelize);
      const tableResults = await ensureTablesExist(sequelize, models, {
        verbose: options.verbose,
        force: false,
        alter: false
      });

      if (tableResults.created.length > 0) {
        console.log(`✅ Created ${tableResults.created.length} missing tables: ${tableResults.created.join(', ')}`);
      }
      if (tableResults.existed.length > 0) {
        console.log(`✅ Verified ${tableResults.existed.length} existing tables`);
      }
    } catch (tableError) {
      console.error('❌ Failed to create required database tables:', tableError.message);
      console.error('   This may indicate a database connection or permission issue.');
      throw tableError;
    }

    // Check for existing demo data
    const demoDataCheck = await validateNoExistingDemoData();

    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made');
      console.log('✅ Production seeding validation completed');
      return;
    }

    // Start transaction for rollback capability
    transaction = await sequelize.transaction();
    console.log('📝 Starting database transaction...');

    // Step 1: Core application data
    console.log('\n📊 STEP 1: Core application data');
    console.log('   ├─ Admin user account');
    console.log('   ├─ Incident types (fire, water, general)');
    console.log('   ├─ System statuses');
    console.log('   └─ Essential system settings');
    await seedMainData();

    // Step 2: Complete location data
    console.log('\n📊 STEP 2: Complete location data');
    console.log('   ├─ All 50 US states + DC');
    console.log('   └─ ~3,100+ counties with proper naming');
    await seedCompleteLocationData();

    // Step 3: Company types
    console.log('\n📊 STEP 3: Company types');
    console.log('   └─ Fire Department, EMS, Police, etc.');
    await seedCompanyTypes();

    // REMOVED STEP 4: No default subscription plans or demo companies
    console.log('\n🚫 STEP 4: Skipping subscription plans (no demo companies)');

    // Commit transaction
    await transaction.commit();
    console.log('✅ Database transaction committed');

    // Success summary
    const duration = Math.round((Date.now() - startTime) / 1000);
    console.log('\n🎉 PRODUCTION SEEDING COMPLETED SUCCESSFULLY!');
    console.log('============================================');
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log('✅ Application ready for production use');
    console.log('');
    console.log('🔐 Default admin credentials:');
    console.log('   Username: admin');
    console.log('   Password: FireAdmin2025!');
    console.log('');
    console.log('🔒 SECURITY REMINDER:');
    console.log('   - Change the default admin password immediately');
    console.log('   - Configure API keys in the admin panel');
    console.log('   - Review system settings');
    console.log('');
    console.log('📋 PRODUCTION READY:');
    console.log('   ✅ No demo incidents');
    console.log('   ✅ No demo companies or subscribers');
    console.log('   ✅ No sample users (admin only)');
    console.log('   ✅ Only essential system data');
    console.log('   ✅ Companies must be created through admin interface');

  } catch (error) {
    console.error('\n❌ PRODUCTION SEEDING FAILED');
    console.error('============================');
    console.error('Error:', error.message);

    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 Database transaction rolled back');
      } catch (rollbackError) {
        console.error('❌ Failed to rollback transaction:', rollbackError.message);
      }
    }

    process.exit(1);
  }
}

// Run if executed directly
if (require.main === module) {
  runProductionSeeding().catch(err => {
    console.error('❌ Unhandled error:', err);
    process.exit(1);
  });
}

module.exports = runProductionSeeding;
