/**
 * Production Security Validation Script
 * Validates that all required security configurations are properly set
 */

require('dotenv').config();

function validateProductionSecurity() {
  const errors = [];
  const warnings = [];

  console.log('🔒 Validating production security configuration...');

  // Check if we're in production mode
  const isProduction = process.env.NODE_ENV === 'production';
  
  if (isProduction) {
    console.log('✅ Running in production mode');
  } else {
    warnings.push('Not running in production mode (NODE_ENV !== "production")');
  }

  // Validate JWT Secret
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    errors.push('JWT_SECRET environment variable is required');
  } else if (jwtSecret.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long');
  } else if (jwtSecret === 'firealerts911_secret_key') {
    errors.push('JWT_SECRET is using default value - must be changed for production');
  } else {
    console.log('✅ JWT_SECRET is properly configured');
  }

  // Validate Database Configuration
  const requiredDbVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  const missingDbVars = requiredDbVars.filter(varName => !process.env[varName]);
  
  if (missingDbVars.length > 0) {
    errors.push(`Missing required database environment variables: ${missingDbVars.join(', ')}`);
  } else {
    console.log('✅ Database configuration is complete');
    
    // Check for default database credentials
    if (process.env.DB_USER === 'dispatchuser' && isProduction) {
      warnings.push('Using default database username in production');
    }
    if (process.env.DB_PASSWORD === 'dispatchpassword' && isProduction) {
      errors.push('Using default database password in production - SECURITY RISK');
    }
  }

  // Validate Email Configuration
  const hasMailgun = process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN;
  const hasSmtp = process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASSWORD;
  
  if (!hasMailgun && !hasSmtp) {
    warnings.push('No email provider configured (neither Mailgun nor SMTP)');
  } else {
    console.log('✅ Email configuration is available');
  }

  // Validate SMS Configuration (optional but recommended)
  const hasTwilio = process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN;
  if (!hasTwilio) {
    warnings.push('SMS notifications not configured (Twilio credentials missing)');
  } else {
    console.log('✅ SMS configuration is available');
  }

  // Check for development-only settings in production
  if (isProduction) {
    if (process.env.DB_LOGGING === 'true') {
      warnings.push('Database logging is enabled in production - may impact performance');
    }
  }

  // Report results
  console.log('\n📊 Security Validation Results:');
  
  if (errors.length > 0) {
    console.log('\n❌ CRITICAL ERRORS (must be fixed):');
    errors.forEach(error => console.log(`   • ${error}`));
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS (should be addressed):');
    warnings.forEach(warning => console.log(`   • ${warning}`));
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    console.log('✅ All security validations passed!');
  }
  
  console.log('\n🔒 Security validation complete.\n');
  
  // Exit with error code if critical errors found
  if (errors.length > 0) {
    console.error('❌ Critical security errors found. Application startup aborted.');
    process.exit(1);
  }
  
  return { errors, warnings };
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateProductionSecurity();
}

module.exports = validateProductionSecurity;
