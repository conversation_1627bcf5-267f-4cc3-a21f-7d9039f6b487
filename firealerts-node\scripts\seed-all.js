/**
 * Master seed script that runs all seeding operations in the correct order
 * This ensures all required data is properly loaded into the database
 */
require('dotenv').config();
const { sequelize } = require('../models');

// Import table creation utility
const { ensureTablesExist, loadModels } = require('./utils/table-creation');

// Import individual seed functions
const seedMainData = require('./seed-data');
const seedLocationData = require('./seed-complete-locations');
const seedCompanyTypes = require('./seed-company-types');
// const seedSubscriptionPlans = require('./seed-subscription-plans'); // REMOVED: No default subscribers

/**
 * Run all seed scripts in the proper sequence
 */
async function runAllSeeds() {
  try {
    console.log('🌱 Starting comprehensive database seeding process...');

    // Check database connection
    console.log('🔌 Checking database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Create all required tables from scratch (clean database)
    console.log('🔧 Creating database tables from scratch (clean database)...');
    try {
      const models = loadModels(sequelize);
      const tableResults = await ensureTablesExist(sequelize, models, {
        verbose: true,
        force: false,  // Don't drop existing tables (clean database)
        alter: true    // ALTER existing tables to match models (fix schema mismatches)
      });

      if (tableResults.created.length > 0) {
        console.log(`✅ Successfully created ${tableResults.created.length} new tables: ${tableResults.created.join(', ')}`);
      }
      if (tableResults.existed.length > 0) {
        console.log(`✅ Found ${tableResults.existed.length} existing tables (skipped creation)`);
      }

      console.log('✅ Database schema setup completed successfully');
    } catch (tableError) {
      console.error('❌ Failed to create required database tables:', tableError.message);
      console.error('   This may indicate a database connection or permission issue.');
      console.error('   Full error:', tableError);
      throw tableError;
    }

    // 1. First run the core seed data script (admin user, incident types, etc)
    console.log('\n📊 STEP 1: Seeding core application data...');
    await seedMainData();

    // 2. Seed location data (states and counties)
    console.log('\n📊 STEP 2: Seeding location data (states and counties)...');
    await seedLocationData();

    // 3. Seed company types (categories only, no actual companies)
    console.log('\n📊 STEP 3: Seeding company types...');
    await seedCompanyTypes();

    // REMOVED STEP 4: No default subscription plans or demo companies

    console.log('\n🎉 All seeding operations completed successfully!');
    console.log('✅ The FireAlerts911 application is now ready to use with all required data:');
    console.log('   📊 Database tables created from scratch');
    console.log('   👤 Admin user account created');
    console.log('   🚨 Incident types seeded');
    console.log('   🌍 Location data (states and counties) seeded');
    console.log('   🏢 Company types seeded (categories only)');
    console.log('   🚫 No default subscribers or demo companies created');
    console.log('');
    console.log('🔑 You can now log in with the default admin credentials:');
    console.log('   - Username: admin');
    console.log('   - Password: FireAdmin2025!');
    console.log('');
    console.log('🌐 Access the application at: http://localhost/');
    console.log('');
    console.log('🚀 The system is fully operational and ready for use!');

    // Don't exit when called as a module - let the calling process continue
    if (require.main === module) {
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Error during seeding process:', error);
    // Don't exit when called as a module - let the calling process handle the error
    if (require.main === module) {
      process.exit(1);
    } else {
      throw error; // Re-throw for the calling process to handle
    }
  }
}

// Run if executed directly
if (require.main === module) {
  runAllSeeds();
}

module.exports = runAllSeeds;
