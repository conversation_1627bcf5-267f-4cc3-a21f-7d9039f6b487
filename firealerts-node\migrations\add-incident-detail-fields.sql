-- Migration: Add new incident detail fields
-- Date: 2024-12-19
-- Description: Add smoke_severity, home_stories, and conditions_on_arrival fields to incident_details table

-- Add smoke_severity field
ALTER TABLE incident_details 
ADD COLUMN smoke_severity VARCHAR(50) COMMENT 'Smoke severity level (Light, Moderate, Heavy, Dense, No Visible Smoke)';

-- Add home_stories field  
ALTER TABLE incident_details 
ADD COLUMN home_stories INT COMMENT 'Number of stories/floors in the building';

-- Add conditions_on_arrival field
ALTER TABLE incident_details 
ADD COLUMN conditions_on_arrival TEXT COMMENT 'Description of scene conditions when first responders arrived';

-- Add indexes for performance (optional)
CREATE INDEX idx_smoke_severity ON incident_details(smoke_severity);
CREATE INDEX idx_home_stories ON incident_details(home_stories);
