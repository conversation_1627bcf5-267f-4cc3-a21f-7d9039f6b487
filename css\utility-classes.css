/* FireAlerts911 - CSS Utility Classes
 * Consolidated common patterns to reduce duplicate CSS across files
 * Created during Phase 5: CSS Consolidation
 */

/* ===== NOTIFICATION UTILITIES ===== */
.notification-base {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    animation: slideIn 0.3s ease-out forwards;
    transform: translateX(120%);
    transition: transform 0.3s ease-in-out;
}

.notification-base.show {
    transform: translateX(0);
}

.notification-base i {
    margin-right: 10px;
}

.notification-success {
    background-color: #4caf50;
}

.notification-error {
    background-color: #f44336;
}

.notification-info {
    background-color: #2196f3;
}

.notification-warning {
    background-color: #ff9800;
}

/* ===== STATUS BADGE UTILITIES ===== */
.status-badge-base {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
    text-transform: uppercase;
}

.status-active {
    background-color: var(--accent-green);
    color: white;
}

.status-pending {
    background-color: var(--accent-orange);
    color: white;
}

.status-critical {
    background-color: var(--accent-red);
    color: white;
    animation: pulse 1.5s infinite;
}

.status-sent {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--accent-green);
}

.status-delivered {
    background-color: rgba(76, 175, 80, 0.3);
    color: #2e7d32;
}

.status-failed {
    background-color: rgba(229, 57, 53, 0.2);
    color: var(--accent-red);
}

/* ===== BUTTON UTILITIES ===== */
.btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.btn-full-width {
    width: 100%;
}

/* ===== FORM UTILITIES ===== */
.form-error {
    border-color: #f44336 !important;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2) !important;
}

.form-success {
    border-color: #4caf50 !important;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
}

.form-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.error-message {
    color: #f44336;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* ===== LOADING UTILITIES ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.loading-message {
    padding: 10px;
    text-align: center;
    color: var(--text-secondary);
}

/* ===== ICON UTILITIES ===== */
.icon-fire {
    color: var(--accent-red);
}

.icon-water {
    color: var(--accent-blue);
}

.icon-success {
    color: var(--accent-green);
}

.icon-warning {
    color: var(--accent-orange);
}

.icon-info {
    color: var(--accent-blue);
}

.icon-error {
    color: var(--accent-red);
}

/* ===== SPACING UTILITIES ===== */
.m-0 { margin: 0; }
.m-1 { margin: 5px; }
.m-2 { margin: 10px; }
.m-3 { margin: 15px; }
.m-4 { margin: 20px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 5px; }
.mt-2 { margin-top: 10px; }
.mt-3 { margin-top: 15px; }
.mt-4 { margin-top: 20px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mb-4 { margin-bottom: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 5px; }
.p-2 { padding: 10px; }
.p-3 { padding: 15px; }
.p-4 { padding: 20px; }

/* ===== TEXT UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--accent-blue); }
.text-success { color: var(--accent-green); }
.text-danger { color: var(--accent-red); }
.text-warning { color: var(--accent-orange); }
.text-muted { color: var(--text-secondary); }
.text-light { color: var(--text-light); }

.font-weight-normal { font-weight: 400; }
.font-weight-bold { font-weight: 700; }
.font-weight-light { font-weight: 300; }

/* ===== DISPLAY UTILITIES ===== */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

/* ===== FLEX UTILITIES ===== */
.flex-30 { flex: 0 0 30%; }
.flex-40 { flex: 0 0 40%; }
.flex-50 { flex: 0 0 50%; }
.flex-60 { flex: 0 0 60%; }
.flex-70 { flex: 0 0 70%; }

/* Responsive flex utilities */
@media (max-width: 576px) {
    .flex-30, .flex-40, .flex-50, .flex-60, .flex-70 {
        flex: 1 1 100%;
    }
}

/* ===== ANIMATION UTILITIES ===== */
@keyframes slideIn {
    from {
        transform: translateX(120%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* ===== HOVER UTILITIES ===== */
.hover-bg:hover {
    background-color: var(--hover-bg);
}

.hover-opacity:hover {
    opacity: 0.8;
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* ===== ANTI-FLICKER TABLE UTILITIES ===== */
/* These styles prevent white flashing during table loading by ensuring consistent dark backgrounds */
.table-wrapper {
    background-color: var(--secondary-dark) !important;
}

.data-table {
    background-color: var(--secondary-dark) !important;
}

.data-table tbody {
    background-color: var(--secondary-dark) !important;
}

.data-table tbody tr {
    background-color: var(--secondary-dark) !important;
}

.data-table tbody td {
    background-color: transparent !important;
}

/* Preserve hover effects */
.data-table tbody tr:hover {
    background-color: var(--hover-bg) !important;
}

/* Ensure skeleton rows don't interfere with hover */
.data-table tbody tr.skeleton-row:hover {
    background-color: var(--secondary-dark) !important;
}

/* Skeleton loading styles for smooth loading transitions */
.skeleton-row {
    background-color: var(--secondary-dark) !important;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton-text {
    height: 16px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: 4px;
    width: 60%;
}

.skeleton-text-long {
    width: 80%;
}

@keyframes skeleton-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes skeleton-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Loading states with consistent dark backgrounds */
.loading-state-dark {
    background-color: var(--secondary-dark) !important;
    color: #f5f5f5 !important;
}

.loading-spinner-dark {
    color: #1e88e5 !important;
}

.error-state-dark {
    background-color: var(--secondary-dark) !important;
    color: var(--accent-red) !important;
}
