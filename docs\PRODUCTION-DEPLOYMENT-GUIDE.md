# FireAlerts911 Production Deployment Guide

## Overview

This guide covers the complete process for deploying FireAlerts911 to production on Render.com with a clean, production-ready codebase that excludes all development files and Docker configurations.

## Pre-Deployment Preparation

### 1. Codebase Cleanup Status

The codebase has been prepared for production deployment with the following exclusions in `.gitignore`:

#### Docker Files (Excluded from Production)
- `Dockerfile`
- `docker-compose.yml` 
- `.dockerignore`
- `docker/` directory and all contents

#### Development and Testing Files (Excluded)
- `tests/` directory
- All `test-*.js` and `test-*.html` files
- Development scripts in `firealerts-node/scripts/`
- `start.bat` (Windows development file)

#### Legacy Model Files (Excluded - No Longer Actively Used)
- `firealerts-node/models/location.js` (replaced by state/county system)
- `firealerts-node/models/logger.js` (replaced by activity system)
- `firealerts-node/models/os.js` (operating_systems - not used)

#### Documentation Archives (Excluded)
- `docs/archive/` directory

### 2. Production Database Seeding

The production seeding script (`npm run seed-production`) includes ONLY essential system data:

✅ **Included in Production:**
- Admin user account (username: admin, password: FireAdmin2025!)
- All 50 US states + DC with proper naming
- ~3,100+ counties with proper naming conventions
- Incident types (fire, water, general)
- System statuses and essential settings
- Company types for organization classification

❌ **Excluded from Production:**
- Demo incidents
- Test companies/subscribers  
- Mock user accounts beyond admin
- Sample/test data

### 3. Environment Configuration

The application is configured for Render.com deployment with:
- Node.js version specified: `>=18.0.0`
- Production-ready API URL handling
- Environment-based configuration
- SSL support for cloud databases

## Render.com Deployment Steps

### Step 1: Database Setup

Choose and set up a cloud database:

**Recommended Options:**
- **PlanetScale** (MySQL, serverless, free tier available)
- **Railway** (PostgreSQL/MySQL, simple setup)
- **AWS RDS** (Enterprise option)

**Database Requirements:**
- MySQL 5.7+ or 8.0+
- SSL support enabled
- Connection string/credentials ready

### Step 2: Create Render Web Service

1. **Connect Repository:**
   - Go to [Render.com](https://render.com)
   - Create new Web Service
   - Connect your GitHub repository

2. **Configure Service:**
   - **Name:** `firealerts911` (or your preferred name)
   - **Root Directory:** `firealerts-node`
   - **Environment:** `Node`
   - **Build Command:** `npm install`
   - **Start Command:** `npm start`

3. **Environment Variables:**
   Copy from `.env.render.template` and configure:

   ```bash
   # Database Configuration (Required)
   DB_HOST=your-database-host
   DB_USER=your-database-user  
   DB_PASSWORD=your-database-password
   DB_NAME=firealerts911
   DB_PORT=3306
   DB_SSL=true

   # Application Configuration (Required)
   NODE_ENV=production
   PORT=5000
   JWT_SECRET=your-generated-jwt-secret

   # API Keys (Configure after deployment)
   TWILIO_ACCOUNT_SID=
   TWILIO_AUTH_TOKEN=
   TWILIO_FROM_NUMBER=
   GOOGLE_MAPS_API_KEY=
   MAILGUN_API_KEY=
   MAILGUN_DOMAIN=
   ESTATED_API_KEY=

   # Optional Configuration
   DB_LOGGING=false
   DISABLE_NOTIFICATION_WORKER=false
   ENABLE_SECURITY_HEADERS=true
   ```

### Step 3: Deploy and Initialize

1. **Deploy Service:**
   - Click "Create Web Service"
   - Wait for initial deployment to complete

2. **Run Production Seeding:**
   - Access Render service shell or use deployment hook
   - Run: `npm run seed-production`
   - Verify seeding completed successfully

3. **Verify Deployment:**
   - Access your Render URL
   - Login with admin credentials: `admin` / `FireAdmin2025!`
   - Change default admin password immediately

## Post-Deployment Configuration

### 1. Security Setup

- [ ] Change default admin password
- [ ] Mark sensitive environment variables as secrets in Render
- [ ] Verify HTTPS is working
- [ ] Test security headers

### 2. API Keys Configuration

Configure the following API keys in the admin panel:

- **Twilio:** SMS notifications
- **Google Maps:** Geocoding and mapping
- **Mailgun:** Email notifications  
- **Estated:** Property information lookup

### 3. System Testing

- [ ] Create test incident
- [ ] Verify location dropdowns populate
- [ ] Test user management functions
- [ ] Verify notifications (if API keys configured)
- [ ] Check all pages load correctly
- [ ] Monitor performance and logs

## Database Hosting Options

### PlanetScale (Recommended)
- **Pros:** Serverless MySQL, free tier, excellent performance
- **Setup:** Create database, get connection string
- **SSL:** Enabled by default

### Railway
- **Pros:** Simple setup, good free tier
- **Setup:** Create MySQL service, get credentials
- **SSL:** Available

### AWS RDS
- **Pros:** Enterprise-grade, highly scalable
- **Setup:** Create RDS MySQL instance
- **SSL:** Configure SSL certificates

## Troubleshooting

### Common Issues

1. **Database Connection Errors:**
   - Verify DB_HOST, DB_USER, DB_PASSWORD
   - Ensure DB_SSL=true for cloud databases
   - Check database server is accessible

2. **Seeding Failures:**
   - Check database permissions
   - Verify all required environment variables
   - Review logs for specific errors

3. **Frontend API Errors:**
   - Verify API_BASE_URL configuration
   - Check CORS settings
   - Ensure authentication is working

### Monitoring

- Use Render's built-in logging
- Monitor database performance
- Set up uptime monitoring
- Review error logs regularly

## Maintenance

### Regular Tasks
- Monitor database usage and performance
- Review and rotate API keys periodically
- Update dependencies as needed
- Backup database regularly
- Monitor application logs for errors

### Updates
- Test changes in development first
- Use Render's deployment previews
- Monitor deployment for issues
- Have rollback plan ready

## Support

For deployment issues:
1. Check Render documentation
2. Review application logs
3. Verify environment configuration
4. Test database connectivity
5. Contact support if needed

---

**Generated:** Production deployment preparation completed
**Last Updated:** Ready for Render.com deployment
