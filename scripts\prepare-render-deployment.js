#!/usr/bin/env node

/**
 * Enhanced Render.com Deployment Preparation Script
 *
 * This script provides comprehensive preparation for FireAlerts911 deployment on Render.com
 * with business-level production standards including:
 * - Development file cleanup validation
 * - Legacy model exclusion verification
 * - Database seeding validation
 * - Error handling and rollback capabilities
 * - Comprehensive logging and progress reporting
 * - .gitignore effectiveness validation
 * - Production branch preparation
 *
 * Version: 2.0 - Production Ready
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { generateSecureSecret } = require('./generate-jwt-secret');

// Configuration
const CONFIG = {
  logFile: 'deployment-preparation.log',
  backupDir: '.deployment-backup',
  maxRetries: 3,
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v'),
  dryRun: process.argv.includes('--dry-run'),
  force: process.argv.includes('--force')
};

// Logging utilities
const logger = {
  info: (msg) => {
    const timestamp = new Date().toISOString();
    const logMsg = `[${timestamp}] INFO: ${msg}`;
    console.log(`ℹ️  ${msg}`);
    try {
      const logPath = resolvePath(CONFIG.logFile);
      fs.appendFileSync(logPath, logMsg + '\n');
    } catch (error) {
      // Fallback if path resolution fails
      fs.appendFileSync(CONFIG.logFile, logMsg + '\n');
    }
  },
  success: (msg) => {
    const timestamp = new Date().toISOString();
    const logMsg = `[${timestamp}] SUCCESS: ${msg}`;
    console.log(`✅ ${msg}`);
    try {
      const logPath = resolvePath(CONFIG.logFile);
      fs.appendFileSync(logPath, logMsg + '\n');
    } catch (error) {
      fs.appendFileSync(CONFIG.logFile, logMsg + '\n');
    }
  },
  warning: (msg) => {
    const timestamp = new Date().toISOString();
    const logMsg = `[${timestamp}] WARNING: ${msg}`;
    console.log(`⚠️  ${msg}`);
    try {
      const logPath = resolvePath(CONFIG.logFile);
      fs.appendFileSync(logPath, logMsg + '\n');
    } catch (error) {
      fs.appendFileSync(CONFIG.logFile, logMsg + '\n');
    }
  },
  error: (msg) => {
    const timestamp = new Date().toISOString();
    const logMsg = `[${timestamp}] ERROR: ${msg}`;
    console.log(`❌ ${msg}`);
    try {
      const logPath = resolvePath(CONFIG.logFile);
      fs.appendFileSync(logPath, logMsg + '\n');
    } catch (error) {
      fs.appendFileSync(CONFIG.logFile, logMsg + '\n');
    }
  }
};

console.log('🚀 FireAlerts911 Enhanced Render.com Deployment Preparation');
console.log('============================================================');
console.log(`📝 Logging to: ${CONFIG.logFile}`);
console.log(`🔧 Mode: ${CONFIG.dryRun ? 'DRY RUN' : 'LIVE'}`);
console.log('============================================================\n');

/**
 * Generate a secure JWT secret using the dedicated JWT secret generation script
 */
function generateJWTSecret() {
  try {
    logger.info('Generating JWT secret using dedicated script...');
    const jwtSecret = generateSecureSecret(64);
    logger.success(`JWT secret generated successfully (${jwtSecret.length} characters)`);
    logger.info(`Secret preview: ${jwtSecret.substring(0, 16)}...${jwtSecret.substring(-8)}`);
    return jwtSecret;
  } catch (error) {
    logger.error(`Failed to generate JWT secret: ${error.message}`);
    logger.warning('Falling back to basic crypto generation...');
    // Fallback to basic generation if the dedicated script fails
    const crypto = require('crypto');
    return crypto.randomBytes(64).toString('hex');
  }
}

/**
 * Create backup of critical files before making changes
 */
function createBackup() {
  logger.info('Creating backup of critical files...');

  const backupDir = resolvePath(CONFIG.backupDir);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const criticalFiles = [
    '.gitignore',
    'firealerts-node/package.json',
    'js/api.js'
  ];

  criticalFiles.forEach(file => {
    const fullPath = resolvePath(file);
    if (fs.existsSync(fullPath)) {
      const backupPath = path.join(backupDir, file.replace(/\//g, '_'));
      fs.copyFileSync(fullPath, backupPath);
      logger.success(`Backed up: ${file} -> ${backupPath}`);
    }
  });
}

/**
 * Validate Git repository status and .gitignore effectiveness
 */
function validateGitIgnore() {
  logger.info('Validating .gitignore effectiveness...');

  try {
    // Check if .env files are properly ignored
    const envFiles = ['.env', 'firealerts-node/.env'];
    envFiles.forEach(envFile => {
      if (fs.existsSync(envFile)) {
        try {
          execSync(`git check-ignore ${envFile}`, { stdio: 'pipe' });
          logger.success(`${envFile} is properly ignored by Git`);
        } catch (error) {
          logger.error(`${envFile} is NOT ignored by Git - security risk!`);
          return false;
        }
      }
    });

    // Check for tracked development files that should be ignored
    const devFiles = [
      'Dockerfile',
      'docker-compose.yml',
      'tests/',
      'start.bat'
    ];

    devFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const result = execSync(`git ls-files ${file}`, { stdio: 'pipe' }).toString().trim();
          if (result) {
            logger.warning(`${file} exists and is tracked by Git - should be in .gitignore`);
          } else {
            logger.success(`${file} is properly excluded from Git tracking`);
          }
        } catch (error) {
          logger.success(`${file} is not tracked by Git`);
        }
      }
    });

    return true;
  } catch (error) {
    logger.error(`Git validation failed: ${error.message}`);
    return false;
  }
}

/**
 * Get the correct base path for file operations
 */
function getBasePath() {
  // If we're running from firealerts-node directory, go up one level
  const currentDir = process.cwd();
  if (currentDir.endsWith('firealerts-node')) {
    return path.join(currentDir, '..');
  }
  return currentDir;
}

/**
 * Resolve file path relative to project root
 */
function resolvePath(filePath) {
  return path.join(getBasePath(), filePath);
}

/**
 * Check if required files exist with enhanced validation
 */
function checkRequiredFiles() {
  logger.info('Checking required production files...');

  const requiredFiles = [
    'firealerts-node/package.json',
    'firealerts-node/server.js',
    'firealerts-node/scripts/seed-production.js',
    'index.html',
    'js/api.js',
    '.gitignore'
  ];

  let allFilesExist = true;

  requiredFiles.forEach(file => {
    const fullPath = resolvePath(file);
    if (fs.existsSync(fullPath)) {
      logger.success(`Required file present: ${file}`);
    } else {
      logger.error(`Required file missing: ${file} (checked: ${fullPath})`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

/**
 * Validate that legacy model files are properly excluded
 */
function validateLegacyModelsExcluded() {
  logger.info('Validating legacy model files are excluded...');

  const legacyModels = [
    'firealerts-node/models/location.js',
    'firealerts-node/models/logger.js',
    'firealerts-node/models/os.js',
    'firealerts-node/models/operatingSystem.js',
    'firealerts-node/models/subscriber.js',
    'firealerts-node/models/subscriberPreference.js'
  ];

  let allExcluded = true;

  legacyModels.forEach(file => {
    const fullPath = resolvePath(file);
    if (fs.existsSync(fullPath)) {
      logger.error(`Legacy model file still exists: ${file} - should be removed`);
      allExcluded = false;
    } else {
      logger.success(`Legacy model properly excluded: ${file}`);
    }
  });

  return allExcluded;
}

/**
 * Validate development files are properly excluded
 */
function validateDevelopmentFilesExcluded() {
  logger.info('Validating development files are excluded...');

  const devFiles = [
    'Dockerfile',
    'docker-compose.yml',
    '.dockerignore',
    'docker/',
    'tests/',
    'start.bat',
    'firealerts-node/cleanup-test-data.js',
    'firealerts-node/data-migration.js',
    'firealerts-node/docker-entrypoint.sh',
    'docs/archive/'
  ];

  let allExcluded = true;

  devFiles.forEach(file => {
    const fullPath = resolvePath(file);
    if (fs.existsSync(fullPath)) {
      logger.warning(`Development file exists: ${file} - should be excluded by .gitignore`);
      // Check if it's in .gitignore
      const gitignorePath = resolvePath('.gitignore');
      if (fs.existsSync(gitignorePath)) {
        const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
        if (!gitignoreContent.includes(file)) {
          logger.error(`${file} is not in .gitignore!`);
          allExcluded = false;
        } else {
          logger.success(`${file} is properly excluded in .gitignore`);
        }
      } else {
        logger.error('.gitignore file not found!');
        allExcluded = false;
      }
    } else {
      logger.success(`Development file properly excluded: ${file}`);
    }
  });

  return allExcluded;
}

/**
 * Validate package.json for Render deployment with enhanced checks
 */
function validatePackageJson() {
  logger.info('Validating package.json for production deployment...');

  try {
    const packagePath = resolvePath(path.join('firealerts-node', 'package.json'));
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

    let isValid = true;

    // Check required scripts
    const requiredScripts = {
      'start': 'node server.js',
      'seed-production': 'node scripts/seed-production.js'
    };

    Object.entries(requiredScripts).forEach(([script, expectedCommand]) => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        if (packageJson.scripts[script].includes(expectedCommand.split(' ')[1])) {
          logger.success(`Script "${script}": ${packageJson.scripts[script]}`);
        } else {
          logger.warning(`Script "${script}" exists but may not be correct: ${packageJson.scripts[script]}`);
        }
      } else {
        logger.error(`Missing required script: ${script}`);
        isValid = false;
      }
    });

    // Check Node.js version
    if (packageJson.engines && packageJson.engines.node) {
      logger.success(`Node.js version specified: ${packageJson.engines.node}`);
    } else {
      logger.error('Node.js version not specified in engines - required for Render.com');
      isValid = false;
    }

    // Check essential dependencies
    const requiredDeps = [
      'express', 'sequelize', 'mysql2', 'bcryptjs', 'jsonwebtoken',
      'dotenv', 'cors', 'twilio', 'axios'
    ];

    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        logger.success(`Required dependency present: ${dep}`);
      } else {
        logger.error(`Missing required dependency: ${dep}`);
        isValid = false;
      }
    });

    // Validate main entry point
    if (packageJson.main === 'server.js') {
      logger.success(`Main entry point correct: ${packageJson.main}`);
    } else {
      logger.warning(`Main entry point may be incorrect: ${packageJson.main} (expected: server.js)`);
    }

    return isValid;
  } catch (error) {
    logger.error(`Error reading package.json: ${error.message}`);
    return false;
  }
}

/**
 * Validate database seeding configuration for production
 */
function validateDatabaseSeeding() {
  logger.info('Validating database seeding configuration...');

  try {
    // Check if seed-production.js exists and is properly configured
    const seedProductionPath = resolvePath('firealerts-node/scripts/seed-production.js');
    if (!fs.existsSync(seedProductionPath)) {
      logger.error('seed-production.js script not found');
      return false;
    }

    // Read and validate seed-production.js content
    const seedContent = fs.readFileSync(seedProductionPath, 'utf8');

    // Check for production safety features
    const requiredFeatures = [
      'NODE_ENV',
      'transaction',
      'rollback',
      'seedMainData',
      'seedCompleteLocationData'
    ];

    let hasAllFeatures = true;
    requiredFeatures.forEach(feature => {
      if (seedContent.includes(feature)) {
        logger.success(`Production seeding feature present: ${feature}`);
      } else {
        logger.warning(`Production seeding feature missing: ${feature}`);
        hasAllFeatures = false;
      }
    });

    // Check that demo/test data creation is disabled
    const testDataIndicators = [
      'demo',
      'test incident',
      'sample company',
      'mock user'
    ];

    testDataIndicators.forEach(indicator => {
      if (seedContent.toLowerCase().includes(indicator.toLowerCase())) {
        logger.warning(`Potential test/demo data creation found: ${indicator}`);
      }
    });

    // Validate seed-data.js (core seeding)
    const seedDataPath = resolvePath('firealerts-node/scripts/seed-data.js');
    if (fs.existsSync(seedDataPath)) {
      const coreContent = fs.readFileSync(seedDataPath, 'utf8');
      if (coreContent.includes('FireAdmin2025!')) {
        logger.success('Admin user with secure password configured');
      } else {
        logger.warning('Admin user password may not be properly configured');
      }
    }

    return hasAllFeatures;
  } catch (error) {
    logger.error(`Database seeding validation failed: ${error.message}`);
    return false;
  }
}

/**
 * Validate frontend API configuration for production
 */
function validateFrontendConfig() {
  logger.info('Validating frontend API configuration...');

  try {
    const apiJsPath = resolvePath(path.join('js', 'api.js'));
    const apiJs = fs.readFileSync(apiJsPath, 'utf8');

    // Check for localhost references
    if (apiJs.includes('localhost:5000')) {
      logger.warning('Frontend still configured for localhost');
      logger.info('Checking for environment-based configuration...');

      if (apiJs.includes('window.location.hostname') || apiJs.includes('process.env')) {
        logger.success('Environment-based API URL configuration detected');
        return true;
      } else {
        logger.error('Frontend needs environment-based API URL configuration');
        logger.info('Suggested fix for js/api.js:');
        console.log(`
const API_BASE_URL = window.location.hostname === 'localhost'
  ? 'http://localhost:5000/api'
  : '/api'; // Use relative URL for production
`);
        return false;
      }
    } else {
      logger.success('Frontend API configuration looks production-ready');
      return true;
    }
  } catch (error) {
    logger.error(`Error checking frontend config: ${error.message}`);
    return false;
  }
}

/**
 * Generate environment variables template with integrated JWT secret generation
 */
function generateEnvTemplate() {
  logger.info('Generating environment variables template...');

  // Generate JWT secret using the dedicated script
  const jwtSecret = generateJWTSecret();

  const envTemplate = `# FireAlerts911 Environment Variables for Render.com
# Copy these to your Render service environment variables

# Database Configuration (Required)
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=firealerts911
DB_PORT=3306

# Application Configuration (Required)
NODE_ENV=production
PORT=5000
JWT_SECRET=${jwtSecret}

# Database SSL (for cloud databases like PlanetScale)
DB_SSL=true

# API Keys (Configure after deployment)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_FROM_NUMBER=
GOOGLE_MAPS_API_KEY=
MAILGUN_API_KEY=
MAILGUN_DOMAIN=
ESTATED_API_KEY=

# Optional Configuration
DB_LOGGING=false
DISABLE_NOTIFICATION_WORKER=false

# Security Headers (Optional)
ENABLE_SECURITY_HEADERS=true
`;

  const envPath = resolvePath('.env.render.template');
  fs.writeFileSync(envPath, envTemplate);
  logger.success('Environment template created: .env.render.template');
  logger.info('Copy these variables to your Render service configuration');
}

/**
 * Generate render.yaml configuration file
 */
function generateRenderConfig() {
  logger.info('Generating render.yaml configuration...');

  const renderConfig = `# Render.com configuration for FireAlerts911
# This file is optional but provides advanced configuration options

services:
  # Backend API Service
  - type: web
    name: firealerts911-api
    env: node
    region: oregon # Choose region closest to your users
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5000
    # Auto-scaling configuration
    scaling:
      minInstances: 1
      maxInstances: 3
      targetCPUPercent: 70

  # Frontend Static Site (Optional - if deploying separately)
  - type: static
    name: firealerts911-frontend
    buildCommand: echo "No build needed for static files"
    publishDir: .
    headers:
      - path: /*
        headers:
          X-Frame-Options: DENY
          X-Content-Type-Options: nosniff
          X-XSS-Protection: "1; mode=block"
    routes:
      - type: rewrite
        source: /*
        destination: /index.html

# Database (if using Render PostgreSQL)
# databases:
#   - name: firealerts911-db
#     databaseName: firealerts911
#     user: firealerts_user
`;

  const renderPath = resolvePath('render.yaml');
  fs.writeFileSync(renderPath, renderConfig);
  logger.success('Render configuration created: render.yaml');
}



/**
 * Generate deployment checklist
 */
function generateDeploymentChecklist() {
  logger.info('Generating deployment checklist...');

  const checklist = `# FireAlerts911 Render.com Deployment Checklist

## Pre-deployment
- [ ] Database created (PlanetScale, Railway, AWS RDS, etc.)
- [ ] Database connection string obtained
- [ ] JWT secret generated
- [ ] Repository pushed to GitHub
- [ ] Environment variables template prepared

## Render Service Setup
- [ ] Web service created on Render
- [ ] GitHub repository connected
- [ ] Build command set: \`npm install\`
- [ ] Start command set: \`npm start\`
- [ ] Root directory set: \`firealerts-node\`
- [ ] All environment variables configured
- [ ] Service deployed successfully

## Post-deployment
- [ ] Application accessible via Render URL
- [ ] Database seeding completed: \`npm run seed-production\`
- [ ] Admin login working (admin / FireAdmin2025!)
- [ ] Admin password changed
- [ ] API keys configured in admin panel
- [ ] Core functionality tested
- [ ] Custom domain configured (optional)
- [ ] SSL certificate active
- [ ] Monitoring set up

## API Keys to Configure
- [ ] Twilio (SMS notifications)
- [ ] Google Maps (geocoding)
- [ ] Mailgun (email notifications)
- [ ] Estated (property information)

## Testing
- [ ] Create test incident
- [ ] Verify location dropdowns
- [ ] Test user management
- [ ] Verify notifications (if configured)
- [ ] Check all pages load correctly
- [ ] Monitor performance and logs

## Security
- [ ] Default admin password changed
- [ ] Environment variables marked as secrets
- [ ] Database access secured
- [ ] HTTPS working correctly
- [ ] Security headers configured

---
Generated by FireAlerts911 deployment preparation script
`;

  const checklistPath = resolvePath('RENDER-DEPLOYMENT-CHECKLIST.md');
  fs.writeFileSync(checklistPath, checklist);
  logger.success('Deployment checklist created: RENDER-DEPLOYMENT-CHECKLIST.md');
}

/**
 * Run comprehensive production readiness verification
 */
function runProductionReadinessChecks() {
  logger.info('Running comprehensive production readiness verification...');

  const checks = [
    { name: 'Required Files', fn: checkRequiredFiles },
    { name: 'Package.json Configuration', fn: validatePackageJson },
    { name: 'Git Ignore Effectiveness', fn: validateGitIgnore },
    { name: 'Legacy Models Excluded', fn: validateLegacyModelsExcluded },
    { name: 'Development Files Excluded', fn: validateDevelopmentFilesExcluded },
    { name: 'Database Seeding Configuration', fn: validateDatabaseSeeding },
    { name: 'Frontend API Configuration', fn: validateFrontendConfig }
  ];

  let allPassed = true;
  const results = {};

  checks.forEach(check => {
    logger.info(`\n🔍 Running check: ${check.name}`);
    try {
      const result = check.fn();
      results[check.name] = result;
      if (result) {
        logger.success(`✅ ${check.name} - PASSED`);
      } else {
        logger.error(`❌ ${check.name} - FAILED`);
        allPassed = false;
      }
    } catch (error) {
      logger.error(`❌ ${check.name} - ERROR: ${error.message}`);
      results[check.name] = false;
      allPassed = false;
    }
  });

  return { allPassed, results };
}

/**
 * Generate comprehensive deployment summary report
 */
function generateDeploymentSummary(checkResults) {
  logger.info('Generating deployment summary report...');

  const timestamp = new Date().toISOString();
  const summary = `# FireAlerts911 Deployment Preparation Summary

**Generated:** ${timestamp}
**Script Version:** 2.0 - Production Ready
**Mode:** ${CONFIG.dryRun ? 'DRY RUN' : 'LIVE'}

## Production Readiness Check Results

${Object.entries(checkResults.results).map(([check, passed]) =>
  `- [${passed ? 'x' : ' '}] ${check}`
).join('\n')}

## Overall Status
${checkResults.allPassed ? '✅ **READY FOR PRODUCTION DEPLOYMENT**' : '❌ **NOT READY - ISSUES MUST BE RESOLVED**'}

## Generated Files
- \`.env.render.template\` - Environment variables template
- \`render.yaml\` - Render.com service configuration
- \`RENDER-DEPLOYMENT-CHECKLIST.md\` - Manual deployment checklist
- \`deployment-preparation.log\` - Detailed execution log

## Next Steps
${checkResults.allPassed ? `
1. Review all generated configuration files
2. Set up external database (PlanetScale/Railway recommended)
3. Create Render.com web service
4. Configure environment variables from template
5. Deploy application
6. Run production database seeding: \`npm run seed-production\`
7. Change default admin password
8. Configure API keys in admin panel
` : `
1. **CRITICAL:** Fix all failed checks above
2. Re-run this script until all checks pass
3. Only proceed with deployment after all issues are resolved
`}

## Support Resources
- **Documentation:** docs/DEPLOYMENT-GUIDE.md
- **Render.com:** https://render.com
- **Database Options:** PlanetScale, Railway, AWS RDS
- **Support:** Check deployment logs and documentation

---
*This report was generated by the enhanced FireAlerts911 deployment preparation script*
`;

  const summaryPath = resolvePath('DEPLOYMENT-SUMMARY.md');
  fs.writeFileSync(summaryPath, summary);
  logger.success('Deployment summary saved to: DEPLOYMENT-SUMMARY.md');
}

/**
 * Enhanced main execution with comprehensive error handling
 */
async function main() {
  const startTime = Date.now();

  try {
    logger.info('Starting enhanced deployment preparation...');

    // Initialize log file
    const logPath = resolvePath(CONFIG.logFile);
    if (fs.existsSync(logPath)) {
      fs.unlinkSync(logPath);
    }

    // Create backup if not in dry-run mode
    if (!CONFIG.dryRun) {
      createBackup();
    }

    // Run comprehensive checks
    const checkResults = runProductionReadinessChecks();

    // Generate configuration files only if basic checks pass
    if (checkResults.results['Required Files'] && checkResults.results['Package.json Configuration']) {
      logger.info('\n🔧 Generating deployment configuration files...');
      generateEnvTemplate();
      generateRenderConfig();
      generateDeploymentChecklist();
    } else {
      logger.warning('Skipping configuration file generation due to basic validation failures');
    }

    // Generate comprehensive summary
    generateDeploymentSummary(checkResults);

    // Final results
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);

    console.log('\n' + '='.repeat(70));

    if (checkResults.allPassed) {
      logger.success('🎉 DEPLOYMENT PREPARATION COMPLETE!');
      logger.success('✅ All production readiness checks passed');
      logger.info(`⏱️  Completed in ${duration} seconds`);

      console.log('\n📋 Generated Files:');
      console.log('   - .env.render.template (Environment variables)');
      console.log('   - render.yaml (Render.com configuration)');
      console.log('   - RENDER-DEPLOYMENT-CHECKLIST.md (Manual checklist)');
      console.log('   - DEPLOYMENT-SUMMARY.md (Comprehensive report)');
      console.log('   - deployment-preparation.log (Detailed log)');

      console.log('\n🚀 Ready for Production Deployment!');
      console.log('📖 Next: Follow DEPLOYMENT-SUMMARY.md for deployment steps');

    } else {
      logger.error('❌ DEPLOYMENT PREPARATION FAILED');
      logger.error('⚠️  Critical issues must be resolved before deployment');
      logger.info(`⏱️  Completed in ${duration} seconds`);

      console.log('\n📋 Review these files for details:');
      console.log('   - DEPLOYMENT-SUMMARY.md (Issue summary)');
      console.log('   - deployment-preparation.log (Detailed log)');

      console.log('\n🔧 Fix all issues and re-run this script');
    }

    console.log('\n🔗 Resources:');
    console.log('   - Render.com: https://render.com');
    console.log('   - PlanetScale: https://planetscale.com');
    console.log('   - Railway: https://railway.app');
    console.log('   - Documentation: docs/DEPLOYMENT-GUIDE.md');

    // Exit with appropriate code
    process.exit(checkResults.allPassed ? 0 : 1);

  } catch (error) {
    logger.error(`Fatal error during deployment preparation: ${error.message}`);
    logger.error(`Stack trace: ${error.stack}`);

    console.log('\n💥 FATAL ERROR');
    console.log('❌ Deployment preparation failed with critical error');
    console.log(`📝 Check ${CONFIG.logFile} for detailed error information`);

    process.exit(1);
  }
}

// Run the script
main();
