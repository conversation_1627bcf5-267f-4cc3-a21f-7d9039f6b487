module.exports = (sequelize, DataTypes) => {
  const Activity = sequelize.define('activity', {
    userId: {
      type: DataTypes.INTEGER,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'SET NULL'
    },
    action: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    entityType: {
      type: DataTypes.STRING(50),
      field: 'entity_type'
    },
    entityId: {
      type: DataTypes.INTEGER,
      field: 'entity_id'
    },
    details: {
      type: DataTypes.TEXT
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      field: 'ip_address'
    },
    userAgent: {
      type: DataTypes.TEXT,
      field: 'user_agent'
    },
    module: {
      type: DataTypes.STRING(50)
    },
    severity: {
      type: DataTypes.ENUM('info', 'warning', 'error', 'critical'),
      defaultValue: 'info'
    }
  }, {
    tableName: 'activities',
    underscored: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['severity']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return Activity;
};
