// Import necessary modules and middleware
const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const db = require('../models');

// @route   GET /api/users
// @desc    Get all users (excluding passwords)
// @access  Private (Admin or company admin)
router.get('/', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ msg: 'Not authorized to view users' });
    }

    const { limit = 50, offset = 0, role, status, company_id } = req.query;

    // Build where clause
    const whereClause = {};

    if (role) {
      whereClause.role = role;
    }

    if (status) {
      whereClause.status = status === 'active';
    }

    if (company_id) {
      whereClause.company_id = company_id;
    }

    // If user is company_admin, only show users from their company (excluding dispatchers)
    if (req.user.role === 'company_admin') {
      whereClause.company_id = req.user.company_id;
      // Company admins cannot see dispatchers (they are FireAlerts911 employees)
      if (!whereClause.role) {
        whereClause.role = { [db.Sequelize.Op.ne]: 'dispatcher' };
      }
    }

    const users = await db.user.findAll({
      where: whereClause,
      attributes: { exclude: ['password'] },
      include: [
        {
          model: db.company,
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json(users);
  } catch (err) {
    console.error('Error fetching users:', err);
    res.status(500).json({ msg: 'Server error fetching users', error: err.message });
  }
});

// @route   GET /api/users/debug
// @desc    Debug endpoint to check users in database
// @access  Private
router.get('/debug', auth, async (req, res) => {
  try {
    const users = await db.user.findAll({
      attributes: ['id', 'username', 'role', 'status', 'email']
    });

    // Also check if user ID 1 exists specifically
    const userOne = await db.user.findByPk(1);

    res.json({
      totalUsers: users.length,
      users: users,
      userIdOne: userOne ? 'exists' : 'missing',
      requestingUser: req.user
    });
  } catch (err) {
    console.error('Error in debug endpoint:', err);
    res.status(500).json({ msg: 'Server error', error: err.message });
  }
});

// @route   GET /api/users/stats
// @desc    Get user statistics (counts by role, status, etc.)
// @access  Private (Admin only)
router.get('/stats', auth, async (req, res) => {
  try {
    // Only allow admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to view user statistics' });
    }

    // Get total user count
    const totalCount = await db.user.count();

    // Get active user count
    const activeCount = await db.user.count({
      where: { status: true }
    });

    // Get count by role
    const roleCounts = await db.user.findAll({
      attributes: [
        'role',
        [db.sequelize.fn('count', db.sequelize.col('role')), 'count']
      ],
      group: ['role']
    });

    // Get count by company
    let companyCounts = [];
    try {
      companyCounts = await db.user.findAll({
        attributes: [
          'company_id',
          [db.sequelize.fn('count', db.sequelize.col('user.id')), 'count']
        ],
        include: [{
          model: db.company,
          attributes: ['name'],
          required: false
        }],
        group: ['company_id', 'company.id'],
        raw: true,
        nest: true
      });
    } catch (companyErr) {
      console.error('Error getting company stats:', companyErr);
    }

    // Format role counts
    const byRole = roleCounts.reduce((acc, item) => {
      acc[item.role] = parseInt(item.dataValues.count);
      return acc;
    }, {});

    // Return the statistics
    res.json({
      totalCount,
      activeCount,
      inactiveCount: totalCount - activeCount,
      byRole,
      byCompany: companyCounts,
      timestamp: Date.now()
    });
  } catch (err) {
    console.error('Error fetching user stats:', err);
    res.status(500).json({ msg: 'Server error fetching user statistics', error: err.message });
  }
});

// @route   GET /api/users/:id
// @desc    Get user by ID (excluding password)
// @access  Private (Admin or company admin)
router.get('/:id', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin, or the user themselves
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'company_admin' &&
      req.user.id.toString() !== req.params.id.toString()
    ) {
      return res.status(403).json({ msg: 'Not authorized to view this user' });
    }
    const user = await db.user.findByPk(req.params.id, {
      attributes: { exclude: ['password'] }
    });
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    console.log('[DEBUG] Returning user:', user.toJSON());
    res.json(user);
  } catch (err) {
    console.error('Error fetching user:', err, err?.stack);
    res.status(500).json({ msg: 'Server error fetching user', error: err.message, stack: err.stack });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user by ID
// @access  Private (Admin or company admin)
router.put('/:id', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin, or the user themselves
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'company_admin' &&
      req.user.id.toString() !== req.params.id.toString()
    ) {
      return res.status(403).json({ msg: 'Not authorized to update this user' });
    }
    const user = await db.user.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    // Only update allowed fields
    const allowedFields = ['firstName', 'lastName', 'email', 'phone', 'role', 'status', 'username'];
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        if (field === 'status') {
          // Convert string status to boolean
          if (req.body.status === 'active' || req.body.status === true || req.body.status === 1) {
            user.status = true;
          } else if (req.body.status === 'inactive' || req.body.status === false || req.body.status === 0) {
            user.status = false;
          } else {
            user.status = req.body.status; // fallback, but should be boolean
          }
        } else {
          user[field] = req.body[field];
        }
      }
    }

    // Handle company_id based on role
    if (req.body.role === 'dispatcher') {
      user.company_id = null; // Dispatchers are FireAlerts911 employees, not company-specific
    } else if (req.body.company_id !== undefined) {
      user.company_id = req.body.company_id;
    }
    await user.save();    // Send welcome email if requested during edit
    if (req.body.sendWelcomeEmail) {
      console.log(`[DEBUG] Attempting to send welcome email to ${user.email} (edit)`);
      try {
        const emailService = require('../services/emailService');
        const { EMAIL_TYPES } = require('../services/emailConstants');

        // Generate a new temporary password for the user
        const password = Math.random().toString(36).slice(2) + Math.random().toString(36).toUpperCase().slice(2);
        const bcrypt = require('bcryptjs');
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);        user.password = hashedPassword;
        await user.save();

        // Send welcome email using the email service
        await emailService.sendWelcomeEmail({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email
        }, password);

        console.log(`[DEBUG] Welcome email sent to ${user.email} (edit)`);
      } catch (err) {
        console.error('Error sending welcome email (edit):', err);
      }
    }

    res.json({ success: true, msg: 'User updated successfully', user });
  } catch (err) {
    console.error('Error updating user:', err, err?.stack);
    res.status(500).json({ msg: 'Server error updating user', error: err.message, stack: err.stack });
  }
});

// @route   POST /api/users
// @desc    Create a new user and optionally send a welcome email
// @access  Private (Admin or company admin)
router.post('/', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ msg: 'Not authorized to create users' });
    }
    const { firstName, lastName, email, phone, role, status, company_id, subscriberId, sendWelcomeEmail, username } = req.body;
    const companyId = company_id || subscriberId; // Support both field names for backward compatibility

    // For dispatcher role, company_id is not required (they are FireAlerts911 employees)
    if (!firstName || !lastName || !email || !role) {
      return res.status(400).json({ msg: 'Missing required fields' });
    }

    // For non-dispatcher roles, company_id is required
    if (role !== 'dispatcher' && !companyId) {
      return res.status(400).json({ msg: 'Company ID is required for non-dispatcher users' });
    }
    // Check if user already exists
    const existing = await db.user.findOne({ where: { email } });
    if (existing) {
      return res.status(400).json({ msg: 'A user with this email already exists' });
    }
    // Generate a random password for the user
    const password = Math.random().toString(36).slice(2) + Math.random().toString(36).toUpperCase().slice(2);
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    // Create the user
    const user = await db.user.create({
      firstName,
      lastName,
      email,
      username: username || email.toLowerCase(), // Use provided username or default to email
      password: hashedPassword,
      phone,
      role,
      status: status === 'active' || status === true,
      company_id: role === 'dispatcher' ? null : companyId // Set company_id to null for dispatchers
    });    // Send welcome email if requested
    console.log('[DEBUG] sendWelcomeEmail value:', sendWelcomeEmail);
    if (sendWelcomeEmail) {
      console.log(`[DEBUG] Attempting to send welcome email to ${user.email}`);
      try {
        const emailService = require('../services/emailService');
        const { EMAIL_TYPES } = require('../services/emailConstants');
          // Send welcome email using the email service
        await emailService.sendWelcomeEmail({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email
        }, password);

        console.log(`[DEBUG] Welcome email sent to ${user.email}`);
      } catch (err) {
        console.error('Error sending welcome email:', err);
        // Don't fail user creation if email fails
      }
    }
    res.status(201).json({ success: true, msg: 'User created successfully', user: { id: user.id, email: user.email, firstName: user.firstName, lastName: user.lastName } });
  } catch (err) {
    console.error('Error creating user:', err, err?.stack);
    res.status(500).json({ msg: 'Server error creating user', error: err.message, stack: err.stack });
  }
});

// @route   POST /api/users/:id/reset-password
// @desc    Reset user password and send email
// @access  Private (Admin or company admin)
router.post('/:id/reset-password', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ msg: 'Not authorized to reset passwords' });
    }

    const user = await db.user.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Generate a new temporary password
    const newPassword = Math.random().toString(36).slice(2) + Math.random().toString(36).toUpperCase().slice(2);
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user password
    user.password = hashedPassword;
    await user.save();

    // Send password reset email
    try {
      const emailService = require('../services/emailService');
      await emailService.sendPasswordResetEmail({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email
      }, newPassword);

      console.log(`Password reset email sent to ${user.email}`);
    } catch (emailErr) {
      console.error('Error sending password reset email:', emailErr);
      // Don't fail the request if email fails, password was still reset
    }

    res.json({ success: true, msg: 'Password reset successfully and email sent' });
  } catch (err) {
    console.error('Error resetting password:', err);
    res.status(500).json({ msg: 'Server error resetting password', error: err.message });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user by ID
// @access  Private (Admin or company admin)
router.delete('/:id', auth, async (req, res) => {
  try {
    // Only allow admin or company_admin
    if (req.user.role !== 'admin' && req.user.role !== 'company_admin') {
      return res.status(403).json({ msg: 'Not authorized to delete users' });
    }

    const user = await db.user.findByPk(req.params.id);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Don't allow deleting yourself
    if (req.user.id.toString() === req.params.id.toString()) {
      return res.status(400).json({ msg: 'Cannot delete your own account' });
    }

    // Don't allow deleting the last admin
    if (user.role === 'admin') {
      const adminCount = await db.user.count({ where: { role: 'admin' } });
      if (adminCount <= 1) {
        return res.status(400).json({ msg: 'Cannot delete the last admin user' });
      }
    }

    await user.destroy();

    res.json({ success: true, msg: 'User deleted successfully' });
  } catch (err) {
    console.error('Error deleting user:', err);
    res.status(500).json({ msg: 'Server error deleting user', error: err.message });
  }
});

module.exports = router;