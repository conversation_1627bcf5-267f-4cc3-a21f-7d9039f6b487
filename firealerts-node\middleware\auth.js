const jwt = require('jsonwebtoken');
const db = require('../models');

module.exports = function(req, res, next) {
  // Get token from header
  let token = null;
  const authHeader = req.header('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const tokenPart = authHeader.substring(7); // Remove 'Bearer '
    if (tokenPart && tokenPart.trim() !== '') {
      token = tokenPart.trim();
    }
  }

  // Also check cookie for token (for web client)
  if (!token && req.cookies && req.cookies.authToken) {
    token = req.cookies.authToken;
  }

  // Check if token exists
  if (!token) {
    return res.status(401).json({ msg: 'No token, authorization denied' });
  }

  try {
    // Get JWT secret from environment or throw error if not set
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not set in environment variables');
      return res.status(500).json({ msg: 'Server configuration error' });
    }

    // Token validation successful - proceeding with JWT verification

    // Verify token
    const decoded = jwt.verify(token, jwtSecret);

    // Add user from token to request
    req.user = decoded.user;

    // Verify user exists in database and role is current
    db.user.findByPk(req.user.id, { attributes: ['id', 'username', 'role', 'status'] })
      .then(user => {
        if (!user) {
          return res.status(401).json({ msg: 'User no longer exists' });
        }
        // Check if user is active
        if (!user.status) {
          return res.status(401).json({ msg: 'User account is inactive' });
        }
        // Verify that role in token matches current role in database
        if (user.role !== req.user.role) {
          return res.status(401).json({
            msg: 'User role has changed, please log in again'
          });
        }
        // Update req.user with the latest data from the database
        req.user = {
          id: user.id,
          username: user.username,
          role: user.role
        };
        next();
      })
      .catch(err => {
        console.error('Database error in auth middleware:', err);
        return res.status(500).json({ msg: 'Server error' });
      });
  } catch (err) {
    console.error('Token validation error:', err.message);
    res.status(401).json({ msg: 'Token is not valid' });
  }
};
