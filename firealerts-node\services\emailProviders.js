/**
 * Email Providers
 * 
 * Manages multiple email delivery providers with fallback support
 */

const nodemailer = require('nodemailer');
const formData = require('form-data');
const Mailgun = require('mailgun.js');
const mailgun = new Mailgun(formData);

class EmailProviders {
    constructor() {
        this.mailgunClient = null;
        this.smtpTransporter = null;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return this;

        const db = require('../models');
        
        try {
            console.log('Initializing email providers...');
            
            // Initialize Mailgun if configured - try unified config first, then fallback to separate entries
            let mailgunApiKey = null;
            let mailgunDomain = null;

            // Try unified configuration first
            const mailgunConfig = await db.systemSetting.getValue('mailgun_config');
            if (mailgunConfig) {
                try {
                    const config = JSON.parse(mailgunConfig);
                    mailgunApiKey = config.api_key;
                    mailgunDomain = config.domain;
                    console.log('Using unified Mailgun configuration');
                } catch (parseError) {
                    console.error('Error parsing unified Mailgun configuration:', parseError);
                }
            }

            // Fallback to separate entries for backward compatibility
            if (!mailgunApiKey || !mailgunDomain) {
                console.log('Unified config not found, checking separate entries...');
                mailgunApiKey = mailgunApiKey || await db.systemSetting.getValue('mailgun_api_key');
                mailgunDomain = mailgunDomain || await db.systemSetting.getValue('mailgun_domain');
            }

            if (mailgunApiKey && mailgunDomain) {
                console.log('Configuring Mailgun provider...');
                this.mailgunClient = mailgun.client({ username: 'api', key: mailgunApiKey });
                this.mailgunDomain = mailgunDomain;
                console.log(`✅ Mailgun configured with domain: ${mailgunDomain}`);
            } else {
                console.log('⚠️ Mailgun not configured - missing API key or domain');
            }
            
            // Initialize SMTP as fallback
            const smtpHost = process.env.SMTP_HOST || await db.systemSetting.getValue('smtp_host');
            const smtpPort = process.env.SMTP_PORT || await db.systemSetting.getValue('smtp_port', '587');
            const smtpUser = process.env.SMTP_USER || await db.systemSetting.getValue('smtp_user');
            const smtpPassword = process.env.SMTP_PASSWORD || await db.systemSetting.getValue('smtp_password');
            const smtpSecure = process.env.SMTP_SECURE === 'true' || await db.systemSetting.getValue('smtp_secure') === 'true';
                  if (smtpHost && smtpUser && smtpPassword) {
                console.log('Configuring SMTP provider...');
                this.smtpTransporter = nodemailer.createTransport({
                    host: smtpHost,
                    port: parseInt(smtpPort),
                    secure: smtpSecure,
                    auth: {
                        user: smtpUser,
                        pass: smtpPassword
                    }
                });
            } else {
                console.log('SMTP not configured');
            }
            
            this.initialized = true;
            console.log('Email providers initialized successfully');
            
        } catch (error) {
            console.error('Error initializing email providers:', error);
            throw error;
        }

        return this;
    }

    /**
     * Send email with automatic provider selection and fallback
     */
    async send(emailContent, preferredProvider = 'auto') {
        if (!this.initialized) {
            await this.initialize();
        }

        const providers = this.getAvailableProviders();
        
        if (providers.length === 0) {
            throw new Error('No email providers configured');
        }

        // Determine send order based on preference
        let sendOrder = [...providers];
        
        if (preferredProvider === 'mailgun' && this.mailgunClient) {
            sendOrder = ['mailgun', ...providers.filter(p => p !== 'mailgun')];
        } else if (preferredProvider === 'smtp' && this.smtpTransporter) {
            sendOrder = ['smtp', ...providers.filter(p => p !== 'smtp')];
        }

        let lastError = null;

        // Try each provider in order
        for (const provider of sendOrder) {
            try {
                console.log(`Attempting to send email via ${provider}...`);
                const result = await this.sendViaProvider(provider, emailContent);
                console.log(`Email sent successfully via ${provider}`);
                return result;
            } catch (error) {
                console.error(`Failed to send via ${provider}:`, error.message);
                lastError = error;
                
                // Continue to next provider if available
                if (sendOrder.indexOf(provider) < sendOrder.length - 1) {
                    console.log(`Trying next provider...`);
                    continue;
                }
            }
        }

        // All providers failed
        throw new Error(`All email providers failed. Last error: ${lastError?.message || 'Unknown error'}`);
    }

    /**
     * Send email via specific provider
     */
    async sendViaProvider(provider, emailContent) {
        switch (provider) {
            case 'mailgun':
                return await this.sendViaMailgun(emailContent);
            case 'smtp':
                return await this.sendViaSmtp(emailContent);
            default:
                throw new Error(`Unknown email provider: ${provider}`);
        }
    }    /**
     * Send email via Mailgun
     */
    async sendViaMailgun(emailContent) {
        if (!this.mailgunClient || !this.mailgunDomain) {
            throw new Error('Mailgun not configured');
        }

        const messageData = {
            from: emailContent.from,
            to: emailContent.to,
            subject: emailContent.subject,
            text: emailContent.text,
            html: emailContent.html
        };

        console.log('Mailgun send attempt:');
        console.log('- Domain:', this.mailgunDomain);
        console.log('- From:', messageData.from);
        console.log('- To:', messageData.to);
        console.log('- Subject:', messageData.subject);

        try {
            const result = await this.mailgunClient.messages.create(this.mailgunDomain, messageData);
            return {
                provider: 'mailgun',
                messageId: result.id,
                status: result.status || 'queued'
            };
        } catch (error) {
            console.error('Mailgun API Error Details:');
            console.error('- Status:', error.status);
            console.error('- Message:', error.message);
            console.error('- Details:', error.details);
            throw error;
        }
    }

    /**
     * Send email via SMTP
     */
    async sendViaSmtp(emailContent) {
        if (!this.smtpTransporter) {
            throw new Error('SMTP not configured');
        }

        const result = await this.smtpTransporter.sendMail(emailContent);

        return {
            provider: 'smtp',
            messageId: result.messageId,
            status: 'sent'
        };
    }

    /**
     * Get list of available providers
     */
    getAvailableProviders() {
        const providers = [];
        
        if (this.mailgunClient && this.mailgunDomain) {
            providers.push('mailgun');
        }
        
        if (this.smtpTransporter) {
            providers.push('smtp');
        }
        
        return providers;
    }

    /**
     * Test a specific provider
     */
    async testProvider(provider, testEmail) {
        if (!this.initialized) {
            await this.initialize();
        }

        const testContent = {
            from: `"FireAlerts911" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
            to: testEmail,
            subject: `FireAlerts911 ${provider.toUpperCase()} Test`,
            text: `This is a test email sent via ${provider.toUpperCase()} at ${new Date().toLocaleString()}`,
            html: `
                <div style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2 style="color: #e53935;">FireAlerts911 ${provider.toUpperCase()} Test</h2>
                    <p>This is a test email sent via <strong>${provider.toUpperCase()}</strong></p>
                    <p><small>Sent at: ${new Date().toLocaleString()}</small></p>
                </div>
            `
        };

        return await this.sendViaProvider(provider, testContent);
    }

    /**
     * Get provider status
     */
    getProviderStatus() {
        return {
            mailgun: {
                configured: !!(this.mailgunClient && this.mailgunDomain),
                available: !!(this.mailgunClient && this.mailgunDomain)
            },
            smtp: {
                configured: !!this.smtpTransporter,
                available: !!this.smtpTransporter
            }
        };
    }
}

module.exports = {
    initialize: async () => {
        const providers = new EmailProviders();
        await providers.initialize();
        return providers;
    },
    EmailProviders
};
