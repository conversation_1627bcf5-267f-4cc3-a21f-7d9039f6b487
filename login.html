<!-- filepath: g:\GitHub\dispatchSite\login.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="robots" content="noindex,nofollow">
    <meta name="application-name" content="FireAlerts911">
    <title>FireAlerts911 - Login</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* Override auth container styles to match original exactly */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: var(--primary-dark);
            box-sizing: border-box;
            padding: 0;
        }
        
        /* Make sure auth card has exact same dimensions */
        .auth-card {
            width: 400px;
            background-color: var(--secondary-dark);
            border-radius: 5px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin: 0 auto;
        }
        
        /* Notification styling */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            max-width: 350px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
            transform: translateX(120%);
            transition: transform 0.3s ease-in-out;
            z-index: 1000;
            display: flex;
            align-items: center;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background-color: #4caf50;
        }
        
        .notification.error {
            background-color: #f44336;
        }
        
        .notification.info {
            background-color: #2196f3;
        }
        
        .notification.warning {
            background-color: #ff9800;
        }
        
        .notification i {
            margin-right: 10px;
        }
        
        /* Form validation styling */
        .form-control.error {
            border-color: #f44336;
        }
        
        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }

        /* Password feedback */
        .password-error {
            color: #f44336;
            font-size: 14px;
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: rgba(244, 67, 54, 0.1);
            border-left: 3px solid #f44336;
            display: none;
        }
        
        .password-error.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <i class="fas fa-fire-alt fa-2x" style="color: #e53935; margin-right: 10px;"></i>
                    <h1 style="margin: 0; font-size: 24px;">FireAlerts911</h1>
                </div>
                <h2 style="margin: 0; font-size: 18px; font-weight: 400;">Admin Login</h2>
            </div>
            <div class="auth-body">
                <form id="loginForm" action="javascript:void(0);" method="post" autocomplete="on">
                    <div class="form-group">
                        <label class="form-label" for="username">Username</label>
                        <div style="position: relative;">
                            <input type="text" class="form-control" id="username" name="username" required autocomplete="username" style="padding-left: 40px;">
                            <i class="fas fa-user" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: var(--text-secondary);"></i>
                        </div>
                        <div class="error-message" id="username-error">Username is required</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="password">Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control" id="password" name="password" required autocomplete="current-password" style="padding-left: 40px;">
                            <i class="fas fa-lock" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: var(--text-secondary);"></i>
                        </div>
                        <div class="error-message" id="password-error">Password is required</div>
                    </div>
                    
                    <!-- Password Error Message Container -->
                    <div class="password-error" id="auth-error">
                        <i class="fas fa-exclamation-circle"></i> Incorrect username or password
                    </div>
                    
                    <!-- Hidden fields to help browsers with form detection -->
                    <input type="hidden" name="login" value="true">
                    
                    <div class="form-group">
                        <label class="form-check" for="remember">
                            <input type="checkbox" name="remember" id="remember">
                            <span style="margin-left: 10px;">Remember me</span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="loginButton" style="width: 100%;">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                </form>
            </div>
            <div class="auth-footer">
                <a href="forgot-password.html" style="color: var(--accent-blue); text-decoration: none;">Forgot Password?</a>
            </div>
        </div>
    </div>
    
    <!-- Notification element -->
    <div class="notification" id="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notification-message"></span>
    </div>
    
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Get login reason from URL
            const urlParams = new URLSearchParams(window.location.search);
            const reason = urlParams.get("reason");
            const details = urlParams.get("details");
            
            // Show appropriate message based on the reason
            if (reason) {
                let message = "";
                
                switch (reason) {
                    case "session_expired":
                        message = details || "Your session has expired. Please log in again.";
                        break;
                    case "auth_required":
                        message = "Authentication required to access this page.";
                        break;
                    case "token_missing":
                        message = "Authentication token missing. Please log in again.";
                        break;
                    case "token_invalid":
                        message = "Invalid authentication. Please log in again.";
                        break;
                    default:
                        message = "Please log in to continue.";
                }
                
                // Show notification if showNotification function is available
                if (typeof window.showNotification === "function") {
                    window.showNotification(message, "info");
                } else {
                    // Fallback notification function
                    showLocalNotification(message, "info");
                }
            }
            
            // Local notification function
            function showLocalNotification(message, type = "info") {
                const notification = document.getElementById("notification");
                const notificationMessage = document.getElementById("notification-message");
                
                // Remove existing classes
                notification.className = "notification";
                notification.classList.add(type);
                
                // Set message
                notificationMessage.textContent = message;
                
                // Show notification
                notification.classList.add("show");
                
                // Hide after 5 seconds
                setTimeout(() => {
                    notification.classList.remove("show");
                }, 5000);
            }
            
            // Make function available globally for this page
            window.showLocalNotification = showLocalNotification;
            
            // Form validation function
            function validateForm() {
                const username = document.getElementById("username");
                const password = document.getElementById("password");
                let isValid = true;
                
                // Validate username
                if (!username.value.trim()) {
                    username.classList.add("error");
                    document.getElementById("username-error").classList.add("show");
                    isValid = false;
                } else {
                    username.classList.remove("error");
                    document.getElementById("username-error").classList.remove("show");
                }
                
                // Validate password
                if (!password.value) {
                    password.classList.add("error");
                    document.getElementById("password-error").classList.add("show");
                    isValid = false;
                } else {
                    password.classList.remove("error");
                    document.getElementById("password-error").classList.remove("show");
                }
                
                return isValid;
            }
            
            // Add input event listeners to remove error state when typing
            document.getElementById("username").addEventListener("input", function() {
                this.classList.remove("error");
                document.getElementById("username-error").classList.remove("show");
                document.getElementById("auth-error").classList.remove("show");
            });
            
            document.getElementById("password").addEventListener("input", function() {
                this.classList.remove("error");
                document.getElementById("password-error").classList.remove("show");
                document.getElementById("auth-error").classList.remove("show");
            });
            
            // Form submission handler
            document.getElementById("loginForm").addEventListener("submit", function(e) {
                e.preventDefault();
                
                // Validate form
                if (!validateForm()) {
                    return;
                }
                
                // Hide any previous auth errors
                document.getElementById("auth-error").classList.remove("show");
                
                const username = this.elements.username.value.trim();
                const password = this.elements.password.value;
                const rememberMe = this.elements.remember.checked;
                
                // Show loading state
                const submitButton = document.getElementById("loginButton");
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Logging in...";
                submitButton.disabled = true;
                
                // Call the API login method
                API.auth.login(username, password)
                    .then(response => {
                        console.log("Login response:", response);
                        
                        if (response && response.token) {
                            // Store the token based on remember me setting
                            if (rememberMe) {
                                localStorage.setItem("token", response.token);
                                localStorage.setItem("isLoggedIn", "true");
                            } else {
                                sessionStorage.setItem("token", response.token);
                                localStorage.setItem("isLoggedIn", "true");
                            }
                            
                            // Store user info including role for role-based UI
                            if (response.user) {
                                const userInfo = {
                                    id: response.user.id,
                                    username: response.user.username,
                                    firstName: response.user.firstName,
                                    lastName: response.user.lastName,
                                    role: response.user.role
                                };
                                
                                localStorage.setItem("userInfo", JSON.stringify(userInfo));
                            }
                            
                            // Check for redirect URL in session storage
                            const redirectUrl = sessionStorage.getItem("redirectAfterLogin");
                            
                            // Use the showNotification function from modern-dispatch.js
                            if (typeof window.showNotification === "function") {
                                window.showNotification("Login successful! Redirecting...", "success");
                            } else {
                                showLocalNotification("Login successful! Redirecting...", "success");
                            }
                            
                            // Redirect to dashboard or the page user was trying to access
                            setTimeout(() => {
                                if (redirectUrl) {
                                    sessionStorage.removeItem("redirectAfterLogin");
                                    window.location.href = redirectUrl;
                                } else {
                                    window.location.href = "dashboard.html";
                                }
                            }, 1000);
                        } else {
                            // Show prominent error message for authentication failure
                            document.getElementById("auth-error").classList.add("show");
                            
                            // Also show notification
                            if (typeof window.showNotification === "function") {
                                window.showNotification(response?.message || "Login failed. Please check your credentials.", "error");
                            } else {
                                showLocalNotification(response?.message || "Login failed. Please check your credentials.", "error");
                            }
                            
                            // Reset button state
                            submitButton.innerHTML = originalText;
                            submitButton.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Show error notification
                        if (typeof window.showNotification === "function") {
                            window.showNotification("An error occurred while connecting to the server. Please try again.", "error");
                        } else {
                            showLocalNotification("An error occurred while connecting to the server. Please try again.", "error");
                        }
                        
                        console.error("Login error:", error);
                        
                        // Reset button state
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    });
            });
            
            // Clear any stored tokens if on the login page (optional security measure)
            if (window.location.pathname.endsWith("login.html")) {
                // Only clear if user is not trying to log back in after session expired
                if (!urlParams.get("reason")) {
                    localStorage.removeItem("token");
                    sessionStorage.removeItem("token");
                }
            }
        });
    </script>
</body>
</html>
