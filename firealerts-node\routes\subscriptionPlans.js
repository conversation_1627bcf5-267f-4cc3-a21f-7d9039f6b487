/**
 * Subscription Plans API routes
 * These routes handle access to subscription plans in the system
 */
const express = require('express');
const router = express.Router();
const db = require('../models');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/subscription-plans
 * @desc    Get all subscription plans
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    // Get distinct subscription types from companies table
    const subscriptionPlans = await db.company.findAll({
      attributes: ['id', 'subscriptionType'],
      where: {
        subscriptionType: {
          [db.Sequelize.Op.ne]: null // Only get non-null subscription types
        },
        status: true // Only get active subscription plans
      },
      order: [['subscriptionType', 'ASC']]
    });

    // Transform to a more friendly format for the frontend
    const formattedPlans = subscriptionPlans.map(plan => {
      // Handle case where subscriptionType might be null despite the WHERE clause
      if (!plan.subscriptionType) return null;
      
      return {
        id: plan.id,
        name: plan.subscriptionType.charAt(0).toUpperCase() + plan.subscriptionType.slice(1), // Capitalize first letter
        code: plan.subscriptionType
      };
    }).filter(plan => plan !== null); // Remove any null entries
    
    // If no subscription plans found, return default ones
    if (formattedPlans.length === 0) {
      console.log('No subscription plans found in database, returning defaults');
      return res.json([
        { id: 'basic', name: 'Basic Plan', code: 'basic' },
        { id: 'standard', name: 'Standard Plan', code: 'standard' },
        { id: 'premium', name: 'Premium Plan', code: 'premium' },
        { id: 'enterprise', name: 'Enterprise Plan', code: 'enterprise' }
      ]);
    }
    
    return res.json(formattedPlans);
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    // Return default plans on error to ensure UI doesn't break
    return res.json([
      { id: 'basic', name: 'Basic Plan', code: 'basic' },
      { id: 'standard', name: 'Standard Plan', code: 'standard' },
      { id: 'premium', name: 'Premium Plan', code: 'premium' },
      { id: 'enterprise', name: 'Enterprise Plan', code: 'enterprise' }
    ]);
  }
});

/**
 * @route   GET /api/subscription-plans/:id
 * @desc    Get subscription plan by ID
 * @access  Authenticated
 */
router.get('/:id', auth, (req, res) => {
  db.company.findByPk(req.params.id, {
    attributes: ['id', 'name', 'subscriptionType', 'subscriptionExpiry']
  })
  .then(plan => {
    if (!plan) {
      return res.status(404).json({ message: 'Subscription plan not found' });
    }
    
    return res.json({
      id: plan.id,
      name: plan.name || 'Unnamed Plan',
      type: plan.subscriptionType,
      expiresAt: plan.subscriptionExpiry
    });
  })
  .catch(error => {
    console.error('Error fetching subscription plan:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  });
});

module.exports = router;
