const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { check, validationResult } = require('express-validator');
const db = require('../models');
const authMiddleware = require('../middleware/auth');

// Cookie configuration
const cookieConfig = {
  httpOnly: true,                   // Not accessible via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'lax',                  // Lax CSRF protection - allows cross-tab sharing
  maxAge: 24 * 60 * 60 * 1000,      // 24 hours
  path: '/'                         // Available for entire domain, no explicit domain for localhost
};

// @route   POST api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post(
  '/login',
  [
    check('username', 'Username is required').exists(),
    check('password', 'Password is required').exists()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, password } = req.body;

    try {
      // Find user by username
      const user = await db.user.findOne({
        where: { username }
      });

      if (!user) {
        return res.status(400).json({ msg: 'Invalid credentials' });
      }

      // Check if user is active
      if (!user.status) {
        return res.status(400).json({ msg: 'Account has been deactivated' });
      }

      // Check password
      const isMatch = await bcrypt.compare(password, user.password);

      if (!isMatch) {

        // Log failed login attempt
        await db.activity.create({
          action: 'failed_login',
          details: JSON.stringify({ username }),
          userId: null,
          ip: req.ip || req.connection.remoteAddress,
          module: 'auth',
          severity: 'warning'
        });

        return res.status(400).json({ msg: 'Invalid credentials' });
      }

      // Get JWT secret from environment or throw error if not set
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        console.error('JWT_SECRET is not set in environment variables');
        return res.status(500).json({ msg: 'Server configuration error' });
      }

      // Create JWT payload with minimal data
      const payload = {
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      };

      // Sign token
      jwt.sign(
        payload,
        jwtSecret,
        { expiresIn: '24h' },
        (err, token) => {
          if (err) throw err;

          // Set the token as an HTTP-only cookie
          res.cookie('authToken', token, cookieConfig);

          // Log successful login
          db.activity.create({
            action: 'login',
            details: JSON.stringify({ username, role: user.role }),
            userId: user.id,
            ip: req.ip || req.connection.remoteAddress,
            module: 'auth',
            severity: 'info'
          }).catch(err => {
            console.error('Error logging login activity:', err);
          });

          // Return token and minimal user info in response body for API usage
          res.json({
            token,
            user: {
              id: user.id,
              username: user.username,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              role: user.role
            }
          });
        }
      );
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   POST api/auth/logout
// @desc    Log out user by clearing cookies
// @access  Public
router.post('/logout', (req, res) => {
  // Get token from header or cookie
  const token = req.header('Authorization')?.replace('Bearer ', '') ||
               (req.cookies && req.cookies.authToken);

  // Log the logout if we have user info
  if (token) {
    try {
      // Try to decode the token to get user info for logging
      const jwtSecret = process.env.JWT_SECRET;
      if (jwtSecret) {
        const decoded = jwt.verify(token, jwtSecret);
        if (decoded && decoded.user) {
          // Log logout activity
          db.activity.create({
            action: 'logout',
            details: JSON.stringify({ username: decoded.user.username }),
            userId: decoded.user.id,
            ip: req.ip || req.connection.remoteAddress,
            module: 'auth',
            severity: 'info'
          }).catch(err => {
            console.error('Error logging logout activity:', err);
          });
        }
      }
    } catch (err) {
      // Just log the error but continue with logout
      console.error('Error logging logout:', err);
    }
  }

  // Clear the authentication cookie with proper configuration
  res.clearCookie('authToken', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });
  res.json({ msg: 'Logged out successfully' });
});

// @route   GET api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authMiddleware, async (req, res) => {
  // The auth middleware will attach the user to the request
  try {
    const user = await db.user.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/auth/validate
// @desc    Validate token - useful for client-side auth checks
// @access  Private
router.get('/validate', authMiddleware, (req, res) => {
  // Since we're using the enhanced authMiddleware, it already:
  // 1. Verifies the token
  // 2. Checks that the user exists in the database
  // 3. Verifies their role matches what's in the token
  // 4. Ensures the account is active

  // At this point, req.user contains the latest user data from the database
  res.json({
    valid: true,
    user: req.user
  });
});

// @route   GET api/auth/check-cookie
// @desc    Check if auth cookie exists without full validation - for cross-tab session detection
// @access  Public
router.get('/check-cookie', (req, res) => {
  // Simply check if the auth cookie exists
  const hasCookie = !!(req.cookies && req.cookies.authToken);

  res.json({
    hasCookie: hasCookie
  });
});

module.exports = router;
