const db = require('./models');
const bcrypt = require('bcryptjs');

async function initializeDatabase() {
  try {
    console.log('Starting database initialization...');

    // Sync all models with the database
    await db.sequelize.sync({ force: true });
    console.log('Database tables created');

    // Create incident types
    console.log('Creating incident types...');
    await db.incidentType.bulkCreate([
      { name: 'Fire - Residential', category: 'fire', icon: 'fa-fire' },
      { name: 'Fire - Commercial', category: 'fire', icon: 'fa-building-fire' },
      { name: 'Fire - Vehicle', category: 'fire', icon: 'fa-car-fire' },
      { name: 'Fire - Wildland', category: 'fire', icon: 'fa-tree-fire' },
      { name: 'Water - Flood', category: 'water', icon: 'fa-water' },
      { name: 'Water - Rescue', category: 'water', icon: 'fa-life-ring' },
      { name: 'Water - Main Break', category: 'water', icon: 'fa-pipe-water' },
      { name: 'Medical Emergency', category: 'other', icon: 'fa-ambulance' },
      { name: 'Gas Leak', category: 'other', icon: 'fa-gas-pump' },
      { name: 'Storm Damage', category: 'other', icon: 'fa-cloud-bolt' }
    ]);

    // Create statuses
    console.log('Creating incident statuses...');
    await db.status.bulkCreate([
      { name: 'Active', color: 'blue' },
      { name: 'Pending', color: 'orange' },
      { name: 'Critical', color: 'red' },
      { name: 'Resolved', color: 'green' },
      { name: 'False Alarm', color: 'gray' }
    ]);

    // Create locations (sample counties/cities)
    console.log('Creating locations...');
    const locations = [
      { city: 'Los Angeles', state: 'CA', county: 'Los Angeles', zip: '90001', latitude: 34.0522, longitude: -118.2437 },
      { city: 'San Francisco', state: 'CA', county: 'San Francisco', zip: '94016', latitude: 37.7749, longitude: -122.4194 },
      { city: 'Sacramento', state: 'CA', county: 'Sacramento', zip: '95814', latitude: 38.5816, longitude: -121.4944 },
      { city: 'Fresno', state: 'CA', county: 'Fresno', zip: '93650', latitude: 36.7468, longitude: -119.7726 },
      { city: 'Chicago', state: 'IL', county: 'Cook', zip: '60601', latitude: 41.8781, longitude: -87.6298 },
      { city: 'New York', state: 'NY', county: 'New York', zip: '10001', latitude: 40.7128, longitude: -74.0060 },
      { city: 'Miami', state: 'FL', county: 'Miami-Dade', zip: '33101', latitude: 25.7617, longitude: -80.1918 }
    ];
    await db.location.bulkCreate(locations);

    // Create admin user
    console.log('Creating admin user...');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    await db.user.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: 'admin'
    });

    // Create sample dispatcher
    const dispatcherPassword = await bcrypt.hash('dispatch123', salt);
    await db.user.create({
      firstName: 'Test',
      lastName: 'Dispatcher',
      email: '<EMAIL>',
      username: 'dispatcher',
      password: dispatcherPassword,
      role: 'dispatcher'
    });

    // Sample company creation disabled per user request
    console.log('🚫 Sample company creation is disabled. No sample companies or company users will be created.');
    console.log('   Companies should be created manually through the admin interface.');

    // Skip all company and company user creation
    // const company = null; // No sample company will be created

    // Create sample incidents
    console.log('Creating sample incidents...');
    const now = new Date();

    const incidents = [
      {
        title: 'Residential Structure Fire',
        address: '123 Oak Avenue',
        description: 'Two-alarm fire in residential building',
        incidentDate: now,
        userId: 1,
        incidentTypeId: 1,
        statusId: 1,
        locationId: 1 // Los Angeles
      },
      {
        title: 'Water Main Break',
        address: '456 Elm Street',
        description: 'Major water main break affecting 3 blocks',
        incidentDate: new Date(now.getTime() - 30*60000), // 30 minutes ago
        userId: 1,
        incidentTypeId: 7,
        statusId: 1,
        locationId: 1 // Los Angeles
      },
      {
        title: 'Brush Fire',
        address: 'Westside Park',
        description: 'Fast-moving brush fire approaching residential area',
        incidentDate: new Date(now.getTime() - 24*60*60000), // 1 day ago
        userId: 2,
        incidentTypeId: 4,
        statusId: 3,
        locationId: 2 // San Francisco
      }
    ];

    for (const incident of incidents) {
      const createdIncident = await db.incident.create(incident);

      // Create incident details
      await db.incidentDetail.create({
        incidentId: createdIncident.id,
        structureType: incident.incidentTypeId === 1 ? 'Residential' : null,
        smokeType: incident.incidentTypeId <= 4 ? 'Heavy black smoke' : null,
        evacuationStatus: incident.incidentTypeId === 4 ? 'mandatory' : 'none',
        waterLevel: incident.incidentTypeId === 5 ? '3 feet' : null,
        notes: 'Sample incident created during system initialization'
      });
    }

    console.log('Database initialization complete!');
  } catch (error) {
    console.error('Database initialization failed:', error);
    console.error(error.stack);
  }
}

// Run the initialization
initializeDatabase().catch(console.error);
