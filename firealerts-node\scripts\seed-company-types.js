/**
 * <PERSON>ript to seed company types into the database.
 * This ensures there are default company types available for the subscriber form.
 */
require('dotenv').config();
const { companyType: CompanyType, sequelize } = require('../models');

// Import table creation utility
const { ensureTablesExist } = require('./utils/table-creation');

const defaultCompanyTypes = [
  { name: 'Fire Department', code: 'FIRE_DEPT', description: 'Fire departments and fire protection services' },
  { name: 'EMS', code: 'EMS', description: 'Emergency Medical Services' },
  { name: 'Police Department', code: 'POLICE_DEPT', description: 'Law enforcement agencies' },
  { name: 'Emergency Management', code: 'EMERGENCY_MGMT', description: 'Emergency management and coordination agencies' },
  { name: 'Dispatch Center', code: 'DISPATCH', description: '911 dispatch and communication centers' },
  { name: 'Hospital', code: 'HOSPITAL', description: 'Hospitals and medical facilities' },
  { name: 'Government Agency', code: 'GOVT_AGENCY', description: 'Government agencies and departments' },
  { name: 'Private Company', code: 'PRIVATE', description: 'Private companies and organizations' },
  { name: 'Other', code: 'OTHER', description: 'Other organization types' }
];

/**
 * Seeds company types into the database
 */
async function seedCompanyTypes() {
  try {
    console.log('🔧 Ensuring company_types table exists...');

    // Ensure the company_types table exists
    try {
      const models = { CompanyType };
      await ensureTablesExist(sequelize, models, {
        verbose: false,
        force: false,
        alter: false
      });
      console.log('✅ Company types table verified');
    } catch (tableError) {
      console.error('❌ Failed to create company_types table:', tableError.message);
      throw tableError;
    }

    console.log('Checking for existing company types...');

    // Get count of existing company types
    const count = await CompanyType.count();

    if (count > 0) {
      console.log(`Found ${count} existing company types. Skipping seeding.`);
      return;
    }

    console.log('No company types found. Seeding default company types...');

    // Create all company types
    await CompanyType.bulkCreate(defaultCompanyTypes);

    console.log(`Successfully seeded ${defaultCompanyTypes.length} company types.`);
  } catch (error) {
    console.error('Error seeding company types:', error);
    throw error;
  }
}

// Run the seed function if this script is executed directly
if (require.main === module) {
  seedCompanyTypes()
    .then(() => {
      console.log('Company type seeding complete.');
      process.exit(0);
    })
    .catch(err => {
      console.error('Failed to seed company types:', err);
      process.exit(1);
    });
} else {
  // Export for use in other scripts
  module.exports = seedCompanyTypes;
}
