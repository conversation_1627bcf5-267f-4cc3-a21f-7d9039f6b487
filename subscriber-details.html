<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Subscriber Details</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">

    <style>
        /* County Selector Improvements */
        .county-selector-container {
            background-color: rgba(39, 46, 72, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }

        /* Search-first Interface Styles */
        .search-first {
            position: relative;
            margin-bottom: 15px;
        }

        .location-search {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
            color: var(--text-light);
            font-size: 16px;
        }

        .location-search:focus {
            border-color: var(--accent-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            pointer-events: none;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: var(--secondary-dark);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 250px;
            overflow-y: auto;
            z-index: 10;
            display: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .search-result {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.2s;
        }

        .search-result:hover {
            background-color: var(--hover-bg);
        }

        .search-result:last-child {
            border-bottom: none;
        }

        .result-county {
            font-weight: 500;
            color: var(--text-light);
        }

        .result-state {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        /* Tree View Styles */
        .tree-fallback {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 15px;
        }

        .fallback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .toggle-browse {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .toggle-browse:hover {
            background-color: var(--hover-bg);
            color: var(--text-light);
        }

        .toggle-browse i {
            transition: transform 0.2s;
        }

        .toggle-browse.expanded i {
            transform: rotate(180deg);
        }

        .tree-selector {
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
        }

        .tree-node {
            padding: 10px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: none;
        }

        .tree-node:hover {
            background-color: var(--hover-bg);
        }

        .tree-state {
            font-weight: 500;
            background-color: rgba(30, 136, 229, 0.1);
            border-left: 3px solid var(--accent-blue);
            display: flex;
            align-items: center;
        }

        .tree-state .expand-icon {
            margin-right: 8px;
            transition: transform 0.2s;
            font-size: 12px;
        }

        .tree-state.collapsed .expand-icon {
            transform: rotate(-90deg);
        }

        .tree-state .state-checkbox {
            margin-right: 8px;
            margin-left: auto;
        }

        .tree-counties {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .tree-state.collapsed + .tree-counties {
            display: none;
        }

        .tree-county {
            padding-left: 35px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .tree-county input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Selected Counties Display */
        .selected-counties {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(30, 136, 229, 0.1);
            border-radius: 4px;
            border: 1px solid var(--accent-blue);
        }

        .county-tag {
            display: inline-block;
            background-color: var(--accent-blue);
            color: white;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 12px;
            position: relative;
        }

        .county-tag .remove {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
            font-weight: bold;
        }

        .county-tag .remove:hover {
            opacity: 1;
            color: #ffcccc;
        }

        /* Loading states */
        .loading-message {
            padding: 20px;
            text-align: center;
            color: var(--text-secondary);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .county-selector-container {
                padding: 15px;
            }

            .location-search {
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .search-results {
                max-height: 200px;
            }

            .tree-selector {
                max-height: 250px;
            }
        }

        /* View/Edit Mode Styles */
        .read-only-mode .form-control,
        .read-only-mode .form-control[readonly] {
            background-color: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--border-color);
            color: var(--text-light);
            cursor: default;
            pointer-events: none;
        }

        .read-only-mode .form-control:focus {
            box-shadow: none;
            border-color: var(--border-color);
        }

        .read-only-mode .btn:not(.btn-outline):not(.btn-primary):not(.btn-success):not(#editModeBtn) {
            display: none;
        }

        .read-only-mode #addUserBtn {
            display: none;
        }

        .edit-mode .form-control {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            pointer-events: auto;
        }

        /* Status badge styles */
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 80px;
        }

        .status-active {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }

        .status-inactive {
            background-color: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            border: 1px solid #6c757d;
        }

        .status-sent {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }

        .status-pending {
            background-color: rgba(255, 143, 0, 0.2);
            color: var(--accent-orange);
            border: 1px solid var(--accent-orange);
        }

        .status-failed {
            background-color: rgba(229, 57, 53, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        .status-delivered {
            background-color: rgba(30, 136, 229, 0.2);
            color: var(--accent-blue);
            border: 1px solid var(--accent-blue);
        }

        .status-info {
            background-color: rgba(30, 136, 229, 0.2);
            color: var(--accent-blue);
            border: 1px solid var(--accent-blue);
        }

        .status-warning {
            background-color: rgba(255, 143, 0, 0.2);
            color: var(--accent-orange);
            border: 1px solid var(--accent-orange);
        }

        .status-error {
            background-color: rgba(229, 57, 53, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        .status-critical {
            background-color: rgba(229, 57, 53, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        /* Notification type badges */
        .notification-type-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
        }

        .notification-email {
            background-color: rgba(30, 136, 229, 0.2);
            color: var(--accent-blue);
        }

        .notification-sms {
            background-color: rgba(255, 143, 0, 0.2);
            color: var(--accent-orange);
        }

        .notification-push {
            background-color: rgba(40, 167, 69, 0.2);
            color: var(--accent-green);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Subscriber Details Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-building" id="page-icon" style="margin-right: 10px;"></i>
                        <span id="page-title">View Subscriber</span>: <span id="companyNameTitle"></span>
                    </div>
                    <div class="card-actions">
                        <a href="subscribers.html" class="btn btn-outline btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Subscribers
                        </a>
                        <button id="editModeBtn" class="btn btn-primary btn-sm" style="display: none;">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button id="viewModeBtn" class="btn btn-outline btn-sm" style="display: none;">
                            <i class="fas fa-eye"></i> View Mode
                        </button>
                        <button id="saveModeBtn" class="btn btn-success btn-sm" style="display: none;">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Tabs for different sections -->
                    <div class="tabs">
                        <div class="tab-list">
                            <button class="tab-button active" data-tab="company-info">Company Info</button>
                            <button class="tab-button" data-tab="users">Users</button>
                            <button class="tab-button" data-tab="subscription">Subscription</button>
                            <button class="tab-button" data-tab="counties" id="counties-tab">Counties</button>
                            <button class="tab-button" data-tab="incident-activity">Incident Activity</button>
                            <button class="tab-button" data-tab="account-changes">Account Changes</button>
                        </div>

                        <!-- Company Info Tab -->
                        <div class="tab-content active" id="company-info">
                            <form id="companyInfoForm" action="#" method="post">
                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Company Name *</label>
                                            <input type="text" class="form-control" name="company_name" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Company Type</label>
                                            <select class="form-control" name="company_type">
                                                <option value="">Select Type</option>
                                                <option value="fire_department">Fire Department</option>
                                                <option value="police_department">Police Department</option>
                                                <option value="emergency_services">Emergency Services</option>
                                                <option value="government">Government Agency</option>
                                                <option value="private_security">Private Security</option>
                                                <option value="insurance">Insurance Company</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" name="company_phone" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Email *</label>
                                            <input type="email" class="form-control" name="company_email" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Address *</label>
                                    <input type="text" class="form-control" name="company_address" required>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">City *</label>
                                            <input type="text" class="form-control" name="company_city" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">State *</label>
                                            <input type="text" class="form-control" name="company_state" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">ZIP Code *</label>
                                            <input type="text" class="form-control" name="company_zip" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Additional Notes</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Account Status</label>
                                    <select class="form-control" name="status">
                                        <option value="active">Active</option>
                                        <option value="pending">Pending</option>
                                        <option value="trial">Trial</option>
                                        <option value="blocked">Blocked</option>
                                    </select>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Users Tab -->
                        <div class="tab-content" id="users">
                            <div style="margin-bottom: 20px; display: flex; justify-content: flex-end;">
                                <button class="btn btn-primary" id="addUserBtn">
                                    <i class="fas fa-user-plus"></i> Add User
                                </button>
                            </div>

                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Last Login</th>
                                            <th>Status</th>
                                            <th style="width: 120px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- User rows will be loaded dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Subscription Tab -->
                        <div class="tab-content" id="subscription">
                            <div class="subscription-overview" style="background-color: rgba(30, 136, 229, 0.1); border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <h4 style="margin: 0;">Current Plan: <strong id="currentPlan"></strong></h4>
                                    <span class="status-badge" id="subscriptionStatus"></span>
                                </div>
                                <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                                    <div>
                                        <div style="color: var(--text-secondary);">Billing Cycle</div>
                                        <div><strong id="billingCycle"></strong></div>
                                    </div>
                                    <div>
                                        <div style="color: var(--text-secondary);">Next Billing Date</div>
                                        <div><strong id="nextBillingDate"></strong></div>
                                    </div>
                                    <div>
                                        <div style="color: var(--text-secondary);">Users</div>
                                        <div><strong id="usersCount"></strong></div>
                                    </div>
                                </div>
                            </div>

                            <form id="subscriptionForm">
                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Subscription Plan</label>
                                            <select class="form-control" name="subscription_plan">
                                                <option value="basic">Basic Plan</option>
                                                <option value="standard">Standard Plan</option>
                                                <option value="premium">Premium Plan</option>
                                                <option value="enterprise">Enterprise Plan</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Maximum Users</label>
                                            <input type="number" class="form-control" name="max_users" min="1" max="100">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Billing Cycle</label>
                                            <select class="form-control" name="billing_cycle">
                                                <option value="monthly">Monthly</option>
                                                <option value="annual">Annual</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Billing Contact Email</label>
                                            <input type="email" class="form-control" name="billing_email">
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Subscription
                                    </button>
                                </div>
                            </form>

                            <h3 class="section-title">Billing History</h3>

                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Invoice #</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="billingHistoryBody">
                                        <!-- Billing history will be loaded dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Counties Tab -->
                        <div class="tab-content" id="counties">
                            <!-- Admin Access Check -->
                            <div id="counties-access-denied" style="display: none;">
                                <div style="text-align: center; padding: 40px; background-color: rgba(255, 193, 7, 0.1); border: 1px solid #ffc107; border-radius: 8px;">
                                    <i class="fas fa-lock" style="font-size: 48px; color: #ffc107; margin-bottom: 20px;"></i>
                                    <h3 style="color: #ffc107; margin-bottom: 10px;">Admin Access Required</h3>
                                    <p style="color: var(--text-secondary);">County management is restricted to administrators only.</p>
                                </div>
                            </div>

                            <!-- Counties Management Form (Admin Only) -->
                            <div id="counties-management" style="display: none;">
                                <!-- Current Counties Display -->
                                <div class="form-group">
                                    <label class="form-label">Currently Subscribed Counties</label>
                                    <div id="current-counties-display" style="background-color: rgba(30, 136, 229, 0.1); border: 1px solid var(--accent-blue); border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                                        <div id="current-counties-list">Loading...</div>
                                        <small style="color: var(--text-secondary); margin-top: 10px; display: block;">
                                            These counties are assigned to all users in this company for incident notifications.
                                        </small>
                                    </div>
                                </div>

                                <form id="countiesForm">
                                    <div class="form-group">
                                        <label class="form-label">Manage Counties of Interest</label>

                                    <!-- Search-first Interface (Option 4) -->
                                    <div class="county-selector-container">
                                        <div class="search-first">
                                            <input type="text" class="location-search" id="location-search" placeholder="🔍 Start typing county or state name (fastest way)..." />
                                            <i class="fas fa-search search-icon"></i>
                                            <div class="search-results" id="search-results">
                                                <!-- Search results will appear here -->
                                            </div>
                                        </div>

                                        <!-- Tree View Browser (Option 2) -->
                                        <div class="tree-fallback" style="margin-top: 20px;">
                                            <div class="fallback-header">
                                                <span>Or browse by state:</span>
                                                <button type="button" class="toggle-browse" id="toggle-browse">
                                                    <i class="fas fa-chevron-down"></i> Show State Tree
                                                </button>
                                            </div>
                                            <div class="tree-selector" id="tree-browser" style="display: none;">
                                                <div class="loading-message">Loading states and counties...</div>
                                            </div>
                                        </div>

                                        <!-- Selected Counties Display -->
                                        <div class="selected-counties" id="selected-counties-display">
                                            <strong>Selected Counties:</strong>
                                            <div id="county-tags-display">None selected</div>
                                        </div>
                                    </div>

                                    <small style="color: var(--text-secondary);">Select counties you want to add to your subscription. Search is fastest, or browse using the expandable state tree below.</small>
                                </div>

                                    <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Counties
                                        </button>
                                    </div>
                                </form>

                                <!-- Separate Alert Settings Form -->
                                <form id="alertSettingsForm" style="margin-top: 30px; border-top: 1px solid var(--border-color); padding-top: 20px;">
                                    <h3 class="section-title">Alert Settings</h3>
                                    <div class="form-row">
                                        <div class="form-col">
                                            <div class="form-group">
                                                <label class="form-label">Incident Types</label>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="pref_fire">
                                                        <span style="margin-left: 10px;">Fire Incidents</span>
                                                    </label>
                                                </div>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="pref_water">
                                                        <span style="margin-left: 10px;">Water Incidents</span>
                                                    </label>
                                                </div>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="pref_other">
                                                        <span style="margin-left: 10px;">Other Incidents</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-col">
                                            <div class="form-group">
                                                <label class="form-label">Notification Methods</label>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="notify_email">
                                                        <span style="margin-left: 10px;">Email Notifications</span>
                                                    </label>
                                                </div>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="notify_sms">
                                                        <span style="margin-left: 10px;">SMS Notifications</span>
                                                    </label>
                                                </div>
                                                <div style="margin-top: 10px;">
                                                    <label class="form-check">
                                                        <input type="checkbox" name="notify_app">
                                                        <span style="margin-left: 10px;">Mobile App Notifications</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">Notification Frequency</label>
                                        <select class="form-control" name="notification_frequency">
                                            <option value="immediate">Immediate (as incidents occur)</option>
                                            <option value="hourly">Hourly Digest</option>
                                            <option value="daily">Daily Digest</option>
                                        </select>
                                    </div>

                                    <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Alert Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Incident Activity Tab -->
                        <div class="tab-content" id="incident-activity">
                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Incident Type</th>
                                            <th>Location</th>
                                            <th>Notification Type</th>
                                            <th>Status</th>
                                            <th>User</th>
                                        </tr>
                                    </thead>
                                    <tbody id="incidentActivityBody">
                                        <tr>
                                            <td colspan="6" style="text-align: center; padding: 20px;">
                                                <i class="fas fa-spinner fa-spin"></i> Loading incident activity...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Account Changes Tab -->
                        <div class="tab-content" id="account-changes">
                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Action</th>
                                            <th>Details</th>
                                            <th>User</th>
                                            <th>Severity</th>
                                        </tr>
                                    </thead>
                                    <tbody id="accountChangesBody">
                                        <tr>
                                            <td colspan="5" style="text-align: center; padding: 20px;">
                                                <i class="fas fa-spinner fa-spin"></i> Loading account changes...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div class="modal" id="userModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 700px; width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="userModalTitle">Add User</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="user_id" value="">
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="user_firstname" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="user_lastname" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="user_email" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="user_phone">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Role *</label>
                                    <select class="form-control" id="user_role" required>
                                        <option value="admin">Administrator</option>
                                        <option value="user" selected>User</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <select class="form-control" id="user_status">
                                        <option value="active" selected>Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-check">
                                <input type="checkbox" id="send_welcome_email" checked>
                                <span style="margin-left: 10px;">Send welcome email with login instructions</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline close-modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveUserBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Backdrop -->
    <div class="modal-backdrop" id="modalBackdrop" style="display: none;"></div>

    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        let isEditMode = false;
        let subscriberId = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Get subscriber ID from URL
            const params = new URLSearchParams(window.location.search);
            subscriberId = params.get('id');
            const mode = params.get('mode') || 'view'; // Default to view mode

            if (!subscriberId) {
                showNotification('Subscriber ID not provided', 'error');
                setTimeout(() => {
                    window.location.href = 'subscribers.html';
                }, 2000);
                return;
            }

            // Set initial mode
            setViewEditMode(mode === 'edit');

            // Set up mode toggle buttons
            setupModeToggle();

            // Load subscriber data
            loadSubscriberData(subscriberId);

            // Tab functionality
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');

                    // Load data for specific tabs when they become active
                    if (tabId === 'users' && !document.querySelector('#users table tbody tr[data-loaded="true"]')) {
                        loadSubscriberUsers(subscriberId);
                    } else if (tabId === 'counties') {
                        loadCountiesTab(subscriberId);
                    } else if (tabId === 'incident-activity') {
                        loadIncidentActivity();
                    } else if (tabId === 'account-changes') {
                        loadAccountChanges();
                    }
                });
            });

            // Company Info Form submission
            document.getElementById('companyInfoForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Validate required fields
                const companyName = this.elements.company_name.value.trim();
                if (!companyName) {
                    showNotification('Company name is required', 'error');
                    return;
                }

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                submitBtn.disabled = true;

                // Collect form data
                const formData = new FormData(this);
                const companyData = {
                    name: formData.get('company_name'),
                    type: formData.get('company_type'),
                    phone: formData.get('company_phone'),
                    email: formData.get('company_email'),
                    address: formData.get('company_address'),
                    city: formData.get('company_city'),
                    state: formData.get('company_state'),
                    zip: formData.get('company_zip'),
                    status: formData.get('status'),
                    notes: formData.get('notes')
                };

                // Send to API
                API.companies.update(subscriberId, { company: companyData })
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Company information updated successfully', 'success');
                            // Update title if name changed
                            document.getElementById('companyNameTitle').textContent = companyData.name;
                        } else {
                            showNotification('Error updating information: ' + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating subscriber:', error);
                        showNotification('Error connecting to server', 'error');
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });

            // Subscription Form submission
            document.getElementById('subscriptionForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
                submitBtn.disabled = true;

                // Collect form data
                const formData = new FormData(this);
                const subscriptionData = {
                    plan: formData.get('subscription_plan'),
                    maxUsers: formData.get('max_users'),
                    billingCycle: formData.get('billing_cycle'),
                    billingEmail: formData.get('billing_email')
                };

                // Send to API
                API.companies.updateSubscription(subscriberId, subscriptionData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('Subscription updated successfully', 'success');
                        } else {
                            showNotification('Error updating subscription: ' + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating subscription:', error);
                        showNotification('Error connecting to server', 'error');
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });

            // Counties Form submission (additive behavior - only handles adding new counties)
            const countiesForm = document.getElementById('countiesForm');
            if (countiesForm) {
                countiesForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    submitBtn.disabled = true;

                    const selectedCounties = getSelectedCountiesFromDOM();

                    if (selectedCounties.length === 0) {
                        showNotification('Please select at least one county to add', 'warning');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        return;
                    }

                    // Only handle adding new counties
                    API.companies.addCounties(subscriberId, selectedCounties)
                        .then(response => {
                            if (response && response.success !== false) {
                                showNotification(`Added ${selectedCounties.length} counties`, 'success');
                                // Refresh the current counties display
                                loadCurrentCounties(subscriberId);
                                // Clear selections
                                clearCountySelection();
                            } else {
                                showNotification('Error adding counties: ' + (response?.message || 'Unknown error'), 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error adding counties:', error);
                            showNotification('Error adding counties: ' + (error.response?.data?.msg || error.message || 'Unknown error'), 'error');
                        })
                        .finally(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        });
                });
            }

            // Alert Settings Form submission
            const alertSettingsForm = document.getElementById('alertSettingsForm');
            if (alertSettingsForm) {
                alertSettingsForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    submitBtn.disabled = true;

                    // Collect alert settings data - explicitly check each checkbox
                    const alertSettings = {
                        fireAlerts: this.elements.pref_fire.checked,
                        waterAlerts: this.elements.pref_water.checked,
                        otherAlerts: this.elements.pref_other.checked,
                        emailNotify: this.elements.notify_email.checked,
                        smsNotify: this.elements.notify_sms.checked,
                        appNotify: this.elements.notify_app.checked,
                        frequency: this.elements.notification_frequency.value
                    };

                    // Debug logging
                    console.log('Alert settings being sent:', alertSettings);

                    API.companies.updateAlertSettings(subscriberId, alertSettings)
                        .then(response => {
                            if (response && response.success !== false) {
                                showNotification('Alert settings updated successfully', 'success');
                            } else {
                                showNotification('Error updating alert settings: ' + (response?.message || 'Unknown error'), 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error updating alert settings:', error);
                            showNotification('Error connecting to server', 'error');
                        })
                        .finally(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        });
                });
            }



            // Modal functionality for user management
            const userModal = document.getElementById('userModal');
            const modalBackdrop = document.getElementById('modalBackdrop');
            const closeButtons = document.querySelectorAll('.close-modal');

            // Add User button
            document.getElementById('addUserBtn').addEventListener('click', function() {
                // Reset form and set title for adding new user
                document.getElementById('userModalTitle').textContent = 'Add User';
                document.getElementById('user_id').value = '';
                document.getElementById('userForm').reset();

                // Show modal
                userModal.style.display = 'block';
                modalBackdrop.style.display = 'block';
            });

            // Close modal buttons
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    userModal.style.display = 'none';
                    modalBackdrop.style.display = 'none';
                });
            });

            // Save User button
            document.getElementById('saveUserBtn').addEventListener('click', function() {
                const userId = document.getElementById('user_id').value;
                const isNew = userId === '';

                // Validate required fields
                const firstName = document.getElementById('user_firstname').value.trim();
                const lastName = document.getElementById('user_lastname').value.trim();
                const email = document.getElementById('user_email').value.trim();

                if (!firstName) {
                    showNotification('First name is required', 'error');
                    return;
                }
                if (!lastName) {
                    showNotification('Last name is required', 'error');
                    return;
                }
                if (!email) {
                    showNotification('Email is required', 'error');
                    return;
                }
                if (!email.includes('@')) {
                    showNotification('Please enter a valid email address', 'error');
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                this.disabled = true;
                // Collect form data
                const userData = {
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    phone: document.getElementById('user_phone').value,
                    role: document.getElementById('user_role').value,
                    status: document.getElementById('user_status').value,
                    company_id: subscriberId,  // Use company_id instead of subscriberId
                    sendWelcomeEmail: document.getElementById('send_welcome_email').checked
                };
                const apiPromise = isNew
                    ? API.companies.addUser(userData)
                    : API.companies.updateUser(userId, userData);
                apiPromise
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification(`User ${isNew ? 'added' : 'updated'} successfully!`, 'success');
                            userModal.style.display = 'none';
                            modalBackdrop.style.display = 'none';
                            loadSubscriberUsers(subscriberId);
                        } else {
                            showNotification(`Error ${isNew ? 'adding' : 'updating'} user: ` + (response?.message || 'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error(`Error ${isNew ? 'adding' : 'updating'} user:`, error);
                        showNotification('Error connecting to server', 'error');
                    })
                    .finally(() => {
                        this.innerHTML = 'Save';
                        this.disabled = false;
                    });
            });

            // Attach event handlers to edit/reset/delete user buttons
            // These are added dynamically, so we'll use event delegation
            document.addEventListener('click', function(e) {
                // Edit User handler
                if (e.target.closest('.edit-user')) {
                    const button = e.target.closest('.edit-user');
                    const userId = button.getAttribute('data-id');
                    editUser(userId);
                }

                // Reset Password handler
                if (e.target.closest('.reset-password')) {
                    const button = e.target.closest('.reset-password');
                    const userId = button.getAttribute('data-id');
                    const userName = button.closest('tr').cells[0].textContent;

                    if (confirm(`Are you sure you want to reset the password for ${userName}?`)) {
                        resetUserPassword(userId, button);
                    }
                }

                // Delete User handler
                if (e.target.closest('.delete-user')) {
                    const button = e.target.closest('.delete-user');
                    const userId = button.getAttribute('data-id');
                    const userName = button.closest('tr').cells[0].textContent;

                    if (confirm(`Are you sure you want to delete the user ${userName}? This action cannot be undone.`)) {
                        deleteUser(userId, button);
                    }
                }
            });
        });

        // Function to check if current user is admin
        function isCurrentUserAdmin() {
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || '{}');
            return userInfo.role === 'admin';
        }

        // Function to setup mode toggle functionality
        function setupModeToggle() {
            const editModeBtn = document.getElementById('editModeBtn');
            const viewModeBtn = document.getElementById('viewModeBtn');
            const saveModeBtn = document.getElementById('saveModeBtn');

            editModeBtn.addEventListener('click', function() {
                setViewEditMode(true);
            });

            viewModeBtn.addEventListener('click', function() {
                setViewEditMode(false);
            });

            saveModeBtn.addEventListener('click', function() {
                // Save all forms and switch to view mode
                saveAllChanges();
            });
        }

        // Function to set view/edit mode
        function setViewEditMode(editMode) {
            isEditMode = editMode;
            const container = document.querySelector('.main-container');
            const pageTitle = document.getElementById('page-title');
            const pageIcon = document.getElementById('page-icon');
            const editModeBtn = document.getElementById('editModeBtn');
            const viewModeBtn = document.getElementById('viewModeBtn');
            const saveModeBtn = document.getElementById('saveModeBtn');

            if (editMode) {
                container.classList.remove('read-only-mode');
                container.classList.add('edit-mode');
                pageTitle.textContent = 'Edit Subscriber';
                pageIcon.className = 'fas fa-edit';
                editModeBtn.style.display = 'none';
                viewModeBtn.style.display = 'inline-flex';
                saveModeBtn.style.display = 'inline-flex';

                // Make form fields editable
                document.querySelectorAll('.form-control').forEach(field => {
                    field.removeAttribute('readonly');
                    field.style.pointerEvents = 'auto';
                });

                // Enable checkboxes and form interactions
                document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
                    input.removeAttribute('disabled');
                });

                // Enable county selection elements
                const countySearchInput = document.getElementById('location-search');
                const toggleBrowseBtn = document.getElementById('toggle-browse');

                if (countySearchInput) {
                    countySearchInput.removeAttribute('readonly');
                    countySearchInput.style.pointerEvents = 'auto';
                }

                if (toggleBrowseBtn) {
                    toggleBrowseBtn.style.display = 'inline-block';
                    toggleBrowseBtn.removeAttribute('disabled');
                }

                // Show submit buttons
                document.querySelectorAll('button[type="submit"]').forEach(button => {
                    button.style.display = 'inline-flex';
                });

                // Reload counties to add click handlers for removal
                if (subscriberId && isCurrentUserAdmin()) {
                    loadCurrentCounties(subscriberId);
                }
            } else {
                container.classList.remove('edit-mode');
                container.classList.add('read-only-mode');
                pageTitle.textContent = 'View Subscriber';
                pageIcon.className = 'fas fa-eye';
                editModeBtn.style.display = 'inline-flex';
                viewModeBtn.style.display = 'none';
                saveModeBtn.style.display = 'none';

                // Make form fields read-only
                document.querySelectorAll('.form-control').forEach(field => {
                    field.setAttribute('readonly', 'readonly');
                    field.style.pointerEvents = 'none';
                });

                // Disable checkboxes and form interactions
                document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
                    input.setAttribute('disabled', 'disabled');
                });

                // Disable county selection elements
                const countySearchInput = document.getElementById('location-search');
                const toggleBrowseBtn = document.getElementById('toggle-browse');

                if (countySearchInput) {
                    countySearchInput.setAttribute('readonly', 'readonly');
                    countySearchInput.style.pointerEvents = 'none';
                }

                if (toggleBrowseBtn) {
                    toggleBrowseBtn.style.display = 'none';
                }

                // Disable county checkboxes
                document.querySelectorAll('input[name="counties"]').forEach(checkbox => {
                    checkbox.setAttribute('disabled', 'disabled');
                });

                // Hide submit buttons
                document.querySelectorAll('button[type="submit"]').forEach(button => {
                    button.style.display = 'none';
                });

                // Hide county tag removal buttons
                document.querySelectorAll('.county-tag .remove-tag').forEach(tag => {
                    tag.style.display = 'none';
                });

                // Reload counties to remove click handlers
                if (subscriberId && isCurrentUserAdmin()) {
                    loadCurrentCounties(subscriberId);
                }
            }
        }

        // Function to save all changes
        function saveAllChanges() {
            // Save company info
            const companyForm = document.getElementById('companyInfoForm');
            if (companyForm) {
                companyForm.dispatchEvent(new Event('submit'));
            }

            // Save subscription info
            const subscriptionForm = document.getElementById('subscriptionForm');
            if (subscriptionForm) {
                subscriptionForm.dispatchEvent(new Event('submit'));
            }

            // Save counties if form exists
            const countiesForm = document.getElementById('countiesForm');
            if (countiesForm) {
                countiesForm.dispatchEvent(new Event('submit'));
            }

            // Save alert settings if form exists
            const alertSettingsForm = document.getElementById('alertSettingsForm');
            if (alertSettingsForm) {
                alertSettingsForm.dispatchEvent(new Event('submit'));
            }

            // Switch back to view mode after a short delay
            setTimeout(() => {
                setViewEditMode(false);
            }, 1000);
        }

        // Function to load incident activity
        function loadIncidentActivity() {
            const activityBody = document.getElementById('incidentActivityBody');

            API.companies.getNotifications(subscriberId, { limit: 100 })
                .then(response => {
                    if (response && response.success && response.data) {
                        const notifications = response.data;
                        activityBody.innerHTML = '';

                        if (notifications.length === 0) {
                            activityBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--text-secondary);"><i class="fas fa-bell"></i><br>No incident notifications found</td></tr>';
                            return;
                        }

                        notifications.forEach(notification => {
                            const row = document.createElement('tr');
                            const date = new Date(notification.createdAt).toLocaleString();
                            const incident = notification.incident;
                            const location = incident ? `${incident.address}, ${incident.city}` : 'N/A';
                            const incidentType = incident?.type || 'Unknown';

                            const typeBadge = `<span class="notification-type-badge notification-${notification.type}">${notification.type.toUpperCase()}</span>`;
                            const statusBadge = `<span class="status-badge status-${notification.status}">${notification.status}</span>`;
                            const userName = notification.user?.name || 'N/A';

                            row.innerHTML = `
                                <td>${date}</td>
                                <td>${incidentType}</td>
                                <td>${location}</td>
                                <td>${typeBadge}</td>
                                <td>${statusBadge}</td>
                                <td>${userName}</td>
                            `;
                            activityBody.appendChild(row);
                        });
                    } else {
                        activityBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px;">Error loading incident activity: ' +
                            (response?.message || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading incident activity:', error);
                    activityBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px;">Error connecting to server.</td></tr>';
                });
        }

        // Function to load account changes
        function loadAccountChanges() {
            const changesBody = document.getElementById('accountChangesBody');

            API.companies.getActivity(subscriberId, { limit: 100 })
                .then(response => {
                    if (response && response.success && response.data) {
                        const activities = response.data;
                        changesBody.innerHTML = '';

                        if (activities.length === 0) {
                            changesBody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: var(--text-secondary);"><i class="fas fa-history"></i><br>No account changes found</td></tr>';
                            return;
                        }

                        activities.forEach(activity => {
                            const row = document.createElement('tr');
                            const date = new Date(activity.created).toLocaleString();
                            const severityBadge = `<span class="status-badge status-${activity.severity}">${activity.severity}</span>`;

                            row.innerHTML = `
                                <td>${date}</td>
                                <td>${activity.action}</td>
                                <td>${activity.details || 'N/A'}</td>
                                <td>${activity.userName}</td>
                                <td>${severityBadge}</td>
                            `;
                            changesBody.appendChild(row);
                        });
                    } else {
                        changesBody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px;">Error loading account changes: ' +
                            (response?.message || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading account changes:', error);
                    changesBody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px;">Error connecting to server.</td></tr>';
                });
        }

        // Function to load counties tab with access control
        function loadCountiesTab(subscriberId) {
            const accessDenied = document.getElementById('counties-access-denied');
            const countiesManagement = document.getElementById('counties-management');

            if (!isCurrentUserAdmin()) {
                // Show access denied message
                accessDenied.style.display = 'block';
                countiesManagement.style.display = 'none';
            } else {
                // Show counties management for admin
                accessDenied.style.display = 'none';
                countiesManagement.style.display = 'block';

                // Load current counties and county selector
                loadCurrentCounties(subscriberId);
                loadCountiesDataForEdit([]);
            }
        }

        // Function to load current counties for a company
        function loadCurrentCounties(companyId) {
            const currentCountiesList = document.getElementById('current-counties-list');
            if (!currentCountiesList) return;

            currentCountiesList.innerHTML = 'Loading...';

            API.companies.getCounties(companyId)
                .then(response => {
                    if (response && response.counties) {
                        if (response.counties.length === 0) {
                            currentCountiesList.innerHTML = '<span style="color: var(--text-secondary);">No counties currently subscribed</span>';
                        } else {
                            const countiesHtml = response.counties.map(county =>
                                `<span class="county-tag" data-county-id="${county.id}" style="margin: 2px; display: inline-block; cursor: ${isEditMode ? 'pointer' : 'default'};">
                                    ${county.name}, ${county.stateAbbreviation || county.state}
                                </span>`
                            ).join('');
                            currentCountiesList.innerHTML = countiesHtml;

                            // Debug logging
                            console.log('loadCurrentCounties - isEditMode:', isEditMode, 'isCurrentUserAdmin:', isCurrentUserAdmin());

                            // Add click handlers for immediate county removal (only in edit mode and for admin users)
                            if (isEditMode && isCurrentUserAdmin()) {
                                console.log('Adding click handlers to', response.counties.length, 'county tags');
                                currentCountiesList.querySelectorAll('.county-tag').forEach((tag, index) => {
                                    console.log(`Adding click handler to county tag ${index}:`, tag.textContent.trim());
                                    tag.addEventListener('click', function(event) {
                                        event.preventDefault();
                                        event.stopPropagation();

                                        const countyId = this.dataset.countyId;
                                        const countyName = this.textContent.trim();

                                        console.log('County clicked:', countyName, 'ID:', countyId);

                                        if (confirm(`Remove ${countyName} from subscriptions?`)) {
                                            console.log('User confirmed removal of:', countyName);

                                            // Show loading state
                                            this.style.opacity = '0.5';
                                            this.style.pointerEvents = 'none';

                                            // Immediately remove the county
                                            API.companies.removeCounties(subscriberId, [countyId])
                                                .then(response => {
                                                    console.log('Remove county API response:', response);
                                                    if (response && response.success !== false) {
                                                        showNotification(`Removed ${countyName}`, 'success');
                                                        // Refresh the display
                                                        loadCurrentCounties(companyId);
                                                    } else {
                                                        showNotification('Error removing county: ' + (response?.message || 'Unknown error'), 'error');
                                                        // Restore the tag on error
                                                        this.style.opacity = '1';
                                                        this.style.pointerEvents = 'auto';
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error removing county:', error);
                                                    showNotification('Error removing county: ' + (error.response?.data?.msg || error.message || 'Unknown error'), 'error');
                                                    // Restore the tag on error
                                                    this.style.opacity = '1';
                                                    this.style.pointerEvents = 'auto';
                                                });
                                        } else {
                                            console.log('User cancelled removal of:', countyName);
                                        }
                                    });
                                });
                            } else {
                                console.log('Not adding click handlers - isEditMode:', isEditMode, 'isCurrentUserAdmin:', isCurrentUserAdmin());
                            }
                        }
                    } else {
                        currentCountiesList.innerHTML = '<span style="color: var(--text-secondary);">No counties currently subscribed</span>';
                    }
                })
                .catch(error => {
                    console.error('Error loading current counties:', error);
                    currentCountiesList.innerHTML = '<span style="color: var(--text-danger);">Error loading counties</span>';
                });
        }

        // Function to load subscriber data
        function loadSubscriberData(subscriberId) {
            API.companies.getById(subscriberId)
                .then(response => {
                    if (response && response.success !== false) {
                        const subscriber = response.data || response;
                        populateSubscriberData(subscriber);
                    } else {
                        showNotification('Error loading subscriber: ' + (response?.message || 'Not found'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading subscriber:', error);
                    showNotification('Error connecting to server', 'error');
                });
        }

        // Function to populate subscriber data in the form
        function populateSubscriberData(subscriber) {
            // Set company name in title
            document.getElementById('companyNameTitle').textContent =
                subscriber.company?.name || subscriber.name || `Subscriber #${subscriber.id}`;

            // Show/hide counties tab based on admin status
            const countiesTab = document.getElementById('counties-tab');
            if (countiesTab) {
                if (isCurrentUserAdmin()) {
                    countiesTab.style.display = 'block';
                } else {
                    countiesTab.style.display = 'none';
                }
            }

            // Populate company info form
            const companyForm = document.getElementById('companyInfoForm');
            if (companyForm && subscriber.company) {
                companyForm.elements.company_name.value = subscriber.company.name || '';
                companyForm.elements.company_type.value = (subscriber.company.companyType?.id || subscriber.company.companyTypeId || '');
                companyForm.elements.company_phone.value = subscriber.company.phone || '';
                companyForm.elements.company_email.value = subscriber.company.email || '';
                companyForm.elements.company_address.value = subscriber.company.address || '';
                companyForm.elements.company_city.value = subscriber.company.city || '';
                companyForm.elements.company_state.value = subscriber.company.state || '';
                companyForm.elements.company_zip.value = subscriber.company.zip || '';
                companyForm.elements.notes.value = subscriber.company.notes || '';

                // Fix: Map boolean status to string for select field
                let statusValue = subscriber.company.status;
                if (typeof statusValue === 'boolean') {
                    statusValue = statusValue ? 'active' : 'blocked';
                }
                companyForm.elements.status.value = statusValue || 'active';
            }

            // Populate subscription data
            const subscriptionForm = document.getElementById('subscriptionForm');
            if (subscriptionForm && subscriber.subscription) {
                subscriptionForm.elements.subscription_plan.value = subscriber.subscription.plan || 'standard';
                subscriptionForm.elements.max_users.value = subscriber.subscription.maxUsers || '5';
                subscriptionForm.elements.billing_cycle.value = subscriber.subscription.billingCycle || 'monthly';
                subscriptionForm.elements.billing_email.value = subscriber.subscription.billingEmail || subscriber.company?.email || '';

                // Update subscription overview
                updateSubscriptionOverview(subscriber.subscription);
            }

            // Populate alert settings
            const alertSettingsForm = document.getElementById('alertSettingsForm');
            if (alertSettingsForm && subscriber.subscriberPreference) {
                // Set checkboxes based on subscriber preferences
                alertSettingsForm.elements.pref_fire.checked = subscriber.subscriberPreference.fireAlerts !== false;
                alertSettingsForm.elements.pref_water.checked = subscriber.subscriberPreference.waterAlerts !== false;
                alertSettingsForm.elements.pref_other.checked = subscriber.subscriberPreference.otherAlerts !== false;
                alertSettingsForm.elements.notify_email.checked = subscriber.subscriberPreference.emailNotify !== false;
                alertSettingsForm.elements.notify_sms.checked = subscriber.subscriberPreference.smsNotify === true;
                alertSettingsForm.elements.notify_app.checked = subscriber.subscriberPreference.appNotify !== false;

                // Set frequency
                if (subscriber.subscriberPreference.frequency) {
                    alertSettingsForm.elements.notification_frequency.value = subscriber.subscriberPreference.frequency;
                } else {
                    alertSettingsForm.elements.notification_frequency.value = 'immediate';
                }
            }
        }

        // Function to update the subscription overview display
        function updateSubscriptionOverview(subscription) {
            const overviewElement = document.querySelector('.subscription-overview');
            if (!overviewElement || !subscription) return;

            const planElement = overviewElement.querySelector('h4 strong');
            if (planElement) {
                planElement.textContent = subscription.plan ?
                    subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1) : 'Standard';
            }

            const statusBadge = overviewElement.querySelector('.status-badge');
            if (statusBadge) {
                const status = subscription.status || 'active';
                statusBadge.className = `status-badge status-${status.toLowerCase()}`;
                statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }

            const billingCycle = overviewElement.querySelectorAll('div:nth-child(2) div strong')[0];
            if (billingCycle) {
                billingCycle.textContent = (subscription.billingCycle || 'Monthly')
                    .charAt(0).toUpperCase() + (subscription.billingCycle || 'monthly').slice(1);
            }

            const nextBillingDate = overviewElement.querySelectorAll('div:nth-child(2) div strong')[1];
            if (nextBillingDate && subscription.nextBillingDate) {
                const date = new Date(subscription.nextBillingDate);
                nextBillingDate.textContent = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }

            const usersCount = overviewElement.querySelectorAll('div:nth-child(2) div strong')[2];
            if (usersCount) {
                usersCount.textContent = `${subscription.currentUsers || '0'} of ${subscription.maxUsers || '∞'}`;
            }
        }

        // Function to load subscriber users
        function loadSubscriberUsers(subscriberId) {
            const usersTableBody = document.querySelector('#users table tbody');
            if (!usersTableBody) return;

            // Show loading
            usersTableBody.innerHTML = '<tr><td colspan="6"><i class="fas fa-spinner fa-spin"></i> Loading users...</td></tr>';

            // Debug: Check if API.companies.getUsers exists
            if (!API || !API.companies || typeof API.companies.getUsers !== 'function') {
                console.error('API.companies.getUsers is not a function', API && API.companies);
                usersTableBody.innerHTML = '<tr><td colspan="6" style="color:red;">API.companies.getUsers is not a function</td></tr>';
                return;
            }

            API.companies.getUsers(subscriberId)
                .then(response => {
                    if (response && response.success !== false) {
                        const users = response.data || response || [];

                        if (users.length === 0) {
                            usersTableBody.innerHTML = '<tr><td colspan="6">No users found for this company.</td></tr>';
                            return;
                        }

                        // Clear loading message and add users
                        usersTableBody.innerHTML = '';
                        users.forEach(user => {
                            const statusClass = user.status === 'active' ? 'status-active' :
                                user.status === 'inactive' ? 'status-blocked' : 'status-pending';

                            const row = document.createElement('tr');
                            row.dataset.loaded = 'true';
                            row.innerHTML = `
                                <td>${user.firstName || ''} ${user.lastName || ''}</td>
                                <td>${user.email || 'N/A'}</td>
                                <td>${user.role || 'User'}</td>
                                <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}</td>
                                <td><span class="status-badge ${statusClass}">${user.status || 'Unknown'}</span></td>
                                <td>
                                    <div class="incident-actions">
                                        <button class="btn-icon edit-user" data-id="${user.id}" data-tooltip="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon reset-password" data-id="${user.id}" data-tooltip="Reset Password">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn-icon delete-user" data-id="${user.id}" data-tooltip="Delete">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            `;
                            usersTableBody.appendChild(row);
                        });
                    } else {
                        usersTableBody.innerHTML = '<tr><td colspan="6">Error loading users: ' +
                            (response?.message || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    usersTableBody.innerHTML = '<tr><td colspan="6">Error connecting to server.</td></tr>';
                });
        }

        // Function to load subscriber activity log
        function loadSubscriberActivity(subscriberId) {
            const activityTableBody = document.querySelector('#activity table tbody');
            if (!activityTableBody) return;

            // Show loading
            activityTableBody.innerHTML = '<tr><td colspan="4"><i class="fas fa-spinner fa-spin"></i> Loading activity...</td></tr>';

            API.companies.getActivity(subscriberId)
                .then(response => {
                    if (response && response.success !== false) {
                        const activities = response.data || response || [];

                        if (activities.length === 0) {
                            activityTableBody.innerHTML = '<tr><td colspan="4">No activity found for this company.</td></tr>';
                            return;
                        }

                        // Clear loading message and add activities
                        activityTableBody.innerHTML = '';
                        activities.forEach(activity => {
                            const row = document.createElement('tr');
                            row.dataset.loaded = 'true';
                            row.innerHTML = `
                                <td>${activity.created ? new Date(activity.created).toLocaleString() : 'Unknown'}</td>
                                <td>${activity.userName || 'System'}</td>
                                <td>${activity.action || 'Unknown'}</td>
                                <td>${activity.details || ''}</td>
                            `;
                            activityTableBody.appendChild(row);
                        });
                    } else {
                        activityTableBody.innerHTML = '<tr><td colspan="4">Error loading activity: ' +
                            (response?.message || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading activity:', error);
                    activityTableBody.innerHTML = '<tr><td colspan="4">Error connecting to server.</td></tr>';
                });
        }

        // Function to edit a user
        function editUser(userId) {
            // Clear form and add loading indicator to the modal title
            const userModalTitle = document.getElementById('userModalTitle');
            const originalTitle = userModalTitle.textContent;
            userModalTitle.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading User...';

            // Show modal in loading state
            document.getElementById('userModal').style.display = 'block';
            document.getElementById('modalBackdrop').style.display = 'block';

            API.companies.getUserById(userId)
                .then(response => {
                    let user = response && response.data ? response.data : response;
                    if (user && user.id) {
                        // Set modal title for editing
                        userModalTitle.textContent = 'Edit User';
                        document.getElementById('user_id').value = userId;
                        // Populate form fields safely
                        document.getElementById('user_firstname').value = user.firstName || '';
                        document.getElementById('user_lastname').value = user.lastName || '';
                        document.getElementById('user_email').value = user.email || '';
                        document.getElementById('user_phone').value = user.phone || '';
                        document.getElementById('user_role').value = user.role || 'user';
                        // Map boolean status to string for dropdown
                        let statusValue = user.status;
                        if (statusValue === true) statusValue = 'active';
                        else if (statusValue === false) statusValue = 'inactive';
                        document.getElementById('user_status').value = statusValue || 'active';
                        document.getElementById('send_welcome_email').checked = false;
                    } else {
                        userModalTitle.textContent = originalTitle;
                        showNotification('Error loading user: ' + (response?.message || 'User not found'), 'error');
                        document.getElementById('userModal').style.display = 'none';
                        document.getElementById('modalBackdrop').style.display = 'none';
                    }
                })
                .catch(error => {
                    userModalTitle.textContent = originalTitle;
                    console.error('Error loading user:', error);
                    showNotification('Error connecting to server', 'error');
                    document.getElementById('userModal').style.display = 'none';
                    document.getElementById('modalBackdrop').style.display = 'none';
                });
        }

        // Function to reset user password
        function resetUserPassword(userId, buttonElement) {
            if (!userId || !buttonElement) return;

            // Show loading state
            const originalHTML = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            buttonElement.disabled = true;

            API.companies.resetUserPassword(userId)
                .then(response => {
                    if (response && response.success !== false) {
                        showNotification('Password reset email sent!', 'success');
                    } else {
                        showNotification('Error resetting password: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error resetting password:', error);
                    showNotification('Error connecting to server', 'error');
                })
                .finally(() => {
                    // Reset button state
                    buttonElement.innerHTML = originalHTML;
                    buttonElement.disabled = false;
                });
        }

        // Function to delete a user
        function deleteUser(userId, buttonElement) {
            if (!userId || !buttonElement) return;

            // Show loading state
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            buttonElement.disabled = true;

            API.companies.deleteUser(userId)
                .then(response => {
                    if (response && response.success !== false) {
                        showNotification('User deleted successfully!', 'success');
                        // Remove row from table
                        buttonElement.closest('tr').remove();
                    } else {
                        showNotification('Error deleting user: ' + (response?.message || 'Unknown error'), 'error');
                        // Reset button state
                        buttonElement.innerHTML = '<i class="fas fa-trash-alt"></i>';
                        buttonElement.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error deleting user:', error);
                    showNotification('Error connecting to server', 'error');
                    // Reset button state
                    buttonElement.innerHTML = '<i class="fas fa-trash-alt"></i>';
                    buttonElement.disabled = false;
                });
        }

        // --- County/State Loading for Alert Settings ---
        function loadCountiesDataForEdit(selectedCounties = []) {
            const treeBrowser = document.getElementById('tree-browser');
            if (!treeBrowser) return;

            treeBrowser.innerHTML = '<div class="loading-message">Loading states and counties...</div>';

            // Clear any previous search results
            const searchResults = document.getElementById('search-results');
            if (searchResults) {
                searchResults.innerHTML = '';
                searchResults.style.display = 'none';
            }

            // Initialize selected counties display
            updateSelectedCountiesDisplay(selectedCounties);

            // Fetch states
            apiRequest('/locations/states')
                .then(states => {
                    if (!states || !states.length) throw new Error('No states found');
                    // Fetch counties for each state
                    return Promise.all(states.map(state =>
                        apiRequest(`/locations/counties?stateId=${state.id}`)
                            .then(counties => ({ state, counties: counties || [] }))
                            .catch(() => ({ state, counties: [] }))
                    ));
                })
                .then(statesWithCounties => {
                    // Filter out states with no counties
                    const valid = statesWithCounties.filter(item => item.counties.length > 0);
                    if (!valid.length) {
                        treeBrowser.innerHTML = '<div class="loading-message">No counties found.</div>';
                        return;
                    }

                    // Build tree view HTML
                    let html = '';
                    valid.forEach(item => {
                        // Create state node
                        html += `
                            <div class="tree-node tree-state collapsed" data-state-id="${item.state.id}" onclick="toggleStateNode(event, ${item.state.id})">
                                <i class="fas fa-chevron-down expand-icon"></i>
                                ${item.state.name}
                                <input type="checkbox" class="state-checkbox"
                                    data-state-id="${item.state.id}"
                                    onclick="event.stopPropagation(); toggleAllCounties(${item.state.id}, this.checked)">
                            </div>
                            <div class="tree-counties" id="counties-${item.state.id}" style="display: none;">`;

                        // Add county nodes
                        item.counties.forEach(county => {
                            const isChecked = selectedCounties.includes(String(county.id)) ? 'checked' : '';
                            html += `
                                <div class="tree-node tree-county">
                                    <input type="checkbox" id="county-${county.id}"
                                        name="counties"
                                        value="${county.id}"
                                        data-state-id="${item.state.id}"
                                        data-county-name="${county.name}"
                                        data-state-name="${item.state.name}"
                                        ${isChecked}
                                        onchange="updateStateCheckbox(${item.state.id}); updateSelectedCounty(this);">
                                    <label for="county-${county.id}">${county.name}</label>
                                </div>`;
                        });

                        html += '</div>';
                    });

                    treeBrowser.innerHTML = html;

                    // After building the tree, update the selected counties display and state checkboxes
                    if (selectedCounties.length > 0) {
                        // Update the selected counties display
                        updateSelectedCountiesDisplay(selectedCounties);

                        // Update state checkboxes for all states that have selected counties
                        const selectedStates = new Set();
                        selectedCounties.forEach(countyId => {
                            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
                            if (checkbox) {
                                selectedStates.add(checkbox.dataset.stateId);
                            }
                        });

                        selectedStates.forEach(stateId => {
                            updateStateCheckbox(stateId);
                        });
                    }

                    // Setup search functionality
                    setupCountySearch(valid);

                    // Setup toggle browse functionality
                    const toggleBrowse = document.getElementById('toggle-browse');
                    if (toggleBrowse) {
                        toggleBrowse.addEventListener('click', function() {
                            const treeSelector = document.getElementById('tree-browser');
                            if (treeSelector) {
                                const isVisible = treeSelector.style.display !== 'none';
                                treeSelector.style.display = isVisible ? 'none' : 'block';
                                this.classList.toggle('expanded', !isVisible);
                                this.innerHTML = isVisible ?
                                    '<i class="fas fa-chevron-down"></i> Show State Tree' :
                                    '<i class="fas fa-chevron-up"></i> Hide State Tree';
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading counties:', error);
                    treeBrowser.innerHTML = '<div class="loading-message">Error loading counties. Please try again.</div>';
                });
        }

        // Toggle state node expand/collapse
        window.toggleStateNode = function(event, stateId) {
            const stateElement = event.currentTarget;
            const countiesElement = document.getElementById(`counties-${stateId}`);

            if (!countiesElement) return;

            stateElement.classList.toggle('collapsed');
            countiesElement.style.display = stateElement.classList.contains('collapsed') ? 'none' : 'block';
        }

        // Toggle all counties in a state
        window.toggleAllCounties = function(stateId, checked) {
            const countyCheckboxes = document.querySelectorAll(`input[name="counties"][data-state-id="${stateId}"]`);

            countyCheckboxes.forEach(checkbox => {
                if (checkbox.checked !== checked) {
                    checkbox.checked = checked;
                    updateSelectedCounty(checkbox);
                }
            });
        }

        // Update state checkbox based on county selections
        window.updateStateCheckbox = function(stateId) {
            const counties = document.querySelectorAll(`input[name="counties"][data-state-id="${stateId}"]`);
            const stateCheckbox = document.querySelector(`.tree-state[data-state-id="${stateId}"] input[type="checkbox"]`);

            if (!stateCheckbox || counties.length === 0) return;

            const checkedCount = Array.from(counties).filter(cb => cb.checked).length;

            if (checkedCount === 0) {
                stateCheckbox.checked = false;
                stateCheckbox.indeterminate = false;
            } else if (checkedCount === counties.length) {
                stateCheckbox.checked = true;
                stateCheckbox.indeterminate = false;
            } else {
                stateCheckbox.checked = false;
                stateCheckbox.indeterminate = true;
            }
        }

        // Update selected counties display
        window.updateSelectedCountiesDisplay = function(selectedCounties = []) {
            const tagsContainer = document.getElementById('county-tags-display');
            if (!tagsContainer) return;

            if (!Array.isArray(selectedCounties)) {
                selectedCounties = [];
            }

            if (selectedCounties.length === 0) {
                tagsContainer.innerHTML = 'None selected';
                return;
            }

            // Get county names from checkboxes
            let html = '';
            selectedCounties.forEach(countyId => {
                const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
                if (checkbox) {
                    const countyName = checkbox.dataset.countyName || 'Unknown County';
                    const stateName = checkbox.dataset.stateName || '';

                    html += `
                        <span class="county-tag" data-id="${countyId}">
                            ${countyName}${stateName ? `, ${stateName}` : ''}
                            <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                        </span>`;
                }
            });

            tagsContainer.innerHTML = html;
        }

        // Update the selected counties when a checkbox changes
        window.updateSelectedCounty = function(checkbox) {
            const countyId = checkbox.value;
            const countyName = checkbox.dataset.countyName || 'Unknown County';
            const stateName = checkbox.dataset.stateName || '';
            const tagsContainer = document.getElementById('county-tags-display');

            if (!tagsContainer) return;

            if (checkbox.checked) {
                // Add county
                if (tagsContainer.innerHTML === 'None selected') {
                    tagsContainer.innerHTML = '';
                }

                // Skip if already exists
                if (document.querySelector(`.county-tag[data-id="${countyId}"]`)) {
                    return;
                }

                const tag = document.createElement('span');
                tag.className = 'county-tag';
                tag.dataset.id = countyId;
                tag.innerHTML = `
                    ${countyName}${stateName ? `, ${stateName}` : ''}
                    <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                `;

                tagsContainer.appendChild(tag);
            } else {
                // Remove county
                const existingTag = document.querySelector(`.county-tag[data-id="${countyId}"]`);
                if (existingTag) {
                    existingTag.remove();
                }

                if (tagsContainer.children.length === 0) {
                    tagsContainer.innerHTML = 'None selected';
                }
            }
        }

        // Remove a county from the selected display
        window.removeSelectedCounty = function(countyId) {
            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);
            if (checkbox) {
                checkbox.checked = false;
                updateStateCheckbox(checkbox.dataset.stateId);
            }

            const tag = document.querySelector(`.county-tag[data-id="${countyId}"]`);
            if (tag) {
                tag.remove();
            }

            const tagsContainer = document.getElementById('county-tags-display');
            if (tagsContainer && tagsContainer.children.length === 0) {
                tagsContainer.innerHTML = 'None selected';
            }
        }

        // Setup county search functionality
        function setupCountySearch(statesWithCounties) {
            const searchInput = document.getElementById('location-search');
            const searchResults = document.getElementById('search-results');

            if (!searchInput || !searchResults) return;

            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.toLowerCase().trim();

                if (query.length < 2) {
                    searchResults.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    // Search through all counties
                    const results = [];

                    statesWithCounties.forEach(item => {
                        item.counties.forEach(county => {
                            if (county.name.toLowerCase().includes(query) ||
                                item.state.name.toLowerCase().includes(query)) {
                                results.push({
                                    id: county.id,
                                    county: county.name,
                                    state: item.state.name,
                                    stateId: item.state.id
                                });
                            }
                        });
                    });

                    // Display results
                    if (results.length === 0) {
                        searchResults.innerHTML = '<div class="search-result">No results found</div>';
                    } else {
                        searchResults.innerHTML = results.map(result => `
                            <div class="search-result" onclick="selectSearchResult('${result.id}', '${result.stateId}', '${result.county.replace(/'/g, "\\'")}', '${result.state.replace(/'/g, "\\'")}')">
                                <div class="result-county">${result.county}</div>
                                <div class="result-state">${result.state}</div>
                            </div>
                        `).join('');
                    }

                    searchResults.style.display = 'block';
                }, 300);
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-first')) {
                    searchResults.style.display = 'none';
                }
            });
        }

        // Select a county from search results
        window.selectSearchResult = function(countyId, stateId, countyName, stateName) {
            const checkbox = document.querySelector(`input[name="counties"][value="${countyId}"]`);

            if (checkbox) {
                // County exists in the tree - check it
                checkbox.checked = true;
                updateStateCheckbox(stateId);
                updateSelectedCounty(checkbox);

                // Expand the state node to show the selection
                const stateNode = document.querySelector(`.tree-state[data-state-id="${stateId}"]`);
                const countiesNode = document.getElementById(`counties-${stateId}`);

                if (stateNode && countiesNode) {
                    stateNode.classList.remove('collapsed');
                    countiesNode.style.display = 'block';
                }

                // Scroll to the selected county
                const treeBrowser = document.getElementById('tree-browser');
                if (treeBrowser && checkbox.parentElement) {
                    treeBrowser.scrollTop = checkbox.parentElement.offsetTop - 100;
                }
            } else {
                // County doesn't exist in the tree - create a custom tag
                const tagsContainer = document.getElementById('county-tags-display');

                if (tagsContainer) {
                    if (tagsContainer.innerHTML === 'None selected') {
                        tagsContainer.innerHTML = '';
                    }

                    // Skip if already exists
                    if (document.querySelector(`.county-tag[data-id="${countyId}"]`)) {
                        return;
                    }

                    const tag = document.createElement('span');
                    tag.className = 'county-tag';
                    tag.dataset.id = countyId;
                    tag.innerHTML = `
                        ${countyName}${stateName ? `, ${stateName}` : ''}
                        <span class="remove" onclick="removeSelectedCounty('${countyId}')">&times;</span>
                    `;

                    tagsContainer.appendChild(tag);
                }
            }

            // Clear search input and hide results
            const searchInput = document.getElementById('location-search');
            const searchResults = document.getElementById('search-results');

            if (searchInput) searchInput.value = '';
            if (searchResults) searchResults.style.display = 'none';
        }

        // Toggle tree browser visibility
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.getElementById('toggle-browse');
            const treeBrowser = document.getElementById('tree-browser');

            if (toggleButton && treeBrowser) {
                toggleButton.addEventListener('click', function() {
                    const isExpanded = treeBrowser.style.display === 'block';
                    treeBrowser.style.display = isExpanded ? 'none' : 'block';

                    toggleButton.classList.toggle('expanded', !isExpanded);
                    toggleButton.innerHTML = isExpanded ?
                        '<i class="fas fa-chevron-down"></i> Show State Tree' :
                        '<i class="fas fa-chevron-up"></i> Hide State Tree';
                });
            }
        });

        // Patch: Convert selected counties for form submission
        window.getSelectedCountiesFromDOM = function() {
            // Get all selected counties from checkboxes
            const selectedCheckboxes = document.querySelectorAll('input[name="counties"]:checked');
            const checkboxValues = Array.from(selectedCheckboxes).map(cb => cb.value);

            // Also get any custom tags (counties that might not be in the tree but were added via search)
            const customTags = document.querySelectorAll('.county-tag');
            const tagValues = Array.from(customTags).map(tag => tag.dataset.id);

            // Combine and remove duplicates
            const allCounties = [...new Set([...checkboxValues, ...tagValues])];
            return allCounties;
        }

        // Function to clear county selection
        function clearCountySelection() {
            // Uncheck all county checkboxes
            document.querySelectorAll('input[name="counties"]:checked').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Remove all custom county tags
            document.querySelectorAll('.county-tag').forEach(tag => {
                if (tag.parentNode) {
                    tag.parentNode.removeChild(tag);
                }
            });

            // Update the selected counties display
            updateSelectedCountiesDisplay([]);

            // Clear search input
            const searchInput = document.getElementById('location-search');
            if (searchInput) {
                searchInput.value = '';
            }

            // Hide search results
            const searchResults = document.getElementById('search-results');
            if (searchResults) {
                searchResults.innerHTML = '';
                searchResults.style.display = 'none';
            }
        }



        // Get selected counties from preferences
        function getSelectedCountiesFromPreferences(preferences) {
            if (!preferences || !preferences.counties) return [];
            if (Array.isArray(preferences.counties)) return preferences.counties.map(String);
            if (typeof preferences.counties === 'string') return preferences.counties.split(',').map(s => s.trim()).filter(Boolean);
            return [];
        }

        // After populateSubscriberData, call loadCountiesDataForEdit with selected counties
        const originalPopulateSubscriberData = populateSubscriberData;
        populateSubscriberData = function(subscriber) {
            // Call original
            originalPopulateSubscriberData(subscriber);
            // Load counties for alert settings
            const selected = getSelectedCountiesFromPreferences(subscriber.preferences);
            loadCountiesDataForEdit(selected);
        };

        // --- Dynamically populate role and status dropdowns for user modal ---
        const userRoleSelect = document.getElementById('user_role');
        const userStatusSelect = document.getElementById('user_status');
        // Define allowed roles for company_admin and below
        const allowedRoles = [
            { value: 'company_admin', label: 'Company Admin' },
            { value: 'user', label: 'User' },
            { value: 'viewer', label: 'Viewer' }
        ];
        // Define allowed statuses
        const allowedStatuses = [
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'pending', label: 'Pending' }
        ];
        // Populate roles
        if (userRoleSelect) {
            userRoleSelect.innerHTML = '';
            allowedRoles.forEach(role => {
                const opt = document.createElement('option');
                opt.value = role.value;
                opt.textContent = role.label;
                userRoleSelect.appendChild(opt);
            });
        }
        // Populate statuses
        if (userStatusSelect) {
            userStatusSelect.innerHTML = '';
            allowedStatuses.forEach(status => {
                const opt = document.createElement('option');
                opt.value = status.value;
                opt.textContent = status.label;
                userStatusSelect.appendChild(opt);
            });
        }
    </script>
    <style>
        /* Tabs styling */
        .tabs {
            margin-bottom: 20px;
        }

        .tab-list {
            display: flex;
            overflow-x: auto;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            position: relative;
        }

        .tab-button:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: var(--text-light);
        }

        .tab-button.active:after {
            background-color: var(--accent-blue);
        }

        .tab-button:hover {
            color: var(--text-light);
        }

        .tab-button:hover:after {
            background-color: var(--accent-blue);
            opacity: 0.5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1050;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-dialog {
            width: 100%;
            max-width: 500px;
            margin: 20px;
            position: relative;
            z-index: 1051;
        }

        .modal-content {
            background-color: var(--secondary-dark);
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
        }

        .modal-body {
            padding: 20px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close-modal {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 0;
            font-size: 16px;
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1049;
        }

        /* User avatar icon styling */
        .user-avatar-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: #e0e0e0;
            color: #616161;
            border-radius: 50%;
            margin-right: 8px;
        }

        /* County selector styles */
        .county-search-container {
            position: relative;
            margin-bottom: 10px;
        }

        #county-search {
            padding-right: 40px;
        }

        .county-search-container::after {
            content: '\f002';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .checkbox-select {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--secondary-dark);
            padding: 10px;
            position: relative;
        }

        .checkbox-select .loading-message {
            text-align: center;
            padding: 10px;
            color: var(--text-secondary);
        }

        .checkbox-select label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            cursor: pointer;
        }

        .checkbox-select input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        /* Improved County Selector Styles */
        .county-selector-container {
            background-color: rgba(39, 46, 72, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }

        /* Search-first Interface Styles */
        .search-first {
            position: relative;
            margin-bottom: 15px;
        }

        .location-search {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
            color: var(--text-light);
            font-size: 16px;
        }

        .location-search:focus {
            border-color: var(--accent-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            pointer-events: none;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: var(--secondary-dark);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 250px;
            overflow-y: auto;
            z-index: 10;
            display: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .search-result {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.2s;
        }

        .search-result:hover {
            background-color: var(--hover-bg);
        }

        .search-result:last-child {
            border-bottom: none;
        }

        .result-county {
            font-weight: 500;
            color: var(--text-light);
        }

        .result-state {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        /* Tree View Styles */
        .tree-fallback {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 15px;
        }

        .fallback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .toggle-browse {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .toggle-browse:hover {
            background-color: var(--hover-bg);
            color: var(--text-light);
        }

        .toggle-browse i {
            transition: transform 0.2s;
        }

        .toggle-browse.expanded i {
            transform: rotate(180deg);
        }

        .tree-selector {
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--primary-dark);
        }

        .tree-node {
            padding: 10px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: none;
        }

        .tree-node:hover {
            background-color: var(--hover-bg);
        }

        .tree-state {
            font-weight: 500;
            background-color: rgba(30, 136, 229, 0.1);
            border-left: 3px solid var(--accent-blue);
            display: flex;
            align-items: center;
        }

        .tree-state .expand-icon {
            margin-right: 8px;
            transition: transform 0.2s;
            font-size: 12px;
        }

        .tree-state.collapsed .expand-icon {
            transform: rotate(-90deg);
        }

        .tree-state .state-checkbox {
            margin-right: 8px;
            margin-left: auto;
        }

        .tree-counties {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .tree-state.collapsed + .tree-counties {
            display: none;
        }

        .tree-county {
            padding-left: 35px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .tree-county input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Selected Counties Display */
        .selected-counties {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(30, 136, 229, 0.1);
            border-radius: 4px;
            border: 1px solid var(--accent-blue);
        }

        .county-tag {
            display: inline-block;
            background-color: var(--accent-blue);
            color: white;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 12px;
            position: relative;
        }

        .county-tag .remove {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
            font-weight: bold;
        }

        .county-tag .remove:hover {
            opacity: 1;
            color: #ffcccc;
        }

        /* Loading states */
        .loading-message {
            padding: 20px;
            text-align: center;
            color: var(--text-secondary);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .county-selector-container {
                padding: 15px;
            }

            .location-search {
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .search-results {
                max-height: 200px;
            }

            .tree-selector {
                max-height: 250px;
            }
        }
    </style>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('subscribers');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Add any additional subscriber-details specific initialization here
        });
    </script>
</body>
</html>
