# FireAlerts911 Authentication System

## Overview

The FireAlerts911 application now has a comprehensive authentication system that ensures users who aren't logged in cannot access protected pages and are automatically redirected to the login screen.

## Authentication Components

### 1. Client-Side Authentication (`js/auth-check.js`)

**Features:**
- ✅ **Immediate Redirect**: Unauthenticated users are redirected before page content loads
- ✅ **Token Validation**: Checks JWT token expiration and format
- ✅ **Server Validation**: Validates authentication with server periodically
- ✅ **Session Management**: Handles both localStorage and sessionStorage tokens
- ✅ **Automatic Logout**: Clears expired or invalid tokens
- ✅ **Logout Handling**: Proper logout with server-side session clearing

**Public Pages (No Authentication Required):**
- `login.html`
- `forgot-password.html`
- `signup.html`

**Protected Pages (Authentication Required):**
- `index.html` (Dashboard)
- `incidents.html`
- `map.html`
- `subscribers.html`
- `admin-panel.html`
- `account.html`
- `add-fire-incident.html`
- `add-water-incident.html`
- `add-subscriber.html`
- All other pages not in the public list

### 2. Server-Side Authentication (`middleware/page-auth.js`)

**Features:**
- ✅ **JWT Cookie Validation**: Validates authentication cookies for HTML pages
- ✅ **Automatic Redirects**: Server-side redirects for unauthenticated requests
- ✅ **Asset Protection**: Allows access to CSS, JS, and image files
- ✅ **API Route Handling**: Separate handling for API vs page requests

### 3. Enhanced Security Features

**Token Management:**
- ✅ **Expiration Checking**: Automatic token expiration validation
- ✅ **Format Validation**: Validates JWT token structure
- ✅ **Automatic Cleanup**: Removes invalid/expired tokens

**Session Monitoring:**
- ✅ **Periodic Validation**: Validates auth every 5 minutes
- ✅ **Tab Focus Validation**: Re-validates when user returns to tab
- ✅ **Server Sync**: Regular server-side authentication checks

**User Experience:**
- ✅ **Redirect Preservation**: Remembers intended destination after login
- ✅ **Logout Confirmation**: Confirms logout actions
- ✅ **Loading States**: Shows authentication status during checks

## Implementation Details

### Pages with Authentication Script

All protected pages now include the authentication script:

```html
<!-- Load authentication check script -->
<script src="js/auth-check.js"></script>
```

**Updated Pages:**
- ✅ `index.html`
- ✅ `incidents.html`
- ✅ `map.html`
- ✅ `subscribers.html`
- ✅ `admin-panel.html`
- ✅ `account.html`
- ✅ `add-fire-incident.html`
- ✅ `add-water-incident.html`
- ✅ `add-subscriber.html`

### Authentication Flow

1. **Page Load**: `auth-check.js` executes immediately
2. **Token Check**: Validates stored authentication tokens
3. **Redirect**: Unauthenticated users redirected to `login.html`
4. **Server Validation**: Authenticated users validated with server
5. **Periodic Checks**: Authentication re-validated every 5 minutes
6. **Tab Focus**: Re-validation when user returns to tab

### Login Process

1. **User Login**: Credentials submitted to `/api/auth/login`
2. **Token Storage**: JWT token stored in localStorage/sessionStorage
3. **User Info**: User details and role stored for UI customization
4. **Redirect**: User redirected to intended page or dashboard

### Logout Process

1. **Logout Trigger**: User clicks logout or session expires
2. **Server Logout**: Call to `/api/auth/logout` to clear server session
3. **Local Cleanup**: Clear all stored tokens and user data
4. **Redirect**: Redirect to login page with appropriate reason

## Testing

### Test Page: `test-auth.html`

A comprehensive test page is available to verify authentication functionality:

**Features:**
- ✅ **Auth Status Display**: Shows current authentication state
- ✅ **Token Information**: Displays token details and user info
- ✅ **Test Functions**: Buttons to test various auth scenarios
- ✅ **Protected Links**: Links to test access to protected pages
- ✅ **Clear Auth Data**: Function to test unauthenticated state

**Test Scenarios:**
1. **Authenticated Access**: Verify protected pages are accessible
2. **Unauthenticated Access**: Verify redirect to login page
3. **Token Expiration**: Test expired token handling
4. **Server Validation**: Test server-side authentication checks
5. **Logout Process**: Test proper logout functionality

## Security Considerations

### Client-Side Security
- ✅ **UX Enhancement Only**: Client-side checks are for user experience
- ✅ **Server Validation**: All security enforced server-side
- ✅ **Token Expiration**: Automatic handling of expired tokens
- ✅ **Invalid Token Cleanup**: Removes malformed or invalid tokens

### Server-Side Security
- ✅ **JWT Validation**: Proper JWT signature and expiration checking
- ✅ **HTTP-Only Cookies**: Secure cookie-based authentication
- ✅ **Route Protection**: All protected routes require valid authentication
- ✅ **Session Management**: Proper session creation and cleanup

## Configuration

### Public Pages
To add a new public page (no authentication required), add it to the `publicPages` array in `js/auth-check.js`:

```javascript
const publicPages = [
    'login.html',
    'forgot-password.html',
    'signup.html',
    'new-public-page.html'  // Add new public pages here
];
```

### Protected Pages
All pages not in the `publicPages` array are automatically protected. New pages should include the authentication script:

```html
<script src="js/auth-check.js"></script>
```

## Status: ✅ PRODUCTION READY

The authentication system is fully implemented and production-ready with:
- ✅ **Complete Coverage**: All pages properly protected
- ✅ **Robust Validation**: Client and server-side validation
- ✅ **User Experience**: Smooth redirects and proper feedback
- ✅ **Security**: Proper token handling and session management
- ✅ **Testing**: Comprehensive test page for verification

Users who aren't logged in will be automatically redirected to the login screen when attempting to access any protected page.
