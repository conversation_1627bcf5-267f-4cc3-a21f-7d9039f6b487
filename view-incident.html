<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - View Incident</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        /* Dashboard/Timeline Style Layout */
        .incident-view-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Hero Section */
        .incident-hero {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .incident-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 30px;
            align-items: center;
        }

        .hero-icon {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            background: var(--accent-red);
            box-shadow: 0 8px 24px rgba(229, 57, 53, 0.4);
            position: relative;
        }

        .hero-icon.water {
            background: var(--accent-blue);
            box-shadow: 0 8px 24px rgba(30, 136, 229, 0.4);
        }

        .hero-icon::after {
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            z-index: -1;
        }

        .hero-info h1 {
            font-size: 2.5rem;
            margin: 0 0 10px 0;
            color: var(--text-light);
            font-weight: 700;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .hero-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 20px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 1.1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .hero-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .main-dashboard {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .sidebar-dashboard {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* Widget Styling */
        .widget {
            background: var(--secondary-dark);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .widget-header {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .widget-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-light);
            margin: 0;
        }

        .widget-icon {
            font-size: 1.2rem;
            color: var(--accent-blue);
        }

        .widget-badge {
            background: var(--accent-blue);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .widget-content {
            padding: 25px;
        }

        /* Timeline Widget */
        .timeline {
            position: relative;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border-color);
        }

        .timeline-item {
            position: relative;
            padding-left: 50px;
            margin-bottom: 25px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: 12px;
            top: 6px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--accent-blue);
            border: 3px solid var(--secondary-dark);
            z-index: 1;
        }

        .timeline-dot.created { background: var(--accent-green); }
        .timeline-dot.updated { background: var(--accent-orange); }
        .timeline-dot.critical { background: var(--accent-red); }

        .timeline-content {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid var(--accent-blue);
        }

        .timeline-title {
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .timeline-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .timeline-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
            opacity: 0.7;
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .info-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .info-value {
            font-size: 1rem;
            color: var(--text-light);
            font-weight: 500;
            word-wrap: break-word;
        }

        .info-value.highlight {
            color: var(--accent-green);
            font-weight: 600;
        }

        .info-value.large {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Map Widget */
        .map-widget {
            height: 350px;
        }

        .map-container {
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Metrics Widget */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .metric-icon {
            font-size: 2rem;
            color: var(--accent-blue);
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Expandable Sections */
        .expandable-section {
            margin-top: 20px;
        }

        .section-toggle {
            background: none;
            border: none;
            color: var(--accent-blue);
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 0;
            transition: color 0.3s ease;
        }

        .section-toggle:hover {
            color: var(--text-light);
        }

        .section-content {
            display: none;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .section-content.expanded {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar-dashboard {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 20px;
            }

            .hero-stats {
                justify-content: center;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="incident-view-container">
                <!-- Hero Section -->
                <div class="incident-hero">
                    <div class="hero-content">
                        <div class="hero-icon" id="hero-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="hero-info">
                            <h1 id="incident-title">Loading...</h1>
                            <div class="hero-subtitle" id="incident-subtitle">Loading...</div>
                            <div class="hero-stats" id="hero-stats">
                                <!-- Stats will be populated here -->
                            </div>
                        </div>
                        <div class="hero-actions">
                            <button class="btn btn-primary" id="sendNotificationBtn">
                                <i class="fas fa-bell"></i> Send Notification
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                            <a href="#" class="btn btn-outline" id="edit-incident-link">
                                <i class="fas fa-edit"></i> Edit Incident
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Main Dashboard -->
                    <div class="main-dashboard">
                        <!-- Location & Map Widget -->
                        <div class="widget map-widget">
                            <div class="widget-header">
                                <h3 class="widget-title">
                                    <i class="widget-icon fas fa-map-marker-alt"></i>
                                    Incident Location
                                </h3>
                            </div>
                            <div class="map-container" id="incident-map">
                                <!-- Map will be rendered here -->
                            </div>
                        </div>

                        <!-- Incident Details Widget -->
                        <div class="widget">
                            <div class="widget-header">
                                <h3 class="widget-title">
                                    <i class="widget-icon fas fa-clipboard-list"></i>
                                    Incident Details
                                </h3>
                                <span class="widget-badge" id="incident-type-badge">Loading...</span>
                            </div>
                            <div class="widget-content">
                                <div class="info-grid" id="incident-details-grid">
                                    <!-- Details will be populated here -->
                                </div>

                                <div class="expandable-section">
                                    <button class="section-toggle" onclick="toggleSection('type-specific-details')">
                                        <i class="fas fa-chevron-right"></i>
                                        Type-Specific Details
                                    </button>
                                    <div class="section-content" id="type-specific-details">
                                        <div class="info-grid" id="type-specific-grid">
                                            <!-- Type-specific details will be populated here -->
                                        </div>
                                    </div>
                                </div>

                                <div class="expandable-section">
                                    <button class="section-toggle" onclick="toggleSection('response-details')">
                                        <i class="fas fa-chevron-right"></i>
                                        Response Information
                                    </button>
                                    <div class="section-content" id="response-details">
                                        <div class="info-grid" id="response-grid">
                                            <!-- Response details will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Dashboard -->
                    <div class="sidebar-dashboard">
                        <!-- Key Metrics Widget -->
                        <div class="widget">
                            <div class="widget-header">
                                <h3 class="widget-title">
                                    <i class="widget-icon fas fa-chart-bar"></i>
                                    Key Metrics
                                </h3>
                            </div>
                            <div class="widget-content">
                                <div class="metrics-grid" id="metrics-grid">
                                    <!-- Metrics will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Timeline Widget -->
                        <div class="widget">
                            <div class="widget-header">
                                <h3 class="widget-title">
                                    <i class="widget-icon fas fa-history"></i>
                                    Timeline
                                </h3>
                            </div>
                            <div class="widget-content">
                                <div class="timeline" id="incident-timeline">
                                    <!-- Timeline will be populated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Property Information Widget -->
                        <div class="widget">
                            <div class="widget-header">
                                <h3 class="widget-title">
                                    <i class="widget-icon fas fa-home"></i>
                                    Property Information
                                </h3>
                            </div>
                            <div class="widget-content">
                                <div class="info-grid" id="property-grid">
                                    <!-- Property details will be populated here -->
                                </div>

                                <div class="expandable-section">
                                    <button class="section-toggle" onclick="toggleSection('contact-details')">
                                        <i class="fas fa-chevron-right"></i>
                                        Contact Information
                                    </button>
                                    <div class="section-content" id="contact-details">
                                        <div class="info-grid" id="contact-grid">
                                            <!-- Contact details will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="js/shared-utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script>
        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Toggle expandable sections
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId);
            const toggle = content.previousElementSibling;
            const icon = toggle.querySelector('i');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.className = 'fas fa-chevron-right';
            } else {
                content.classList.add('expanded');
                icon.className = 'fas fa-chevron-down';
            }
        }

        function loadIncidentDetails() {
            const urlParams = new URLSearchParams(window.location.search);
            const incidentId = urlParams.get('id');

            if (!incidentId) {
                showNotification('Error: No incident ID specified', 'error');
                document.getElementById('incident-title').textContent = 'Invalid Incident ID';
                return;
            }

            document.getElementById('incident-title').textContent = 'Loading incident details...';

            API.incidents.getById(incidentId)
                .then(data => {
                    if (data) {
                        if (data.success === false) {
                            const errorMsg = data.msg || data.message || 'Unknown error';
                            showNotification('Error loading incident details: ' + errorMsg, 'error');
                            document.getElementById('incident-title').textContent = `Error: ${errorMsg}`;
                        } else {
                            const incidentData = data.data || data;
                            updateIncidentUI(incidentData);
                        }
                    } else {
                        document.getElementById('incident-title').textContent = 'Could not load incident details';
                        showNotification('Could not load incident details. Authentication might be required.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching incident details:', error);
                    showNotification('Error connecting to server to load incident details.', 'error');
                    document.getElementById('incident-title').textContent = 'Error loading incident details';
                });
        }

        function updateIncidentUI(incident) {
            console.log("Updating UI with incident:", incident);

            // Update hero section
            document.getElementById('incident-title').textContent = incident.title || 'Incident Details';
            document.getElementById('incident-subtitle').textContent = `Incident #${incident.id} • ${incident.incidentType?.name || 'Unknown Type'}`;
            document.getElementById('incident-type-badge').textContent = incident.incidentType?.name || 'Unknown';

            // Update hero icon
            updateHeroIcon(incident.incidentType);

            // Populate hero stats
            populateHeroStats(incident);

            // Populate all widgets
            populateIncidentDetails(incident);
            populateTypeSpecificDetails(incident);
            populateResponseDetails(incident);
            populateMetrics(incident);
            populateTimeline(incident);
            populatePropertyDetails(incident);
            populateContactDetails(incident);

            // Initialize map
            if (incident.latitude && incident.longitude) {
                initIncidentMap(incident.latitude, incident.longitude, incident.address);
            }

            // Update edit link
            const editLink = document.getElementById('edit-incident-link');
            if (editLink && incident.id) {
                editLink.href = `edit-incident.html?id=${incident.id}`;
            }
        }

        function updateHeroIcon(incidentType) {
            const iconContainer = document.getElementById('hero-icon');
            if (!iconContainer) return;

            iconContainer.className = 'hero-icon';

            if (incidentType && incidentType.category === 'fire') {
                iconContainer.innerHTML = '<i class="fas fa-fire"></i>';
            } else if (incidentType && incidentType.category === 'water') {
                iconContainer.classList.add('water');
                iconContainer.innerHTML = '<i class="fas fa-water"></i>';
            } else {
                iconContainer.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            }
        }

        function populateHeroStats(incident) {
            const statsContainer = document.getElementById('hero-stats');
            if (!statsContainer) return;

            const incidentDate = incident.incidentDate ? new Date(incident.incidentDate) : null;

            statsContainer.innerHTML = `
                <div class="stat-item">
                    <div class="stat-label">Status</div>
                    <div class="stat-value">${incident.status?.name || 'Active'}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Severity</div>
                    <div class="stat-value">${(incident.severity || 'moderate').toUpperCase()}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Date</div>
                    <div class="stat-value">${incidentDate ? incidentDate.toLocaleDateString() : 'N/A'}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Time</div>
                    <div class="stat-value">${incidentDate ? incidentDate.toLocaleTimeString() : 'N/A'}</div>
                </div>
            `;
        }

        function populateIncidentDetails(incident) {
            const grid = document.getElementById('incident-details-grid');
            if (!grid) return;

            grid.innerHTML = `
                <div class="info-item">
                    <div class="info-label">Description</div>
                    <div class="info-value">${incident.description || 'No description available.'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Address</div>
                    <div class="info-value large">${incident.address || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">City</div>
                    <div class="info-value">${incident.city || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">County</div>
                    <div class="info-value">${incident.county || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Cross Street</div>
                    <div class="info-value">${incident.crossStreet || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Alarm Level</div>
                    <div class="info-value">${incident.alarmLevel || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Reported By</div>
                    <div class="info-value">${incident.user?.username || 'System'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Updates</div>
                    <div class="info-value">${incident.updates || 'No updates available.'}</div>
                </div>
            `;
        }

        function populateTypeSpecificDetails(incident) {
            const grid = document.getElementById('type-specific-grid');
            if (!grid) return;

            const incidentTypeId = incident.incidentTypeId;
            const isFireIncident = (incidentTypeId >= 1 && incidentTypeId <= 4) || (incidentTypeId >= 11);
            const isWaterIncident = (incidentTypeId >= 5 && incidentTypeId <= 10);

            let detailsHTML = '';

            if (incident.incidentDetail) {
                const detail = incident.incidentDetail;

                if (isFireIncident) {
                    detailsHTML = `
                        <div class="info-item">
                            <div class="info-label">Fire Type</div>
                            <div class="info-value">${detail.fireType || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Structure Type</div>
                            <div class="info-value">${detail.structureType || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Smoke Type</div>
                            <div class="info-value">${detail.smokeType || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Smoke Severity</div>
                            <div class="info-value">${detail.smokeSeverity || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">People Affected</div>
                            <div class="info-value">${detail.peopleAffected || 0}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Evacuation Status</div>
                            <div class="info-value">${(detail.evacuationStatus || 'none').toUpperCase()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Estimated Damage</div>
                            <div class="info-value highlight">${detail.estimatedDamage ? `$${parseFloat(detail.estimatedDamage).toLocaleString()}` : 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Responder Count</div>
                            <div class="info-value">${detail.responderCount || 'N/A'}</div>
                        </div>
                    `;
                } else if (isWaterIncident) {
                    detailsHTML = `
                        <div class="info-item">
                            <div class="info-label">Water Type</div>
                            <div class="info-value">${detail.waterType || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Water Level</div>
                            <div class="info-value">${detail.waterLevel || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Damage Extent</div>
                            <div class="info-value">${detail.damageExtent || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Area Affected</div>
                            <div class="info-value">${detail.areaAffected || 'N/A'}</div>
                        </div>
                    `;
                }
            }

            if (!detailsHTML) {
                detailsHTML = `
                    <div class="info-item">
                        <div class="info-value">No type-specific details available.</div>
                    </div>
                `;
            }

            grid.innerHTML = detailsHTML;
        }

        function populateResponseDetails(incident) {
            const grid = document.getElementById('response-grid');
            if (!grid) return;

            const detail = incident.incidentDetail;

            grid.innerHTML = `
                <div class="info-item">
                    <div class="info-label">Response Details</div>
                    <div class="info-value">${detail?.responseDetails || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Conditions on Arrival</div>
                    <div class="info-value">${detail?.conditionsOnArrival || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Dispatch Information</div>
                    <div class="info-value">${incident.dispatchInfo || 'N/A'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Additional Notes</div>
                    <div class="info-value">${detail?.notes || 'No additional notes available.'}</div>
                </div>
            `;
        }

        function populateMetrics(incident) {
            const grid = document.getElementById('metrics-grid');
            if (!grid) return;

            const detail = incident.incidentDetail;
            const peopleAffected = detail?.peopleAffected || 0;
            const responderCount = detail?.responderCount || 0;
            const estimatedDamage = detail?.estimatedDamage || 0;
            const propertyValue = detail?.propertyValue || 0;

            grid.innerHTML = `
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-value">${peopleAffected}</div>
                    <div class="metric-label">People Affected</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-ambulance"></i>
                    </div>
                    <div class="metric-value">${responderCount}</div>
                    <div class="metric-label">Responders</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="metric-value">${estimatedDamage ? `$${(estimatedDamage / 1000).toFixed(0)}K` : '$0'}</div>
                    <div class="metric-label">Est. Damage</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="metric-value">${propertyValue ? `$${(propertyValue / 1000).toFixed(0)}K` : '$0'}</div>
                    <div class="metric-label">Property Value</div>
                </div>
            `;
        }

        function populateTimeline(incident) {
            const timeline = document.getElementById('incident-timeline');
            if (!timeline) return;

            const createdDate = incident.createdAt ? new Date(incident.createdAt) : null;
            const updatedDate = incident.updatedAt ? new Date(incident.updatedAt) : null;

            let timelineHTML = '';

            if (createdDate) {
                timelineHTML += `
                    <div class="timeline-item">
                        <div class="timeline-dot created"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Incident Created</div>
                            <div class="timeline-description">Initial incident report filed</div>
                            <div class="timeline-time">${createdDate.toLocaleString()}</div>
                        </div>
                    </div>
                `;
            }

            if (updatedDate && updatedDate.getTime() !== createdDate?.getTime()) {
                timelineHTML += `
                    <div class="timeline-item">
                        <div class="timeline-dot updated"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Incident Updated</div>
                            <div class="timeline-description">Latest information updated</div>
                            <div class="timeline-time">${updatedDate.toLocaleString()}</div>
                        </div>
                    </div>
                `;
            }

            if (incident.status?.name && incident.status.name.toLowerCase() !== 'active') {
                timelineHTML += `
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Status: ${incident.status.name}</div>
                            <div class="timeline-description">Incident status changed</div>
                            <div class="timeline-time">${updatedDate ? updatedDate.toLocaleString() : 'Unknown'}</div>
                        </div>
                    </div>
                `;
            }

            if (!timelineHTML) {
                timelineHTML = `
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">No Timeline Data</div>
                            <div class="timeline-description">No timeline information available</div>
                        </div>
                    </div>
                `;
            }

            timeline.innerHTML = timelineHTML;
        }

        function populatePropertyDetails(incident) {
            const grid = document.getElementById('property-grid');
            if (!grid) return;

            if (incident.incidentDetail) {
                const detail = incident.incidentDetail;
                grid.innerHTML = `
                    <div class="info-item">
                        <div class="info-label">Dwelling Type</div>
                        <div class="info-value">${detail.dwellingType || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Building Stories</div>
                        <div class="info-value">${detail.homeStories || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Year Built</div>
                        <div class="info-value">${detail.yearBuilt || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Square Footage</div>
                        <div class="info-value">${detail.squareFootage ? detail.squareFootage.toLocaleString() + ' sq ft' : 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bedrooms</div>
                        <div class="info-value">${detail.bedrooms || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bathrooms</div>
                        <div class="info-value">${detail.bathrooms || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Property Value</div>
                        <div class="info-value highlight">${detail.propertyValue ? `$${parseFloat(detail.propertyValue).toLocaleString()}` : 'N/A'}</div>
                    </div>
                `;
            } else {
                grid.innerHTML = `
                    <div class="info-item">
                        <div class="info-value">No property details available</div>
                    </div>
                `;
            }
        }

        function populateContactDetails(incident) {
            const grid = document.getElementById('contact-grid');
            if (!grid) return;

            if (incident.incidentDetail) {
                const detail = incident.incidentDetail;
                grid.innerHTML = `
                    <div class="info-item">
                        <div class="info-label">Property Owner</div>
                        <div class="info-value">${detail.propertyOwner || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Secondary Owner</div>
                        <div class="info-value">${detail.secondaryOwner || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Contact Person</div>
                        <div class="info-value">${detail.ownerContact || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Phone Number</div>
                        <div class="info-value">${detail.ownerPhone || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email Address</div>
                        <div class="info-value">${detail.ownerEmail || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Owner Address</div>
                        <div class="info-value">${detail.ownerAddress || 'N/A'}</div>
                    </div>
                `;
            } else {
                grid.innerHTML = `
                    <div class="info-item">
                        <div class="info-value">No contact details available</div>
                    </div>
                `;
            }
        }

        function initIncidentMap(latitude, longitude, address) {
            const mapElement = document.getElementById('incident-map');
            if (!mapElement || !latitude || !longitude) {
                if (mapElement) {
                    mapElement.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 1.1rem;"><i class="fas fa-map-marker-alt" style="margin-right: 10px;"></i>No location data available</div>';
                }
                return;
            }

            mapElement.innerHTML = '';

            setTimeout(() => {
                try {
                    const map = L.map('incident-map').setView([latitude, longitude], 16);

                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);

                    const marker = L.marker([latitude, longitude]).addTo(map);
                    marker.bindPopup(`
                        <div style="text-align: center;">
                            <b>🚨 Incident Location</b><br>
                            <span style="color: #6b7280;">${address}</span><br>
                            <small>Lat: ${latitude}, Lng: ${longitude}</small>
                        </div>
                    `).openPopup();

                    setTimeout(() => map.invalidateSize(), 100);
                } catch (error) {
                    console.error('Error initializing map:', error);
                    mapElement.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ef4444; font-size: 1.1rem;"><i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>Error loading map</div>';
                }
            }, 100);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize shared sidebar rendering
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('view-incident');
            }

            // Set up notification button
            const sendNotificationBtn = document.getElementById('sendNotificationBtn');
            if (sendNotificationBtn) {
                sendNotificationBtn.addEventListener('click', function() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const incidentId = urlParams.get('id');

                    if (!incidentId) {
                        showNotification('Error: No incident ID available', 'error');
                        return;
                    }

                    this.disabled = true;
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';

                    API.incidents.notify(incidentId)
                        .then(response => {
                            if (response && response.success !== false) {
                                showNotification('Notification sent successfully!', 'success');
                            } else {
                                showNotification('Error: ' + (response?.msg || 'Failed to send notification'), 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error sending notification:', error);
                            showNotification('Error connecting to server', 'error');
                        })
                        .finally(() => {
                            this.disabled = false;
                            this.innerHTML = originalText;
                        });
                });
            }

            if (typeof API !== 'undefined') {
                loadIncidentDetails();
            } else {
                showNotification("Error initializing page. API not available.", "error");
                setTimeout(() => {
                    if (typeof API !== 'undefined') {
                        loadIncidentDetails();
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>