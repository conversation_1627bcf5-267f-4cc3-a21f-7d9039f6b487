const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const roleAccess = require('../middleware/roleAccess');
const permissionCheck = require('../middleware/permissionCheck');
const adminAccess = require('../middleware/adminAccess');
const db = require('../models');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const { PERMISSIONS } = require('../models/permissions');

// @route   GET api/companies
// @desc    Get all companies with their admin users and optional filtering
// @access  Private (Admin only)
router.get('/', [auth, permissionCheck(PERMISSIONS.VIEW_ALL_COMPANIES)], async (req, res) => {
  try {
    const { search, status, limit = 20, offset = 0, sortBy = 'id', sortOrder = 'DESC' } = req.query;

    // Build where clause for companies
    const whereClause = {};

    // Map frontend sort columns to database fields
    const sortMapping = {
      'company_name': 'name',
      'company_email': 'email',
      'company_phone': 'phone',
      'contact_person': 'contactPerson',
      'users': 'id', // Will be handled specially for user count sorting
      'name': 'name',
      'email': 'email',
      'phone': 'phone',
      'status': 'status',
      'date': 'createdAt',
      'id': 'id'
    };

    // Determine sort order
    let orderClause;
    if (sortMapping[sortBy]) {
      orderClause = [sortMapping[sortBy], sortOrder];
    } else {
      // Default sorting
      orderClause = ['id', 'DESC'];
    }

    // Add status filter if provided
    if (status) {
      if (status === 'active') {
        whereClause.status = true;
      } else if (status === 'inactive') {
        whereClause.status = false;
      }
    }

    // Add search filter if provided
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { contactPerson: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } }
      ];
    }

    // Get paginated companies with their admin users
    const companies = await db.company.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: db.companyType,
          attributes: ['id', 'name', 'code'],
          required: false
        },
        {
          model: db.user,
          attributes: ['id', 'firstName', 'lastName', 'email', 'username', 'phone', 'role', 'status', 'createdAt'],
          where: { role: 'company_admin' },
          required: false,
          include: [
            {
              model: db.subscription,
              attributes: ['plan', 'status', 'maxUsers', 'currentUsers'],
              required: false
            }
          ]
        }
      ],
      order: [orderClause],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Get user counts for each company
    const companyIds = companies.rows.map(company => company.id);
    const userCounts = await db.user.findAll({
      attributes: [
        'company_id',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'userCount']
      ],
      where: {
        company_id: companyIds
      },
      group: ['company_id'],
      raw: true
    });

    // Create a map for quick lookup of user counts
    const userCountMap = {};
    userCounts.forEach(count => {
      userCountMap[count.company_id] = parseInt(count.userCount);
    });

    // Format response to match expected frontend format
    let subscribers = companies.rows.map(company => ({
        id: company.id,
        firstName: company.users?.[0]?.firstName || '',
        lastName: company.users?.[0]?.lastName || '',
        email: company.users?.[0]?.email || company.email,
        username: company.users?.[0]?.username || '',
        phone: company.users?.[0]?.phone || company.phone,
        status: company.status,
        createdAt: company.createdAt,
        userCount: userCountMap[company.id] || 0,
        company: {
          id: company.id,
          name: company.name,
          phone: company.phone,
          email: company.email,
          address: company.address,
          city: company.city,
          state: company.state,
          zip: company.zip,
          contactPerson: company.contactPerson,
          status: company.status,
          companyType: company.companyType
        },
        subscription: company.users?.[0]?.subscription || null
      }));

    // Handle user count sorting (since we can't sort by user count in the main query)
    if (sortBy === 'users') {
      subscribers.sort((a, b) => {
        if (sortOrder === 'ASC') {
          return a.userCount - b.userCount;
        } else {
          return b.userCount - a.userCount;
        }
      });
    }

    const response = {
      total: companies.count,
      page: Math.floor(offset / limit) + 1,
      pageSize: parseInt(limit),
      totalPages: Math.ceil(companies.count / parseInt(limit)),
      subscribers: subscribers
    };

    res.json(response);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/companies/stats
// @desc    Get company statistics (counts by status, etc.)
// @access  Private (Admin access only)
router.get('/stats', auth, async (req, res) => {
  try {
    // Special case for admin role - bypass permission check
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        msg: 'Permission denied. Required permission: admin',
        requiredPermission: 'VIEW_COMPANY_STATS',
        yourRole: req.user.role
      });
    }

    // Get total company count
    const totalCount = await db.company.count();

    // Get active company count
    const activeCount = await db.company.count({
      where: { status: true }
    });

    // Get count by company type
    let companyTypeStats = [];
    try {
      companyTypeStats = await db.company.findAll({
        attributes: [
          'company_type_id',
          [db.sequelize.fn('COUNT', db.sequelize.col('company.id')), 'count']
        ],
        include: [{
          model: db.companyType,
          attributes: ['name'],
          required: false
        }],
        group: ['company_type_id', 'companyType.id'],
        raw: true,
        nest: true
      });
    } catch (companyErr) {
      console.error('Error getting company type stats:', companyErr);
    }

    // Return the statistics
    res.json({
      totalCount,
      activeCount,
      inactiveCount: totalCount - activeCount,
      byCompanyType: companyTypeStats,
      timestamp: Date.now()
    });
  } catch (err) {
    console.error('Error getting company stats:', err);
    res.status(500).json({
      success: false,
      message: 'Server error while getting company statistics',
      error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
      totalCount: await db.company.count().catch(() => 0),
      timestamp: Date.now()
    });
  }
});

// @route   GET api/companies/:id
// @desc    Get company by ID with admin user details
// @access  Private (Admin or company admin)
router.get('/:id', auth, async (req, res) => {
  try {
    const { hasPermission, PERMISSIONS } = require('../models/permissions');

    // Check permissions
    if (
      req.user.role !== 'admin' &&
      req.user.company_id?.toString() !== req.params.id.toString() &&
      !hasPermission(req.user.role, PERMISSIONS.VIEW_ALL_COMPANIES)
    ) {
      return res.status(403).json({ msg: 'Not authorized to view this company' });
    }

    const company = await db.company.findByPk(req.params.id, {
      include: [
        {
          model: db.companyType,
          attributes: ['id', 'name', 'code']
        },
        {
          model: db.user,
          where: { role: 'company_admin' },
          required: false,
          attributes: { exclude: ['password'] },
          include: [
            {
              model: db.userPreference,
              required: false
            },
            {
              model: db.subscription,
              required: false
            }
          ]
        }
      ]
    });

    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Format response to match expected subscriber format
    const adminUser = company.users?.[0];
    const response = {
      id: adminUser?.id || company.id,
      firstName: adminUser?.firstName || '',
      lastName: adminUser?.lastName || '',
      email: adminUser?.email || company.email,
      username: adminUser?.username || '',
      phone: adminUser?.phone || company.phone,
      status: company.status,
      createdAt: company.createdAt,
      company: {
        id: company.id,
        name: company.name,
        phone: company.phone,
        email: company.email,
        address: company.address,
        city: company.city,
        state: company.state,
        zip: company.zip,
        contactPerson: company.contactPerson,
        status: company.status,
        companyType: company.companyType
      },
      subscriberPreference: adminUser?.userPreference ? {
        fireAlerts: adminUser.userPreference.fireIncidentAlert,
        waterAlerts: adminUser.userPreference.waterIncidentAlert,
        otherAlerts: adminUser.userPreference.otherIncidentAlert,
        emailNotify: adminUser.userPreference.notifyByEmail,
        smsNotify: adminUser.userPreference.notifyBySms,
        appNotify: adminUser.userPreference.notifyByPush,
        frequency: adminUser.userPreference.frequency || 'immediate'
      } : null,
      subscription: adminUser?.subscription || null
    };

    res.json(response);
  } catch (err) {
    console.error('Full error:', err);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/companies
// @desc    Create a new company with admin user
// @access  Private (Admin only)
router.post(
  '/',
  [
    auth,
    permissionCheck(PERMISSIONS.CREATE_COMPANY),
  ],
  async (req, res) => {
    console.log('Create company request body:', req.body);

    const { company, admin, subscription } = req.body;

    if (!company || !admin) {
      return res.status(400).json({ msg: 'Company and admin information required' });
    }

    try {
      // Start transaction
      const transaction = await db.sequelize.transaction();

      try {
        // Check if admin email already exists
        const existingUser = await db.user.findOne({
          where: { email: admin.email },
          transaction
        });

        if (existingUser) {
          await transaction.rollback();
          return res.status(400).json({ msg: 'Admin email already in use' });
        }

        // Create company
        const newCompany = await db.company.create({
          name: company.name,
          phone: company.phone,
          email: company.email,
          address: company.address,
          city: company.city,
          state: company.state,
          zip: company.zip,
          contactPerson: company.contactPerson || `${admin.firstName} ${admin.lastName}`,
          status: company.status !== 'inactive',
          company_type_id: company.companyTypeId
        }, { transaction });

        console.log('Created new company:', newCompany.id);

        // Generate password for admin user
        const password = admin.generatePassword ?
          Math.random().toString(36).slice(2) + Math.random().toString(36).toUpperCase().slice(2) :
          'ChangeMe123!';

        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create admin user
        const adminUser = await db.user.create({
          firstName: admin.firstName,
          lastName: admin.lastName,
          email: admin.email,
          username: admin.email.toLowerCase(),
          password: hashedPassword,
          phone: admin.phone,
          role: 'company_admin',
          status: true,
          company_id: newCompany.id
        }, { transaction });

        console.log('Created admin user:', adminUser.id);

        // Create user preferences - always create with defaults if not provided
        const { preferences } = req.body;  // Get preferences from root level
        await db.userPreference.create({
          user_id: adminUser.id,
          notifyByEmail: preferences?.emailNotify !== undefined ? preferences.emailNotify : true,
          notifyBySms: preferences?.smsNotify !== undefined ? preferences.smsNotify : true, // Default to true for SMS
          notifyByPush: preferences?.appNotify !== undefined ? preferences.appNotify : false,
          fireIncidentAlert: preferences?.fireAlerts !== undefined ? preferences.fireAlerts : true,
          waterIncidentAlert: preferences?.waterAlerts !== undefined ? preferences.waterAlerts : true,
          otherIncidentAlert: preferences?.otherAlerts !== undefined ? preferences.otherAlerts : true,
          dailyDigest: preferences?.dailyDigest !== undefined ? preferences.dailyDigest : false,
          alertRadius: preferences?.alertRadius !== undefined ? preferences.alertRadius : 10
        }, { transaction });

        // Create subscription if provided
        if (subscription) {
          await db.subscription.create({
            userId: adminUser.id,  // Use camelCase field name that maps to user_id
            plan: subscription.plan || 'standard',
            maxUsers: subscription.maxUsers || 5,  // Use camelCase field name
            billingCycle: subscription.billingCycle || 'monthly',  // Use camelCase field name
            billingEmail: subscription.billingEmail || admin.email,  // Use camelCase field name
            status: 'active',
            currentUsers: 1  // Use camelCase field name
          }, { transaction });

          // Create county subscriptions if counties are provided
          if (subscription.counties && Array.isArray(subscription.counties) && subscription.counties.length > 0) {
            // Convert county IDs to integers and validate
            const countyIds = subscription.counties.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0);

            if (countyIds.length > 0) {
              // Verify all counties exist
              const existingCounties = await db.county.findAll({
                where: { id: countyIds },
                attributes: ['id'],
                transaction
              });

              if (existingCounties.length !== countyIds.length) {
                await transaction.rollback();
                return res.status(400).json({ msg: 'One or more counties not found' });
              }

              // Create county subscriptions for the admin user
              const userSubscriptions = countyIds.map(countyId => ({
                userId: adminUser.id,
                countyId: countyId
              }));

              await db.userSubscription.bulkCreate(userSubscriptions, { transaction });
            }
          }
        }

        // Log action
        await db.activity.create({
          user_id: req.user.id,
          action: `Created new company: ${newCompany.name} (ID: ${newCompany.id})`,
          entity_type: 'company',
          entity_id: newCompany.id,
          details: `Admin user: ${adminUser.email}`,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        }, { transaction });

        await transaction.commit();

        res.status(201).json({
          success: true,
          msg: 'Company and admin user created successfully',
          company: {
            id: newCompany.id,
            name: newCompany.name,
            email: newCompany.email
          },
          admin: {
            id: adminUser.id,
            email: adminUser.email,
            temporaryPassword: admin.generatePassword ? password : null
          }
        });

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (err) {
      console.error('Error creating company:', err.message);
      res.status(500).json({
        success: false,
        msg: 'Server Error: ' + err.message
      });
    }
  }
);

// @route   PUT api/companies/:id
// @desc    Update company information
// @access  Private (Admin or company admin)
router.put('/:id', auth, async (req, res) => {
  try {
    const { hasPermission, PERMISSIONS } = require('../models/permissions');

    // Check permissions
    if (
      req.user.role !== 'admin' &&
      req.user.company_id?.toString() !== req.params.id.toString() &&
      !hasPermission(req.user.role, PERMISSIONS.UPDATE_COMPANY)
    ) {
      return res.status(403).json({ msg: 'Not authorized to update this company' });
    }

    const companyId = req.params.id;
    const { company } = req.body;

    if (!company) {
      return res.status(400).json({ msg: 'Company information required' });
    }

    // Find the company
    const existingCompany = await db.company.findByPk(companyId);
    if (!existingCompany) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Update company information
    await existingCompany.update({
      name: company.name || existingCompany.name,
      phone: company.phone || existingCompany.phone,
      email: company.email || existingCompany.email,
      address: company.address || existingCompany.address,
      city: company.city || existingCompany.city,
      state: company.state || existingCompany.state,
      zip: company.zip || existingCompany.zip,
      contactPerson: company.contactPerson || existingCompany.contactPerson,
      status: company.status !== undefined ? (company.status === 'active' || company.status === true) : existingCompany.status,
      company_type_id: company.companyTypeId || existingCompany.company_type_id
    });

    // Log action
    await db.activity.create({
      user_id: req.user.id,
      action: `Updated company information: ${existingCompany.name} (ID: ${companyId})`,
      entity_type: 'company',
      entity_id: companyId,
      details: `Updated by ${req.user.firstName} ${req.user.lastName}`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      msg: 'Company updated successfully',
      company: {
        id: existingCompany.id,
        name: existingCompany.name,
        email: existingCompany.email
      }
    });

  } catch (err) {
    console.error('Error updating company:', err);
    res.status(500).json({
      success: false,
      msg: 'Server Error: ' + err.message
    });
  }
});

// @route   GET api/companies/:id/users
// @desc    Get all users for a company
// @access  Private (Admin or company admin)
router.get('/:id/users', [auth, permissionCheck(PERMISSIONS.VIEW_ALL_USERS)], async (req, res) => {
  try {
    const users = await db.user.findAll({
      where: { company_id: req.params.id },
      attributes: { exclude: ['password'] },
      include: [
        {
          model: db.userPreference,
          required: false
        }
      ]
    });
    res.json({ success: true, data: users });
  } catch (err) {
    console.error('Error fetching users for company:', err);
    res.status(500).json({ success: false, message: 'Server error fetching users for company' });
  }
});

// @route   GET api/companies/:id/counties
// @desc    Get counties assigned to a company
// @access  Private (Admin only)
router.get('/:id/counties', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Get all users for this company
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    if (users.length === 0) {
      return res.json({ counties: [], userCount: 0 });
    }

    // Get county subscriptions for all users in the company
    const userIds = users.map(user => user.id);
    const subscriptions = await db.userSubscription.findAll({
      where: { userId: userIds },
      include: [
        {
          model: db.county,
          attributes: ['id', 'name', 'state', 'state_id'],
          include: [
            {
              model: db.state,
              as: 'stateObj',
              attributes: ['id', 'name', 'abbreviation'],
              required: false
            }
          ]
        }
      ]
    });

    // Extract unique counties
    const countiesMap = new Map();
    subscriptions.forEach(sub => {
      if (sub.county) {
        const county = sub.county;
        countiesMap.set(county.id, {
          id: county.id,
          name: county.name,
          state: county.state,
          stateId: county.state_id,
          stateName: county.stateObj?.name || county.state,
          stateAbbreviation: county.stateObj?.abbreviation || county.state
        });
      }
    });

    const counties = Array.from(countiesMap.values());

    res.json({
      counties,
      userCount: users.length,
      subscriptionCount: subscriptions.length
    });
  } catch (err) {
    console.error('Error fetching company counties:', err);
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   PUT api/companies/:id/counties
// @desc    Update counties assigned to a company (Admin only)
// @access  Private (Admin only)
router.put('/:id/counties', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;
    const { counties } = req.body;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Validate counties array
    if (!Array.isArray(counties)) {
      return res.status(400).json({ msg: 'Counties must be an array' });
    }

    // Get all users for this company
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    if (users.length === 0) {
      return res.status(400).json({ msg: 'No users found for this company' });
    }

    const userIds = users.map(user => user.id);

    // Start transaction
    const transaction = await db.sequelize.transaction();

    try {
      // Remove existing county subscriptions for all users in the company
      await db.userSubscription.destroy({
        where: { userId: userIds },
        transaction
      });

      // Add new county subscriptions if counties are provided
      if (counties.length > 0) {
        // Convert county IDs to integers and validate
        const countyIds = counties.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0);

        if (countyIds.length > 0) {
          // Verify all counties exist
          const existingCounties = await db.county.findAll({
            where: { id: countyIds },
            attributes: ['id'],
            transaction
          });

          if (existingCounties.length !== countyIds.length) {
            await transaction.rollback();
            return res.status(400).json({ msg: 'One or more counties not found' });
          }

          // Create subscriptions for each user and each county
          const subscriptions = [];
          userIds.forEach(userId => {
            countyIds.forEach(countyId => {
              subscriptions.push({
                userId: userId,
                countyId: countyId
              });
            });
          });

          await db.userSubscription.bulkCreate(subscriptions, { transaction });
        }
      }

      // Commit transaction
      await transaction.commit();

      // Log action
      await db.logger.create({
        action: `Admin updated counties for company (${companyId}) - ${counties.length} counties assigned`,
        user_id: req.user.id,
        action_date: new Date()
      });

      res.json({
        success: true,
        msg: 'Counties updated successfully',
        countiesAssigned: counties.length,
        usersAffected: users.length
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (err) {
    console.error('Error updating company counties:', err);
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   POST api/companies/:id/counties/add
// @desc    Add counties to a company (Admin only) - ADDITIVE behavior
// @access  Private (Admin only)
router.post('/:id/counties/add', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;
    const { counties } = req.body;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Validate counties array
    if (!Array.isArray(counties) || counties.length === 0) {
      return res.status(400).json({ msg: 'Counties must be a non-empty array' });
    }

    // Get all users for this company
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    if (users.length === 0) {
      return res.status(400).json({ msg: 'No users found for this company' });
    }

    const userIds = users.map(user => user.id);

    // Start transaction
    const transaction = await db.sequelize.transaction();

    try {
      // Convert county IDs to integers and validate
      const countyIds = counties.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0);

      if (countyIds.length === 0) {
        await transaction.rollback();
        return res.status(400).json({ msg: 'No valid county IDs provided' });
      }

      // Verify all counties exist
      const existingCounties = await db.county.findAll({
        where: { id: countyIds },
        attributes: ['id'],
        transaction
      });

      if (existingCounties.length !== countyIds.length) {
        await transaction.rollback();
        return res.status(400).json({ msg: 'One or more counties not found' });
      }

      // Get existing subscriptions to avoid duplicates
      const existingSubscriptions = await db.userSubscription.findAll({
        where: {
          userId: userIds,
          countyId: countyIds
        },
        attributes: ['userId', 'countyId'],
        transaction
      });

      // Create map of existing subscriptions
      const existingMap = new Set();
      existingSubscriptions.forEach(sub => {
        existingMap.add(`${sub.userId}-${sub.countyId}`);
      });

      // Create subscriptions for each user and each county (only if not already exists)
      const subscriptions = [];
      userIds.forEach(userId => {
        countyIds.forEach(countyId => {
          const key = `${userId}-${countyId}`;
          if (!existingMap.has(key)) {
            subscriptions.push({
              userId: userId,
              countyId: countyId
            });
          }
        });
      });

      if (subscriptions.length > 0) {
        await db.userSubscription.bulkCreate(subscriptions, { transaction });
      }

      // Commit transaction
      await transaction.commit();

      // Log action (outside transaction to avoid rollback issues)
      try {
        await db.logger.create({
          action: `Admin added ${counties.length} counties to company (${companyId}) - ${subscriptions.length} new subscriptions created`,
          user_id: req.user.id,
          action_date: new Date()
        });
      } catch (logError) {
        console.warn('Failed to log action:', logError.message);
        // Don't fail the request if logging fails
      }

      res.json({
        success: true,
        msg: 'Counties added successfully',
        countiesAdded: counties.length,
        newSubscriptions: subscriptions.length,
        usersAffected: users.length
      });

    } catch (error) {
      // Only rollback if transaction is still active
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }

  } catch (err) {
    console.error('Error adding company counties:', err);
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   POST api/companies/:id/counties/remove
// @desc    Remove counties from a company (Admin only)
// @access  Private (Admin only)
router.post('/:id/counties/remove', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;
    const { counties } = req.body;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Validate counties array
    if (!Array.isArray(counties) || counties.length === 0) {
      return res.status(400).json({ msg: 'Counties must be a non-empty array' });
    }

    // Get all users for this company
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    if (users.length === 0) {
      return res.status(400).json({ msg: 'No users found for this company' });
    }

    const userIds = users.map(user => user.id);

    // Start transaction
    const transaction = await db.sequelize.transaction();

    try {
      // Convert county IDs to integers and validate
      const countyIds = counties.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0);

      if (countyIds.length === 0) {
        await transaction.rollback();
        return res.status(400).json({ msg: 'No valid county IDs provided' });
      }

      // Remove subscriptions for specified counties and users
      const deletedCount = await db.userSubscription.destroy({
        where: {
          userId: userIds,
          countyId: countyIds
        },
        transaction
      });

      // Commit transaction
      await transaction.commit();

      // Log action (outside transaction to avoid rollback issues)
      try {
        await db.logger.create({
          action: `Admin removed ${counties.length} counties from company (${companyId}) - ${deletedCount} subscriptions removed`,
          user_id: req.user.id,
          action_date: new Date()
        });
      } catch (logError) {
        console.warn('Failed to log action:', logError.message);
        // Don't fail the request if logging fails
      }

      res.json({
        success: true,
        msg: 'Counties removed successfully',
        countiesRemoved: counties.length,
        subscriptionsRemoved: deletedCount,
        usersAffected: users.length
      });

    } catch (error) {
      // Only rollback if transaction is still active
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }

  } catch (err) {
    console.error('Error removing company counties:', err);
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   GET api/companies/:id/activity
// @desc    Get activity log for a company
// @access  Private (Admin only)
router.get('/:id/activity', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Get all users for this company to include their activities
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    const userIds = users.map(user => user.id);

    // Get activities related to this company and its users
    const activities = await db.activity.findAll({
      where: {
        [db.Sequelize.Op.or]: [
          { entity_type: 'company', entity_id: companyId },
          { user_id: userIds }
        ]
      },
      include: [
        {
          model: db.user,
          attributes: ['firstName', 'lastName', 'email'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 100 // Limit to last 100 activities
    });

    // Format the response
    const formattedActivities = activities.map(activity => ({
      id: activity.id,
      action: activity.action,
      details: activity.details,
      created: activity.created_at,
      userName: activity.user ? `${activity.user.firstName} ${activity.user.lastName}` : 'System',
      userEmail: activity.user ? activity.user.email : null,
      severity: activity.severity,
      module: activity.module
    }));

    res.json({
      success: true,
      data: formattedActivities
    });

  } catch (err) {
    console.error('Error fetching company activity:', err);
    res.status(500).json({ success: false, message: 'Server error fetching company activity' });
  }
});

// @route   GET api/companies/:id/notifications
// @desc    Get notifications for a company (all users in the company)
// @access  Private (Admin only)
router.get('/:id/notifications', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;
    const { limit = 50, offset = 0, type, status } = req.query;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Get all users for this company
    const users = await db.user.findAll({
      where: { company_id: companyId },
      attributes: ['id']
    });

    const userIds = users.map(user => user.id);

    if (userIds.length === 0) {
      return res.json({
        success: true,
        data: [],
        total: 0
      });
    }

    // Build where clause for notifications
    const whereClause = {
      userId: userIds
    };

    if (type) {
      whereClause.type = type;
    }

    if (status) {
      whereClause.status = status;
    }

    // Get notifications for all users in the company
    const { count, rows } = await db.notification.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: db.incident,
          attributes: ['id', 'address', 'city', 'county', 'state', 'created_at'],
          include: [
            {
              model: db.incidentType,
              attributes: ['name']
            }
          ]
        },
        {
          model: db.user,
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Format the response
    const formattedNotifications = rows.map(notification => ({
      id: notification.id,
      type: notification.type,
      status: notification.status,
      content: notification.content,
      sentAt: notification.sentAt,
      deliveredAt: notification.deliveredAt,
      createdAt: notification.createdAt,
      incident: notification.incident ? {
        id: notification.incident.id,
        address: notification.incident.address,
        city: notification.incident.city,
        county: notification.incident.county,
        state: notification.incident.state,
        type: notification.incident.incidentType?.name || 'Unknown',
        date: notification.incident.created_at
      } : null,
      user: notification.user ? {
        id: notification.user.id,
        name: `${notification.user.firstName} ${notification.user.lastName}`,
        email: notification.user.email
      } : null
    }));

    res.json({
      success: true,
      data: formattedNotifications,
      total: count,
      page: Math.floor(offset / limit) + 1,
      totalPages: Math.ceil(count / limit)
    });

  } catch (err) {
    console.error('Error fetching company notifications:', err);
    res.status(500).json({ success: false, message: 'Server error fetching company notifications' });
  }
});

// @route   PUT api/companies/:id/subscription
// @desc    Update subscription for a company
// @access  Private (Admin only)
router.put('/:id/subscription', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;
    const { plan, maxUsers, billingCycle, billingEmail } = req.body;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Get the company admin user to find their subscription
    const adminUser = await db.user.findOne({
      where: {
        company_id: companyId,
        role: 'company_admin'
      }
    });

    if (!adminUser) {
      return res.status(404).json({ msg: 'Company admin user not found' });
    }

    // Find or create subscription
    let subscription = await db.subscription.findOne({
      where: { userId: adminUser.id }  // Use camelCase field name
    });

    if (!subscription) {
      // Create new subscription
      subscription = await db.subscription.create({
        userId: adminUser.id,  // Use camelCase field name
        plan: plan || 'standard',
        maxUsers: maxUsers || 5,  // Use camelCase field name
        billingCycle: billingCycle || 'monthly',  // Use camelCase field name
        billingEmail: billingEmail || adminUser.email,  // Use camelCase field name
        status: 'active',
        currentUsers: 1  // Use camelCase field name
      });
    } else {
      // Update existing subscription
      await subscription.update({
        plan: plan || subscription.plan,
        maxUsers: maxUsers || subscription.maxUsers,  // Use camelCase field name
        billingCycle: billingCycle || subscription.billingCycle,  // Use camelCase field name
        billingEmail: billingEmail || subscription.billingEmail  // Use camelCase field name
      });
    }

    // Log action
    await db.activity.create({
      user_id: req.user.id,
      action: `Updated subscription for company: ${company.name} (ID: ${companyId})`,
      entity_type: 'company',
      entity_id: companyId,
      details: `Plan: ${subscription.plan}, Max Users: ${subscription.max_users}`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      msg: 'Subscription updated successfully',
      subscription: {
        plan: subscription.plan,
        maxUsers: subscription.maxUsers,  // Use camelCase field name
        billingCycle: subscription.billingCycle,  // Use camelCase field name
        billingEmail: subscription.billingEmail,  // Use camelCase field name
        status: subscription.status
      }
    });

  } catch (err) {
    console.error('Error updating company subscription:', err);
    res.status(500).json({ success: false, message: 'Server error updating subscription' });
  }
});

// @route   PUT api/companies/:id/alert-settings
// @desc    Update alert settings for a company's admin user
// @access  Private (Admin or company admin)
router.put('/:id/alert-settings', auth, async (req, res) => {
  try {
    const { hasPermission, PERMISSIONS } = require('../models/permissions');

    // Check permissions
    if (
      req.user.role !== 'admin' &&
      req.user.company_id?.toString() !== req.params.id.toString() &&
      !hasPermission(req.user.role, PERMISSIONS.UPDATE_COMPANY)
    ) {
      return res.status(403).json({ msg: 'Not authorized to update alert settings for this company' });
    }

    const companyId = req.params.id;
    const alertSettings = req.body;

    // Debug logging
    console.log('Alert settings received:', alertSettings);

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Get the company admin user
    const adminUser = await db.user.findOne({
      where: {
        company_id: companyId,
        role: 'company_admin'
      }
    });

    if (!adminUser) {
      return res.status(404).json({ msg: 'Company admin user not found' });
    }

    // Find or create user preferences
    let userPreference = await db.userPreference.findOne({
      where: { user_id: adminUser.id }
    });

    if (!userPreference) {
      // Create new preferences
      userPreference = await db.userPreference.create({
        user_id: adminUser.id,
        notifyByEmail: alertSettings.emailNotify !== undefined ? alertSettings.emailNotify : true,
        notifyBySms: alertSettings.smsNotify !== undefined ? alertSettings.smsNotify : false,
        notifyByPush: alertSettings.appNotify !== undefined ? alertSettings.appNotify : false,
        fireIncidentAlert: alertSettings.fireAlerts !== undefined ? alertSettings.fireAlerts : true,
        waterIncidentAlert: alertSettings.waterAlerts !== undefined ? alertSettings.waterAlerts : true,
        otherIncidentAlert: alertSettings.otherAlerts !== undefined ? alertSettings.otherAlerts : true,
        dailyDigest: alertSettings.dailyDigest !== undefined ? alertSettings.dailyDigest : false,
        alertRadius: alertSettings.alertRadius || 10
      });
    } else {
      // Update existing preferences
      const updateData = {
        notifyByEmail: alertSettings.emailNotify !== undefined ? alertSettings.emailNotify : userPreference.notifyByEmail,
        notifyBySms: alertSettings.smsNotify !== undefined ? alertSettings.smsNotify : userPreference.notifyBySms,
        notifyByPush: alertSettings.appNotify !== undefined ? alertSettings.appNotify : userPreference.notifyByPush,
        fireIncidentAlert: alertSettings.fireAlerts !== undefined ? alertSettings.fireAlerts : userPreference.fireIncidentAlert,
        waterIncidentAlert: alertSettings.waterAlerts !== undefined ? alertSettings.waterAlerts : userPreference.waterIncidentAlert,
        otherIncidentAlert: alertSettings.otherAlerts !== undefined ? alertSettings.otherAlerts : userPreference.otherIncidentAlert,
        dailyDigest: alertSettings.dailyDigest !== undefined ? alertSettings.dailyDigest : userPreference.dailyDigest,
        alertRadius: alertSettings.alertRadius !== undefined ? alertSettings.alertRadius : userPreference.alertRadius
      };

      console.log('Updating user preferences with:', updateData);
      await userPreference.update(updateData);
    }

    // Log action
    await db.activity.create({
      user_id: req.user.id,
      action: `Updated alert settings for company: ${company.name} (ID: ${companyId})`,
      entity_type: 'company',
      entity_id: companyId,
      details: `Updated by ${req.user.firstName} ${req.user.lastName}`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      msg: 'Alert settings updated successfully',
      preferences: {
        emailNotify: userPreference.notifyByEmail,
        smsNotify: userPreference.notifyBySms,
        appNotify: userPreference.notifyByPush,
        fireAlerts: userPreference.fireIncidentAlert,
        waterAlerts: userPreference.waterIncidentAlert,
        otherAlerts: userPreference.otherIncidentAlert,
        dailyDigest: userPreference.dailyDigest,
        alertRadius: userPreference.alertRadius
      }
    });

  } catch (err) {
    console.error('Error updating alert settings:', err);
    res.status(500).json({
      success: false,
      msg: 'Server Error: ' + err.message
    });
  }
});

// @route   DELETE api/companies/:id
// @desc    Delete a company and all associated data
// @access  Private (Admin only)
router.delete('/:id', [auth, adminAccess], async (req, res) => {
  try {
    const companyId = req.params.id;

    // Verify company exists
    const company = await db.company.findByPk(companyId);
    if (!company) {
      return res.status(404).json({ msg: 'Company not found' });
    }

    // Start a transaction to ensure data consistency
    const transaction = await db.sequelize.transaction();

    try {
      // Get all users associated with this company
      const users = await db.user.findAll({
        where: { company_id: companyId },
        attributes: ['id'],
        transaction
      });

      const userIds = users.map(user => user.id);

      if (userIds.length > 0) {
        // Delete user preferences
        await db.userPreference.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete subscriptions
        await db.subscription.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete user locations
        await db.userLocation.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete user subscriptions (county-based subscriptions)
        await db.userSubscription.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete activities related to these users
        await db.activity.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete notifications related to these users
        await db.notification.destroy({
          where: { user_id: userIds },
          transaction
        });

        // Delete the users themselves
        await db.user.destroy({
          where: { company_id: companyId },
          transaction
        });
      }

      // Log the deletion action before deleting the company
      await db.activity.create({
        user_id: req.user.id,
        action: `Deleted company: ${company.name} (ID: ${companyId})`,
        entity_type: 'company',
        entity_id: companyId,
        details: `Company deleted by ${req.user.firstName} ${req.user.lastName}. Associated users: ${userIds.length}`,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      }, { transaction });

      // Finally, delete the company
      await company.destroy({ transaction });

      // Commit the transaction
      await transaction.commit();

      res.json({
        success: true,
        msg: 'Company and all associated data deleted successfully',
        deletedCompany: {
          id: companyId,
          name: company.name,
          associatedUsers: userIds.length
        }
      });

    } catch (error) {
      // Rollback the transaction on error
      await transaction.rollback();
      throw error;
    }

  } catch (err) {
    console.error('Error deleting company:', err);
    res.status(500).json({
      success: false,
      msg: 'Server Error: ' + err.message,
      error: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
});

module.exports = router;
