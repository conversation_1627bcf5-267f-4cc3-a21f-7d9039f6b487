#!/usr/bin/env node

/**
 * JWT Secret Generator Script
 * Generates a cryptographically secure JWT secret and updates .env files
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

function generateSecureSecret(length = 64) {
  // Generate cryptographically secure random bytes
  const randomBytes = crypto.randomBytes(length);
  
  // Convert to base64 and make it URL-safe
  let secret = randomBytes.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
  
  // Ensure it's at least the specified length
  while (secret.length < length) {
    const additionalBytes = crypto.randomBytes(16);
    secret += additionalBytes.toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }
  
  return secret.substring(0, length);
}

function updateEnvFile(filePath, newSecret) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Check if JWT_SECRET exists
    if (content.includes('JWT_SECRET=')) {
      // Replace existing JWT_SECRET
      content = content.replace(/JWT_SECRET=.*$/m, `JWT_SECRET=${newSecret}`);
      console.log(`✅ Updated JWT_SECRET in: ${filePath}`);
    } else {
      // Add JWT_SECRET if it doesn't exist
      content += `\n# JWT Secret (Generated: ${new Date().toISOString()})\nJWT_SECRET=${newSecret}\n`;
      console.log(`✅ Added JWT_SECRET to: ${filePath}`);
    }
    
    fs.writeFileSync(filePath, content);
    return true;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔐 JWT Secret Generator');
  console.log('========================\n');
  
  // Generate a secure secret
  const newSecret = generateSecureSecret(64);
  
  console.log(`🎲 Generated new JWT secret (${newSecret.length} characters)`);
  console.log(`🔒 Secret preview: ${newSecret.substring(0, 16)}...${newSecret.substring(-8)}\n`);
  
  // Define paths to .env files
  const envFiles = [
    path.join(__dirname, '..', '.env'),                    // Main project .env
    path.join(__dirname, '..', 'firealerts-node', '.env')  // Backend .env
  ];
  
  let updatedFiles = 0;
  
  console.log('📝 Updating .env files...\n');
  
  envFiles.forEach(envFile => {
    if (updateEnvFile(envFile, newSecret)) {
      updatedFiles++;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`   • Generated secret length: ${newSecret.length} characters`);
  console.log(`   • Files updated: ${updatedFiles}/${envFiles.length}`);
  console.log(`   • Security strength: High (cryptographically secure)`);
  
  if (updatedFiles > 0) {
    console.log('\n🚀 Next steps:');
    console.log('   1. Restart your application to use the new JWT secret');
    console.log('   2. Run security validation: npm run validate-security');
    console.log('   3. Test authentication to ensure everything works');
    
    console.log('\n⚠️  Important:');
    console.log('   • Keep this secret secure and never commit it to version control');
    console.log('   • All existing user sessions will be invalidated');
    console.log('   • Users will need to log in again');
  }
  
  console.log('\n✅ JWT secret generation complete!');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { generateSecureSecret, updateEnvFile };
