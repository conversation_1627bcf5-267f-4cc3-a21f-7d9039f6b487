const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const db = require('../models');

// @route   GET api/company-types
// @desc    Get all company types
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Attempt to query company types from the database
    let companyTypes = await db.companyType.findAll({
      where: { is_active: true },
      attributes: ['id', 'name', 'code', 'description'],
      order: [['name', 'ASC']]
    });
    
    // If no company types found in the database, return default types
    if (!companyTypes || companyTypes.length === 0) {
      console.log('No company types found in database. Returning default types.');
      
      // Default company types
      companyTypes = [
        { id: 1, code: 'fire_department', name: 'Fire Department', description: 'Fire and rescue services' },
        { id: 2, code: 'police_department', name: 'Police Department', description: 'Law enforcement agencies' },
        { id: 3, code: 'emergency_services', name: 'Emergency Services', description: 'First responders and emergency management' },
        { id: 4, code: 'government', name: 'Government Agency', description: 'Local, state, or federal government organizations' },
        { id: 5, code: 'private_security', name: 'Private Security', description: 'Security companies and private security firms' },
        { id: 6, code: 'insurance', name: 'Insurance Company', description: 'Insurance providers and agencies' },
        { id: 7, code: 'other', name: 'Other', description: 'Other organization types' }
      ];
    }
    
    res.json(companyTypes);
  } catch (err) {
    console.error('Error fetching company types:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

module.exports = router;
