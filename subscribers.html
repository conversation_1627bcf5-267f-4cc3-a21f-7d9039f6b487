<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Company Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- Subscriber Stats -->
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Company Statistics</div>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; flex-wrap: wrap; gap: 20px; text-align: center;">
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="active" style="font-size: 48px; font-weight: 700; color: var(--accent-green); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Active Companies</div>
                            </div>
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="pending" style="font-size: 48px; font-weight: 700; color: var(--accent-orange); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Pending</div>
                            </div>
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="total" style="font-size: 48px; font-weight: 700; color: var(--accent-blue); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Total Companies</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Alert Preferences</div>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; flex-wrap: wrap; gap: 20px; text-align: center;">
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="fireAlerts" style="font-size: 48px; font-weight: 700; color: var(--accent-red); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Fire Alerts</div>
                            </div>
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="waterAlerts" style="font-size: 48px; font-weight: 700; color: var(--accent-blue); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Water Alerts</div>
                            </div>
                            <div style="flex: 1; min-width: 120px;">
                                <div class="stat-number" data-stat="customAreas" style="font-size: 48px; font-weight: 700; color: var(--text-secondary); margin-bottom: 10px;">0</div>
                                <div class="stat-label" style="color: var(--text-secondary); font-size: 16px;">Custom Areas</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscribers List -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">Company Management</div>
                    <div class="card-actions">
                        <a href="add-subscriber.html" class="btn btn-primary" id="addSubscriberBtn">
                            <i class="fas fa-plus"></i> Add Company
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Filter Controls -->
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
                        <div style="flex: 1; min-width: 200px;">
                            <input type="text" class="form-control" id="searchSubscribers" placeholder="Search companies..." style="width: 100%;">
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="blocked">Blocked</option>
                            </select>

                            <button class="btn btn-primary" id="filterSubscribersBtn">Filter</button>
                        </div>
                    </div>

                    <!-- Subscribers Table - Desktop/Tablet View -->
                    <div class="table-wrapper table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="company_name">Company Name <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="company_email">Company Email <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="company_phone">Company Phone <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="contact_person">Contact Person <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="users">Users <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="status">Status <i class="fas fa-sort sort-icon"></i></th>
                                    <th style="width: 120px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="subscribers-body">
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Loading subscribers...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="table-mobile-cards" id="subscribers-mobile-cards">
                        <!-- Mobile cards will be populated by JavaScript -->
                    </div>

                    <!-- Pagination -->
                    <div id="subscribers-pagination" class="pagination-container" style="margin-top: 15px; display: flex; justify-content: center;">
                        <!-- Pagination will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Send Notification Card -->
            <div class="card" style="margin-top: 20px;">
                <div class="card-header">
                    <div class="card-title">Send Notification to Company Subscribers</div>
                </div>
                <div class="card-content">
                    <form id="notificationForm">
                        <div class="form-group">
                            <label class="form-label">Notification Type</label>
                            <select class="form-control" name="notification_type">
                                <option value="all">All Subscribers</option>
                                <option value="fire">Fire Alert Subscribers</option>
                                <option value="water">Water Alert Subscribers</option>
                                <option value="custom">Custom Selection</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Subject</label>
                            <input type="text" class="form-control" name="subject" placeholder="Notification subject...">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" name="message" rows="4" placeholder="Type your message here..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-check">
                                <input type="checkbox" name="include_map">
                                <span style="margin-left: 10px;">Include map of affected area</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" id="sendNotificationBtn">
                                <i class="fas fa-paper-plane"></i> Send Notification
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Confirmation Modal -->
    <div id="confirmationModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="confirmationModalTitle">Confirm Deletion</h3>
                    <button type="button" class="close-modal" onclick="hideConfirmationModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <i class="fas fa-exclamation-triangle" style="color: var(--accent-orange); font-size: 24px; margin-right: 15px;"></i>
                        <div>
                            <p style="margin: 0; font-weight: 500;">Are you sure you want to delete this company?</p>
                            <p id="confirmationCompanyName" style="margin: 5px 0 0 0; color: var(--text-secondary); font-size: 14px;"></p>
                        </div>
                    </div>
                    <div style="background-color: rgba(229, 57, 53, 0.1); border: 1px solid var(--accent-red); border-radius: 4px; padding: 10px; margin-top: 15px;">
                        <p style="margin: 0; color: var(--accent-red); font-size: 13px;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            This action cannot be undone. All associated data will be permanently removed.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideConfirmationModal()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash-alt"></i> Delete Company
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        // Variables for pagination and sorting
        let currentPage = 1;
        let subscribersPerPage = 10;
        let allSubscribers = [];
        let currentSortColumn = 'company_name';
        let currentSortOrder = 'ASC';
        let totalPages = 1;

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('subscribers');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Add click event listeners to the sort icons only (not entire headers)
            const sortIcons = document.querySelectorAll('.sort-icon');
            sortIcons.forEach(icon => {
                icon.addEventListener('click', function(e) {
                    // Stop event from propagating to parent elements
                    e.stopPropagation();

                    // Get the column name from the parent header
                    const header = this.closest('th');
                    const column = header.getAttribute('data-sort');

                    // If clicking the same column, toggle the sort order
                    if (column === currentSortColumn) {
                        currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
                    } else {
                        // New column, default to ascending
                        currentSortColumn = column;
                        currentSortOrder = 'ASC';
                    }

                    // Update sort indicators in the UI
                    updateSortIndicators();

                    // Reset to page 1 when changing sort
                    currentPage = 1;

                    // Load subscribers with new sort
                    loadSubscribers();
                });
            });

            // Remove the cursor pointer from headers since only icons are clickable now
            const headers = document.querySelectorAll('.data-table th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'default';
            });

            // Load subscribers from API
            loadSubscribers();

            // Load subscriber statistics
            loadSubscriberStats();

            // Set up filter functionality
            document.getElementById('filterSubscribersBtn').addEventListener('click', function(e) {
                e.preventDefault();
                currentPage = 1; // Reset to page 1 when filtering
                loadSubscribers();
            });

            // Set up notification form
            document.getElementById('notificationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                sendBulkNotification();
            });

            // Set up search functionality
            document.getElementById('searchSubscribers').addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    currentPage = 1; // Reset to page 1 when searching
                    loadSubscribers();
                }
            });

            // Add window resize listener for responsive table/card switching
            window.addEventListener('resize', function() {
                // Debounce resize events
                clearTimeout(window.resizeTimeout);
                window.resizeTimeout = setTimeout(function() {
                    // Force re-render of table/cards based on current window size
                    if (allSubscribers && allSubscribers.length > 0) {
                        updateSubscribersTable(allSubscribers);
                    }
                }, 150);
            });
        });

        // Update sort indicators in the table headers
        function updateSortIndicators() {
            const headers = document.querySelectorAll('.data-table th[data-sort]');

            // Remove all existing sort indicators
            headers.forEach(header => {
                const iconElement = header.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = 'fas fa-sort sort-icon';
                }
            });

            // Add the appropriate sort indicator to the current sort column
            const activeHeader = document.querySelector(`.data-table th[data-sort="${currentSortColumn}"]`);
            if (activeHeader) {
                const iconElement = activeHeader.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = currentSortOrder === 'ASC' ? 'fas fa-sort-up sort-icon' : 'fas fa-sort-down sort-icon';
                }
            }
        }

        function loadSubscribers() {
            const searchQuery = document.getElementById('searchSubscribers').value.trim();
            const statusFilter = document.getElementById('statusFilter').value;

            // Show loading indicator with dark background to prevent flicker
            const tableBody = document.getElementById('subscribers-body');
            tableBody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="7" style="text-align: center; padding: 20px; background-color: transparent !important; color: #f5f5f5;"><i class="fas fa-spinner fa-spin" style="color: #1e88e5;"></i> Loading subscribers...</td></tr>';

            // Prepare filter parameters
            const params = {
                page: currentPage,
                limit: subscribersPerPage,
                sortBy: currentSortColumn,
                sortOrder: currentSortOrder
            };

            if (searchQuery) params.search = searchQuery;
            if (statusFilter) params.status = statusFilter;

            API.companies.getAll(params)
                .then(response => {
                    console.log("API Response:", response);
                    if (!response || response.success === false) {
                        showNotification('Failed to load subscribers', 'error');
                        tableBody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red); background-color: transparent !important;">Error loading subscribers. Please try again.</td></tr>';
                        return;
                    }

                    // Handle different response formats
                    let subscribers;
                    let totalCount = 0;
                    if (Array.isArray(response)) {
                        // Direct array response
                        subscribers = response;
                        totalCount = response.length;
                        allSubscribers = response;
                    } else if (response.data && Array.isArray(response.data)) {
                        // Response format: { data: [...], success, message }
                        subscribers = response.data;
                        totalCount = response.totalCount || subscribers.length;
                        totalPages = response.totalPages || Math.ceil(totalCount / subscribersPerPage);
                        allSubscribers = subscribers;
                    } else if (response.subscribers && Array.isArray(response.subscribers)) {
                        // API format: { total, page, pageSize, totalPages, subscribers: [...] }
                        subscribers = response.subscribers;
                        totalCount = response.total || subscribers.length;
                        totalPages = response.totalPages || Math.ceil(totalCount / subscribersPerPage);
                        allSubscribers = subscribers;
                    } else {
                        showNotification('Unexpected response format', 'error');
                        console.error('Unexpected response format:', response);
                        tableBody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red); background-color: transparent !important;">Invalid data format received.</td></tr>';
                        return;
                    }

                    // Calculate total pages if not provided by API
                    if (!totalPages) {
                        totalPages = Math.ceil(totalCount / subscribersPerPage);
                    }

                    // Update sort indicators to reflect current sort
                    updateSortIndicators();

                    // Display subscribers
                    updateSubscribersTable(subscribers);

                    // Create pagination
                    createPaginationControls(totalPages);
                })
                .catch(error => {
                    console.error('Error loading subscribers:', error);
                    showNotification('Error connecting to server', 'error');
                    const tableBody = document.getElementById('subscribers-body');
                    tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px; color: var(--accent-red);">Could not connect to server. Please try again later.</td></tr>';
                });
        }

        // Function to create pagination controls
        function createPaginationControls(totalPages) {
            const paginationContainer = document.getElementById('subscribers-pagination');

            if (!paginationContainer) return;

            // Clear previous pagination
            paginationContainer.innerHTML = '';

            // Don't show pagination if only one page
            if (totalPages <= 1) return;

            // Create pagination list
            const paginationList = document.createElement('ul');
            paginationList.className = 'pagination';

            // Previous button
            const prevLi = document.createElement('li');
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    loadSubscribers();
                }
            });
            prevLi.appendChild(prevLink);
            if (currentPage === 1) prevLi.className = 'disabled';
            paginationList.appendChild(prevLi);

            // Page numbers
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust if we're near the end
            if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page button if not visible
            if (startPage > 1) {
                const firstLi = document.createElement('li');
                const firstLink = document.createElement('a');
                firstLink.href = '#';
                firstLink.textContent = '1';
                firstLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = 1;
                    loadSubscribers();
                });
                firstLi.appendChild(firstLink);
                paginationList.appendChild(firstLi);

                // Ellipsis if needed
                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }
            }

            // Page buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                if (i === currentPage) pageLi.className = 'active';

                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = i;
                    loadSubscribers();
                });
                pageLi.appendChild(pageLink);
                paginationList.appendChild(pageLi);
            }

            // Last page button if not visible
            if (endPage < totalPages) {
                // Ellipsis if needed
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                const lastLink = document.createElement('a');
                lastLink.href = '#';
                lastLink.textContent = totalPages;
                lastLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = totalPages;
                    loadSubscribers();
                });
                lastLi.appendChild(lastLink);
                paginationList.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    loadSubscribers();
                }
            });
            nextLi.appendChild(nextLink);
            if (currentPage === totalPages) nextLi.className = 'disabled';
            paginationList.appendChild(nextLi);

            paginationContainer.appendChild(paginationList);
        }

        function updateSubscribersTable(subscribers) {
            const tableBody = document.getElementById('subscribers-body');
            const mobileCards = document.getElementById('subscribers-mobile-cards');

            if (!subscribers || subscribers.length === 0) {
                // Handle empty state for table
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">No subscribers found. Try adjusting your filters.</td></tr>';

                // Handle empty state for mobile cards
                if (mobileCards) {
                    mobileCards.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>No companies found. Try adjusting your filters.</p>
                        </div>
                    `;
                }

                // Add empty rows to maintain consistent height
                for (let i = 0; i < subscribersPerPage - 1; i++) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.className = 'empty-row';
                    emptyRow.innerHTML = '<td colspan="7">&nbsp;</td>';
                    tableBody.appendChild(emptyRow);
                }
                return;
            }

            // Clear previous content
            tableBody.innerHTML = '';
            if (mobileCards) {
                mobileCards.innerHTML = '';
            }

            // Display subscribers
            subscribers.forEach(subscriber => {
                // Format user count with styling
                const userCount = subscriber.userCount || 0;
                const userCountDisplay = `<span class="user-count-badge" data-count="${userCount}">${userCount}</span>`;

                // Format status badge
                let statusBadge = '<span class="status-badge status-unknown">Unknown</span>';
                if (subscriber.status !== undefined && subscriber.status !== null && subscriber.status !== '') {
                    let statusName = subscriber.status;
                    // Convert boolean or numeric status to string
                    if (typeof statusName === 'boolean') {
                        statusName = statusName ? 'Active' : 'Inactive';
                    } else if (typeof statusName === 'number') {
                        statusName = statusName === 1 ? 'Active' : 'Inactive';
                    }
                    const statusClass = String(statusName).toLowerCase();
                    statusBadge = `<span class="status-badge status-${statusClass}">${statusName}</span>`;
                }

                // Format join date
                let formattedDate = 'N/A';
                if (subscriber.createdAt) {
                    const date = new Date(subscriber.createdAt);
                    formattedDate = date.toLocaleDateString();
                }

                // Create table row for desktop/tablet
                const row = document.createElement('tr');
                let contactPerson = 'N/A';
                if (subscriber.company) {
                    if (subscriber.company.contactPerson) {
                        contactPerson = subscriber.company.contactPerson;
                    } else if (subscriber.company.users && subscriber.company.users.length > 0) {
                        // Use the first company_admin's name
                        const admin = subscriber.company.users[0];
                        contactPerson = `${admin.firstName || ''} ${admin.lastName || ''}`.trim() || 'N/A';
                    }
                }
                row.innerHTML = `
                    <td data-column="company_name">${subscriber.company ? subscriber.company.name : 'No Company Assigned'}</td>
                    <td data-column="company_email">${subscriber.company ? (subscriber.company.email || 'N/A') : 'N/A'}</td>
                    <td data-column="company_phone">${subscriber.company ? (subscriber.company.phone || 'N/A') : 'N/A'}</td>
                    <td data-column="contact_person">${contactPerson}</td>
                    <td data-column="users">${userCountDisplay}</td>
                    <td data-column="status">${statusBadge}</td>
                    <td>
                        <div class="incident-actions">
                            <a href="subscriber-details.html?id=${subscriber.id}&mode=view" class="btn-icon" data-tooltip="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="btn-icon delete-subscriber" data-id="${subscriber.id}" data-tooltip="Delete">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);

                // Create mobile card
                if (mobileCards) {
                    const card = document.createElement('div');
                    card.className = 'table-card';
                    card.innerHTML = `
                        <div class="table-card-header">
                            <div class="table-card-title">${subscriber.company ? subscriber.company.name : 'No Company Assigned'}</div>
                            <div class="table-card-meta">
                                ${statusBadge}
                            </div>
                        </div>
                        <div class="table-card-content">
                            <div class="table-card-field">
                                <div class="table-card-label">Email</div>
                                <div class="table-card-value">${subscriber.company ? (subscriber.company.email || 'N/A') : 'N/A'}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Phone</div>
                                <div class="table-card-value">${subscriber.company ? (subscriber.company.phone || 'N/A') : 'N/A'}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Contact Person</div>
                                <div class="table-card-value">${contactPerson}</div>
                            </div>
                            <div class="table-card-field">
                                <div class="table-card-label">Users</div>
                                <div class="table-card-value">${userCountDisplay}</div>
                            </div>
                        </div>
                        <div class="table-card-actions">
                            <a href="subscriber-details.html?id=${subscriber.id}&mode=view" class="btn-icon btn-icon-mobile" data-tooltip="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="btn-icon btn-icon-mobile delete-subscriber" data-id="${subscriber.id}" data-tooltip="Delete">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    `;
                    mobileCards.appendChild(card);
                }
            });

            // Add empty rows to maintain consistent height if needed
            if (subscribers.length < subscribersPerPage) {
                for (let i = subscribers.length; i < subscribersPerPage; i++) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.className = 'empty-row';
                    emptyRow.innerHTML = '<td colspan="7">&nbsp;</td>';
                    tableBody.appendChild(emptyRow);
                }
            }

            // Add event listeners for delete buttons using event delegation to avoid duplicates
            // Remove any existing event listeners first
            document.removeEventListener('click', handleDeleteButtonClick);
            document.addEventListener('click', handleDeleteButtonClick);
        }

        // Event delegation handler for delete buttons
        function handleDeleteButtonClick(event) {
            if (event.target.closest('.delete-subscriber')) {
                const button = event.target.closest('.delete-subscriber');
                const subscriberId = button.getAttribute('data-id');
                if (subscriberId) {
                    // Find the company name from the table row or mobile card
                    let companyName = 'Unknown Company';

                    // Check if it's in a table row
                    const row = button.closest('tr');
                    if (row) {
                        const companyNameCell = row.querySelector('td[data-column="company_name"]');
                        companyName = companyNameCell ? companyNameCell.textContent.trim() : 'Unknown Company';
                    } else {
                        // Check if it's in a mobile card
                        const card = button.closest('.table-card');
                        if (card) {
                            const titleElement = card.querySelector('.table-card-title');
                            if (titleElement) {
                                companyName = titleElement.textContent.trim() || 'Unknown Company';
                            }
                        }
                    }

                    showConfirmationModal(subscriberId, companyName);
                }
            }
        }

        function loadSubscriberStats() {
            API.companies.getStats()
                .then(response => {
                    if (!response || response.success === false) {
                        console.error('Failed to load subscriber statistics');
                        return;
                    }

                    const stats = response.data || response;

                    // Function to update stat with consistent styling
                    function updateStat(selector, value) {
                        const element = document.querySelector(selector);
                        if (element) {
                            element.textContent = value || '0';
                            // Explicitly set styles to ensure consistency regardless of value
                            element.style.fontSize = '48px';
                            element.style.fontWeight = '700';
                            element.style.lineHeight = '1.2';
                            element.style.marginBottom = '10px';
                        }
                    }

                    // Update statistics using data attributes for more reliable selection
                    updateStat('.stat-number[data-stat="active"]', stats.activeCount || '0');
                    updateStat('.stat-number[data-stat="pending"]', stats.pendingCount || '0');
                    updateStat('.stat-number[data-stat="total"]', stats.totalCount || '0');

                    // Update preference statistics
                    updateStat('.stat-number[data-stat="fireAlerts"]', stats.fireAlertsCount || '0');
                    updateStat('.stat-number[data-stat="waterAlerts"]', stats.waterAlertsCount || '0');
                    updateStat('.stat-number[data-stat="customAreas"]', stats.customAreasCount || '0');

                    // Additional fallback method using the original selectors
                    // in case the data attributes aren't working correctly
                    const activeCount = document.querySelector('.dashboard-grid .card:nth-child(1) .card-content div:nth-child(1) div:nth-child(1)');
                    const pendingCount = document.querySelector('.dashboard-grid .card:nth-child(1) .card-content div:nth-child(2) div:nth-child(1)');
                    const totalCount = document.querySelector('.dashboard-grid .card:nth-child(1) .card-content div:nth-child(3) div:nth-child(1)');

                    if (activeCount) activeCount.style.fontSize = '48px';
                    if (pendingCount) pendingCount.style.fontSize = '48px';
                    if (totalCount) totalCount.style.fontSize = '48px';

                    const fireAlertsCount = document.querySelector('.dashboard-grid .card:nth-child(2) .card-content div:nth-child(1) div:nth-child(1)');
                    const waterAlertsCount = document.querySelector('.dashboard-grid .card:nth-child(2) .card-content div:nth-child(2) div:nth-child(1)');
                    const customAreasCount = document.querySelector('.dashboard-grid .card:nth-child(2) .card-content div:nth-child(3) div:nth-child(1)');

                    if (fireAlertsCount) fireAlertsCount.style.fontSize = '48px';
                    if (waterAlertsCount) waterAlertsCount.style.fontSize = '48px';
                    if (customAreasCount) customAreasCount.style.fontSize = '36px';
                })
                .catch(error => {
                    console.error('Error loading subscriber statistics:', error);
                });
        }

        function deleteSubscriber(id) {
            API.companies.delete(id)
                .then(response => {
                    if (response && response.success !== false) {
                        showNotification('Company deleted successfully', 'success');
                        loadSubscribers(); // Reload the table
                        loadSubscriberStats(); // Update statistics
                    } else {
                        showNotification('Failed to delete company: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error deleting company:', error);
                    showNotification('Error connecting to server', 'error');
                });
        }

        function sendBulkNotification() {
            const form = document.getElementById('notificationForm');
            const formData = new FormData(form);

            const notificationType = formData.get('notification_type');
            const subject = formData.get('subject');
            const message = formData.get('message');
            const includeMap = formData.get('include_map') === 'on';

            if (!subject || !message) {
                showNotification('Please fill in all required fields', 'warning');
                return;
            }

            const sendButton = document.getElementById('sendNotificationBtn');
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            sendButton.disabled = true;

            const payload = {
                notificationType,
                subject,
                message,
                includeMap
            };

            API.companies.sendBulkNotification(payload)
                .then(response => {
                    if (response && response.success !== false) {
                        showNotification('Notification sent successfully to ' + (response.recipientCount || 'all') + ' subscribers', 'success');
                        form.reset();
                    } else {
                        showNotification('Failed to send notification: ' + (response?.message || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error sending notification:', error);
                    showNotification('Error connecting to server', 'error');
                })
                .finally(() => {
                    sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> Send Notification';
                    sendButton.disabled = false;
                });
        }

        // Custom confirmation modal functions
        let pendingDeleteId = null;

        function showConfirmationModal(subscriberId, companyName) {
            pendingDeleteId = subscriberId;

            // Update modal content
            document.getElementById('confirmationCompanyName').textContent = `Company: ${companyName}`;

            // Show modal
            const modal = document.getElementById('confirmationModal');
            modal.style.display = 'flex';

            // Add event listener to confirm button
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            confirmBtn.onclick = function() {
                confirmDelete();
            };

            // Add backdrop click listener
            const backdrop = modal.querySelector('.modal-backdrop');
            backdrop.onclick = function() {
                hideConfirmationModal();
            };

            // Prevent modal content clicks from closing modal
            const modalContent = modal.querySelector('.modal-content');
            modalContent.onclick = function(e) {
                e.stopPropagation();
            };

            // Add escape key listener
            document.addEventListener('keydown', handleModalEscape);
        }

        function hideConfirmationModal() {
            const modal = document.getElementById('confirmationModal');
            modal.style.display = 'none';
            pendingDeleteId = null;

            // Remove escape key listener
            document.removeEventListener('keydown', handleModalEscape);
        }

        function handleModalEscape(event) {
            if (event.key === 'Escape') {
                hideConfirmationModal();
            }
        }

        function confirmDelete() {
            if (pendingDeleteId) {
                const confirmBtn = document.getElementById('confirmDeleteBtn');

                // Show loading state
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
                confirmBtn.disabled = true;

                // Perform deletion
                deleteSubscriber(pendingDeleteId);

                // Hide modal
                hideConfirmationModal();

                // Reset button state
                setTimeout(() => {
                    confirmBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Delete Company';
                    confirmBtn.disabled = false;
                }, 1000);
            }
        }

        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }
    </script>
    <style>
        /* Incident table row styling for consistent height */
        .data-table tr {
            height: 62px; /* Fixed height for all table rows */
            line-height: 1.5;
            transition: background-color 0.2s;
        }

        /* User count badge styling */
        .user-count-badge {
            display: inline-block;
            background-color: var(--accent-blue);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            min-width: 24px;
            text-align: center;
            line-height: 1.2;
        }

        /* Different colors based on user count */
        .user-count-badge[data-count="0"] {
            background-color: var(--text-secondary);
        }

        .user-count-badge[data-count="1"] {
            background-color: var(--accent-blue);
        }

        .data-table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Maximum width for cells to prevent extreme stretching */
            vertical-align: middle; /* Center content vertically */
            padding: 12px 15px; /* Consistent padding */
        }

        /* For columns that might have long text */
        .data-table td[data-column="email"],
        .data-table td[data-column="name"] {
            max-width: 200px; /* Allow more space for these fields */
        }

        /* Empty row styling for consistent table height */
        .data-table tr.empty-row {
            height: 62px; /* Match the height of rows with content */
            background-color: transparent;
        }

        .data-table tr.empty-row td {
            border-top: 1px solid rgba(230, 230, 230, 0.3);
            border-bottom: none;
        }

        /* Sort icon styling */
        .sort-icon {
            margin-left: 5px;
            cursor: pointer;
            display: inline-block;
            transition: color 0.2s;
        }

        .sort-icon:hover {
            color: var(--accent-primary);
        }



        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1050;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(2px);
            animation: modalFadeIn 0.3s ease;
        }

        .modal-dialog {
            width: 100%;
            max-width: 500px;
            margin: 20px;
            position: relative;
            z-index: 1051;
            animation: modalSlideIn 0.3s ease;
        }

        .modal-content {
            background-color: var(--secondary-dark);
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
            border: 1px solid var(--border-color);
        }

        .modal-header {
            padding: 20px 25px 15px 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-light);
        }

        .modal-body {
            padding: 25px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 25px 20px 25px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 0 0 8px 8px;
        }

        .close-modal {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 5px;
            font-size: 18px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close-modal:hover {
            color: var(--text-light);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1049;
        }

        /* Modal animations */
        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Ensure consistent stats display, especially for zero values */
        .stat-number {
            font-size: 48px !important;
            font-weight: 700 !important;
            line-height: 1.2 !important;
            min-height: 48px !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin-bottom: 10px !important;
        }

        /* Anti-flicker table styling */
        .table-wrapper {
            background-color: var(--secondary-dark) !important;
        }

        .data-table {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody tr {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody td {
            background-color: transparent !important;
        }

        /* Preserve hover effects */
        .data-table tbody tr:hover {
            background-color: var(--hover-bg) !important;
        }
    </style>
</body>
</html>
