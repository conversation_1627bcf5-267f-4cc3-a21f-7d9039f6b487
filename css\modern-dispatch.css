/* Import utility classes for common patterns */
@import url('utility-classes.css');

:root {
  /* Fire and Water themed color palette */
  --primary-dark: #1a2035;
  --secondary-dark: #272e48;
  --accent-blue: #1e88e5;    /* Water */
  --accent-red: #e53935;      /* Fire */
  --accent-orange: #ff8f00;   /* Warning */
  --accent-green: #28a745;    /* Success */
  --text-light: #f5f5f5;
  --text-secondary: #b8c2cc;
  --border-color: #464d63;
  --hover-bg: #303950;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  background-color: var(--primary-dark);
  color: var(--text-light);
  line-height: 1.6;
}

.main-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
  width: 250px;
  background-color: var(--secondary-dark);
  border-right: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h1 {
  font-size: 1.2rem;
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.3s;
}

.sidebar.collapsed .sidebar-header h1 {
  opacity: 0;
}

.nav-menu {
  margin-top: 20px;
}

.nav-item {
  padding: 12px 15px;
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s;
  position: relative;
}

.nav-item .nav-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background-color: var(--hover-bg);
  color: var(--text-light);
}

.nav-item:hover .nav-indicator {
  background-color: var(--accent-blue);
}

.nav-item.active {
  background-color: rgba(30, 136, 229, 0.1);
  color: var(--text-light);
}

.nav-item.active .nav-indicator {
  background-color: var(--accent-blue);
}

.nav-item i {
  margin-right: 10px;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.nav-item span {
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s;
}

.sidebar.collapsed .nav-item span {
  opacity: 0;
}

/* Submenu */
.submenu {
  margin-left: 30px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.submenu.active {
  max-height: 1000px; /* Large enough value to accommodate all items */
}

.submenu-item {
  padding: 10px;
  display: block;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s;
}

.submenu-item:hover {
  color: var(--text-light);
  background-color: var(--hover-bg);
}

.submenu-item.active {
  color: var(--accent-blue);
}

/* Main Content Area */
.main-content {
  flex: 1;
  margin-left: 250px;
  transition: all 0.3s ease;
  padding: 20px;
  width: calc(100% - 250px);
}

.main-content.expanded {
  margin-left: 60px;
  width: calc(100% - 60px);
}

/* Top navigation bar */
.top-nav {
  background-color: var(--secondary-dark);
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px;
  margin-bottom: 20px;
}

.top-nav .toggle-sidebar {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 24px;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

/* Standardized user avatar icon styling */
.user-avatar-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #e0e0e0;
  color: #616161;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-actions .btn-icon {
  background: rgba(255, 255, 255, 0.1);
}

/* Cards and panels */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background-color: var(--secondary-dark);
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.card-content {
  min-height: 100px;
}

/* Alert system */
.alert {
  padding: 12px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.alert i {
  margin-right: 10px;
  font-size: 20px;
}

.alert-emergency {
  background-color: var(--accent-red);
  color: white;
}

.alert-warning {
  background-color: var(--accent-orange);
  color: white;
}

.alert-info {
  background-color: var(--accent-blue);
  color: white;
}

.alert-success {
  background-color: var(--accent-green);
  color: white;
}

/* Tables */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--text-secondary);
  font-weight: 500;
}

.data-table tbody tr:hover {
  background-color: var(--hover-bg);
}

.table-wrapper {
  overflow-x: auto;
}

/* Status badges now use utility classes from utility-classes.css */
.status-badge {
  /* Inherits from .status-badge-base in utility-classes.css */
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

/* Status colors moved to utility-classes.css */

/* Type badges */
.type-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.type-fire {
  background-color: rgba(229, 57, 53, 0.2);
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
}

.type-water {
  background-color: rgba(30, 136, 229, 0.2);
  color: var(--accent-blue);
  border: 1px solid var(--accent-blue);
}

/* Buttons */
.btn {
  padding: 8px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn i {
  margin-right: 6px;
}

.btn-primary {
  background-color: var(--accent-blue);
  color: white;
}

.btn-success {
  background-color: var(--accent-green);
  color: white;
}

.btn-danger {
  background-color: var(--accent-red);
  color: white;
}

.btn-warning {
  background-color: var(--accent-orange);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-light);
}

.btn-outline:hover {
  background-color: var(--hover-bg);
}

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-icon {
  padding: 8px;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: var(--text-light);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Forms */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-color: var(--secondary-dark);
  color: var(--text-light);
  border: 1px solid var(--border-color);
  appearance: auto;
}

textarea.form-control {
  min-height: 120px;
  resize: vertical;
}

.form-row {
  display: flex;
  margin-left: -10px;
  margin-right: -10px;
  flex-wrap: wrap;
}

.form-col {
  padding-left: 10px;
  padding-right: 10px;
  flex: 1;
}

/* Map container */
.map-container {
  height: 400px;
  background-color: var(--secondary-dark);
  border-radius: 5px;
  overflow: hidden;
}

/* Incident list */
.incident-list {
  list-style: none;
}

.incident-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  align-items: center;
}

.incident-item:last-child {
  border-bottom: none;
}

.incident-icon {
  width: 40px;
  height: 40px;
  background-color: var(--border-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.incident-icon.fire {
  background-color: var(--accent-red);
}

.incident-icon.water {
  background-color: var(--accent-blue);
}

.incident-details {
  margin-bottom: 20px;
}

.incident-details .meta-item {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.incident-details .meta-label {
  width: 140px;
  font-weight: 500;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.incident-details .meta-value {
  flex: 1;
}

.incident-type-selector {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.incident-type-option {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.incident-type-option:hover {
  background-color: var(--hover-bg);
}

.incident-type-option i {
  font-size: 24px;
  margin-bottom: 10px;
}

.incident-type-option.fire i {
  color: var(--accent-red);
}

.incident-type-option.water i {
  color: var(--accent-blue);
}

.incident-type-option.fire.active {
  background-color: rgba(229, 57, 53, 0.1);
  border-color: var(--accent-red);
}

.incident-type-option.water.active {
  background-color: rgba(30, 136, 229, 0.1);
  border-color: var(--accent-blue);
}

.incident-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.incident-image {
  position: relative;
  width: 120px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
}

.incident-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.incident-image .remove-image {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(229, 57, 53, 0.8);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.incident-image .remove-image:hover {
  background-color: rgba(229, 57, 53, 1);
}

.incident-history {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.incident-history-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.incident-history-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.incident-history-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.incident-history-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.incident-history-dot.update {
  background-color: var(--accent-green);
}

.incident-history-dot.create {
  background-color: var(--accent-blue);
}

.incident-history-content {
  flex: 1;
}

.incident-history-time {
  color: var(--text-secondary);
  font-size: 12px;
}

.section-title {
  font-size: 18px;
  margin: 25px 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-light);
}

/* Authentication pages */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--primary-dark);
}

.auth-card {
  width: 400px;
  max-width: 90%;
  background-color: var(--secondary-dark);
  border-radius: 5px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.auth-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.auth-logo {
  max-width: 150px;
  margin-bottom: 10px;
}

.auth-body {
  padding: 20px;
}

.auth-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  text-align: center;
  font-size: 14px;
}

/* Pagination */
.pagination {
  display: flex;
  list-style: none;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
  padding: 0;
}

.pagination li {
  margin: 0 2px;
  display: inline-block;
}

.pagination a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 4px;
  color: var(--text-light);
  text-decoration: none;
  transition: all 0.2s;
  border: 1px solid var(--border-color);
  background-color: var(--secondary-dark);
  font-size: 14px;
  font-weight: 500;
}

.pagination a:hover {
  background-color: var(--hover-bg);
  border-color: var(--accent-blue);
  transform: translateY(-1px);
}

.pagination .active a {
  background-color: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.pagination a:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.3);
}

/* Disabled pagination buttons */
.pagination a[style*="pointer-events: none"] {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: var(--border-color);
}

.pagination a[style*="pointer-events: none"]:hover {
  background-color: var(--secondary-dark);
  transform: none;
  border-color: var(--border-color);
}

/* Quick Actions Styling */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.quick-action-btn {
  padding: 15px 10px;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70px;
}

.quick-action-btn i {
  margin-bottom: 6px;
  font-size: 1.1em;
}

.quick-action-btn span {
  font-size: 0.85em;
  font-weight: 500;
}

/* System Status Styling */
.system-status-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.status-item {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.status-item.last-item {
  border-bottom: none;
}

.status-label {
  display: flex;
  align-items: center;
}

.status-icon {
  margin-right: 10px;
  width: 16px;
  text-align: center;
}

.status-value {
  font-weight: 500;
}

.status-operational .status-icon {
  color: var(--accent-green);
}

.status-operational .status-value {
  color: var(--accent-green);
}

.status-warning .status-icon {
  color: var(--accent-orange);
}

.status-warning .status-value {
  color: var(--accent-orange);
}

.status-error .status-icon {
  color: var(--accent-red);
}

.status-error .status-value {
  color: var(--accent-red);
}

.status-unknown .status-icon {
  color: var(--text-secondary);
}

.status-unknown .status-value {
  color: var(--text-secondary);
}

/* ===== RESPONSIVE TABLE UTILITIES ===== */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: 8px;
  box-shadow: inset 0 0 0 1px var(--border-color);
}

.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: var(--primary-dark);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Mobile table card layout */
.table-mobile-cards {
  display: none;
}

.table-card {
  background-color: var(--secondary-dark);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.2s ease;
}

.table-card:hover {
  background-color: var(--hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.table-card-title {
  font-weight: 600;
  color: var(--text-light);
  font-size: 16px;
  line-height: 1.3;
}

.table-card-meta {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: right;
}

.table-card-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 15px;
  margin-bottom: 12px;
}

.table-card-field {
  display: flex;
  flex-direction: column;
}

.table-card-label {
  font-size: 11px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
  font-weight: 500;
}

.table-card-value {
  font-size: 14px;
  color: var(--text-light);
  line-height: 1.3;
}

.table-card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid var(--border-color);
}

/* Touch-friendly action buttons */
.btn-icon-mobile {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

/* Media queries for responsiveness */
@media (max-width: 992px) {
  .sidebar {
    width: 60px;
  }

  .sidebar-header h1,
  .nav-item span {
    opacity: 0;
  }

  .main-content {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Responsive pagination for tablets */
  .pagination {
    margin: 15px 0;
  }

  .pagination a {
    width: 32px;
    height: 32px;
    font-size: 13px;
  }
}

/* Half-screen desktop breakpoint for better table handling */
@media (max-width: 960px) {
  /* Enable horizontal scroll for tables */
  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .data-table {
    min-width: 800px; /* Ensure table doesn't get too cramped */
  }

  /* Optimize form layouts */
  .form-row {
    flex-wrap: wrap;
  }

  .form-col {
    min-width: 280px;
  }

  /* Improve button spacing */
  .card-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  /* Table responsiveness - show horizontal scroll */
  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    box-shadow: inset 0 0 0 1px var(--border-color);
  }

  .data-table {
    min-width: 700px;
  }

  /* Form optimizations */
  .incident-type-selector {
    flex-direction: column;
    gap: 10px;
  }

  .incident-details .meta-item {
    flex-direction: column;
  }

  .incident-details .meta-label {
    width: 100%;
    margin-bottom: 5px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-col {
    width: 100%;
    margin-bottom: 15px;
  }

  /* Touch-friendly buttons */
  .btn {
    min-height: 44px;
    padding: 12px 20px;
  }

  .btn-icon {
    min-width: 44px;
    min-height: 44px;
  }

  /* Improve card spacing */
  .card {
    margin-bottom: 20px;
  }

  .card-content {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  /* Mobile navigation */
  .sidebar {
    width: 0;
    transform: translateX(-100%);
    transition: all 0.3s ease;
  }

  .sidebar.mobile-open {
    width: 280px;
    transform: translateX(0);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }

  .sidebar.mobile-open .sidebar-header h1,
  .sidebar.mobile-open .nav-item span {
    opacity: 1;
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .top-nav {
    position: sticky;
    top: 0;
    z-index: 999;
    padding: 10px 15px;
  }

  /* Show hamburger menu button */
  .toggle-sidebar {
    display: block !important;
    min-width: 44px;
    min-height: 44px;
  }

  /* Mobile table cards */
  .table-wrapper {
    display: none;
  }

  .table-mobile-cards {
    display: block;
  }

  /* Single column card content on very small screens */
  .table-card-content {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .incident-meta {
    flex-direction: column;
  }

  .incident-meta span {
    margin-bottom: 5px;
  }

  /* Mobile pagination adjustments */
  .pagination {
    margin: 10px 0;
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination li {
    margin: 2px;
  }

  .pagination a {
    width: 36px;
    height: 36px;
    font-size: 14px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Hide some page numbers on mobile to prevent overflow */
  .pagination li:nth-child(n+6):nth-last-child(n+3) {
    display: none;
  }

  /* Mobile form optimizations */
  .form-control {
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .form-label {
    font-size: 14px;
    margin-bottom: 8px;
  }

  /* Mobile modal adjustments */
  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
    overflow-y: auto;
    width: calc(100% - 20px);
  }

  .modal-dialog {
    margin: 10px;
    max-width: none;
    width: calc(100% - 20px);
  }

  .modal-header {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .modal-body {
    padding: 15px;
  }

  .modal-footer {
    padding: 15px;
    flex-direction: column;
    gap: 10px;
  }

  .modal-footer .btn {
    width: 100%;
    min-height: 44px;
  }

  /* Mobile card adjustments */
  .card {
    margin: 10px;
    border-radius: 12px;
  }

  .card-header {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .card-actions {
    width: 100%;
    justify-content: stretch;
  }

  .card-actions .btn {
    flex: 1;
  }

  /* Typography optimizations for mobile */
  h1 {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.3rem;
    line-height: 1.3;
  }

  h4 {
    font-size: 1.1rem;
    line-height: 1.3;
  }

  p {
    font-size: 14px;
    line-height: 1.5;
  }

  /* Improve touch targets for all interactive elements */
  a, button, input, select, textarea {
    min-height: 44px;
  }

  /* Better spacing for mobile */
  .card-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  /* Improve readability */
  .text-small {
    font-size: 12px;
  }

  .text-large {
    font-size: 16px;
  }
}

/* ===== MAP RESPONSIVE STYLES ===== */
.map-container {
  height: calc(100vh - 200px);
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.map-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background-color: var(--secondary-dark);
  border-left: 1px solid var(--border-color);
  z-index: 1000;
  overflow-y: auto;
  transition: transform 0.3s ease;
}

.incident-mini {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 44px; /* Touch-friendly */
  display: flex;
  align-items: center;
}

.incident-mini:hover {
  background-color: var(--hover-bg);
}

.incident-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.incident-icon.fire {
  background-color: var(--accent-red);
}

.incident-icon.water {
  background-color: var(--accent-blue);
}

.map-filter-toggle {
  display: none;
  position: fixed;
  top: 120px;
  right: 20px;
  z-index: 1001;
  background-color: var(--secondary-dark);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  min-width: 44px;
  min-height: 44px;
}

/* Map responsive styles */
@media (max-width: 768px) {
  .map-container {
    height: calc(100vh - 160px);
  }

  .map-sidebar {
    transform: translateX(100%);
    width: 100%;
    max-width: 350px;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
  }

  .map-sidebar.active {
    transform: translateX(0);
  }

  .map-filter-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Make filter buttons stack on mobile */
  .card-actions > div {
    flex-wrap: wrap;
    gap: 5px;
  }

  .card-actions .btn {
    min-height: 44px;
    padding: 8px 12px;
  }

  /* Improve popup styling on mobile */
  .leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
  }

  .leaflet-popup-content .btn {
    min-height: 44px;
    padding: 8px 16px;
    margin-top: 8px;
  }
}

@media (max-width: 576px) {
  .map-container {
    height: calc(100vh - 140px);
    border-radius: 0;
    margin: 0 -15px;
  }

  .map-filter-toggle {
    top: 100px;
    right: 15px;
    width: 44px;
    height: 44px;
  }

  .card-actions > div {
    flex-direction: column;
    width: 100%;
  }

  .card-actions .btn {
    width: 100%;
    justify-content: center;
    margin-bottom: 5px;
  }

  .incident-mini {
    padding: 15px;
    min-height: 60px;
  }

  .incident-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}
