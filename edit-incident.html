<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Edit Incident</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <link rel="stylesheet" href="css/utility-classes.css">
    <link rel="stylesheet" href="css/fixes.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <style>
        .pac-container {
            z-index: 1051 !important;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            background-color: var(--secondary-dark);
            color: var(--text-light);
        }

        .pac-item {
            border-color: var(--border-color);
            padding: 8px;
            cursor: pointer;
        }

        .pac-item:hover {
            background-color: var(--hover-bg);
        }

        .pac-item-query {
            color: var(--text-light);
        }

        .pac-matched {
            color: var(--accent-blue);
        }

        .pac-icon {
            filter: invert(1);
        }

        .owner-info-card {
            background-color: rgba(30, 136, 229, 0.1);
            border: 1px solid var(--accent-blue);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .incident-images {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }

        .incident-image {
            position: relative;
            width: 120px;
            height: 80px;
            border-radius: 4px;
            overflow: hidden;
        }

        .incident-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .incident-image .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(229, 57, 53, 0.8);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
        }

        .section-subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
            margin: 20px 0 15px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 500;
        }

        .fire-specific-section, .water-specific-section {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        /* Page Header Styling - Matching add incident page */
        .page-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .page-header h1 {
            font-size: 1.8rem;
            margin-bottom: 8px;
            color: var(--text-light);
            display: flex;
            align-items: center;
        }

        .page-header h1 i {
            color: var(--accent-blue);
            margin-right: 12px;
        }

        .page-header p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 1rem;
        }

        /* Section Card Styling */
        .section-card {
            margin-bottom: 25px;
        }

        .section-card .card-header {
            position: relative;
        }

        .section-status {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .section-status i {
            font-size: 12px;
        }

        .section-status .incomplete {
            color: var(--text-secondary);
        }

        .section-status .complete {
            color: var(--accent-green);
        }

        .section-actions {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Incident Type Display for Edit Mode */
        .incident-type-display {
            margin-top: 10px;
        }

        .incident-type-badge {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 15px 20px;
            text-align: center;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
        }

        .incident-type-badge.fire {
            border-color: var(--accent-red);
            background: rgba(229, 57, 53, 0.1);
        }

        .incident-type-badge.water {
            border-color: var(--accent-blue);
            background: rgba(30, 136, 229, 0.1);
        }

        .incident-type-badge i {
            font-size: 1.2rem;
        }

        .incident-type-badge.fire i {
            color: var(--accent-red);
        }

        .incident-type-badge.water i {
            color: var(--accent-blue);
        }

        .incident-type-badge span {
            font-weight: 500;
            color: var(--text-light);
        }

        /* Button Styling */
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .section-status,
            .section-actions {
                position: static;
                transform: none;
                margin-top: 10px;
                text-align: right;
            }

            .incident-type-badge {
                min-width: auto;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-edit"></i> Edit Incident #<span id="incident-id">Loading...</span></h1>
                <p>Update incident information and track changes</p>
            </div>

            <!-- Edit Incident Form -->
            <form id="editIncidentForm" action="#" method="post" data-validate="true">
                <input type="hidden" id="incident_type" name="incident_type" value="fire">

                <!-- Incident History Section -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-history" style="margin-right: 10px; color: var(--text-secondary);"></i>
                            Incident History
                        </div>
                        <div class="card-actions">
                            <button type="button" id="toggleHistoryBtn" class="btn btn-outline btn-sm">
                                <i class="fas fa-eye"></i> Show History
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="incident-history" class="incident-history" style="display: none;">
                            <h4 class="incident-history-title">Incident History</h4>
                            <ul class="incident-history-list" id="incident-history-list">
                                <!-- History items will be dynamically populated -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Section 1: Incident Type -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i id="incident-icon" class="fas fa-fire" style="color: var(--accent-red); margin-right: 10px;"></i>
                            Incident Type
                        </div>
                        <div class="section-status" id="type-status">
                            <i class="fas fa-circle complete" style="color: var(--accent-green);"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <!-- Incident type display (read-only in edit mode) -->
                        <div class="form-group">
                            <label class="form-label">Current Incident Type</label>
                            <div class="incident-type-display" id="incident-type-display">
                                <div class="incident-type-badge fire" id="incident-type-badge">
                                    <i class="fas fa-fire"></i>
                                    <span id="incident-type-text">Fire Incident</span>
                                </div>
                            </div>
                        </div>

                        <!-- Fire-specific fields -->
                        <div class="fire-specific" id="fire-specific-fields">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Fire Type *</label>
                                        <select class="form-control" name="fire_type" id="fire_type">
                                            <option value="">Select Fire Type</option>
                                            <option value="1">Structure Fire - Residential</option>
                                            <option value="2">Structure Fire - Commercial</option>
                                            <option value="3">Vehicle Fire</option>
                                            <option value="4">Brush/Wildland Fire</option>
                                            <option value="11">Electrical Fire</option>
                                            <option value="12">Chimney Fire</option>
                                            <option value="13">Explosion</option>
                                            <option value="14">Hazardous Material</option>
                                            <option value="15">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Fire Alarm Level *</label>
                                        <select class="form-control" name="alarm_level" id="alarm_level">
                                            <option value="1" selected>Level 1 - Single Engine Response</option>
                                            <option value="2">Level 2 - Multiple Engine Response</option>
                                            <option value="3">Level 3 - Full Assignment Response</option>
                                            <option value="4">Level 4 - Multiple Alarm Response</option>
                                            <option value="5">Level 5 - Major Emergency Response</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Water-specific fields -->
                        <div class="water-specific" id="water-specific-fields" style="display: none;">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Water Incident Type *</label>
                                        <select class="form-control" name="water_type" id="water_type">
                                            <option value="">Select Water Type</option>
                                            <option value="5">Water Main Break</option>
                                            <option value="6">Flooding</option>
                                            <option value="7">Fire Hydrant Damage</option>
                                            <option value="8">Sewer Backup</option>
                                            <option value="9">Water Rescue</option>
                                            <option value="10">Dam/Levee Breach</option>
                                            <option value="16">Water Treatment Issue</option>
                                            <option value="17">Water Supply Contamination</option>
                                            <option value="18">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <!-- Empty for layout balance -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Basic Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-info-circle" style="margin-right: 10px; color: var(--accent-blue);"></i>
                            Basic Information
                        </div>
                        <div class="section-status" id="basic-status">
                            <i class="fas fa-circle complete" style="color: var(--accent-green);"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Incident Title *</label>
                                    <input type="text" class="form-control" name="title" id="title" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Status *</label>
                                    <select class="form-control" name="status" id="status" required>
                                        <option value="">Select Status</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Severity</label>
                                    <select class="form-control" name="severity" id="severity">
                                        <option value="low">Low</option>
                                        <option value="moderate" selected>Moderate</option>
                                        <option value="high">High</option>
                                        <option value="critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Incident Date</label>
                                    <input type="datetime-local" class="form-control" name="incident_date" id="incident_date">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Cross Street</label>
                                    <input type="text" class="form-control" name="cross_street" id="cross_street" placeholder="Nearest cross street">
                                </div>
                            </div>
                            <div class="form-col">
                                <!-- Empty for layout balance -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 3: Location Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-map-marker-alt" style="margin-right: 10px; color: var(--accent-green);"></i>
                            Location Information
                        </div>
                        <div class="section-status" id="location-status">
                            <i class="fas fa-circle complete" style="color: var(--accent-green);"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Search Address *</label>
                            <div style="position: relative;">
                                <input type="text" class="form-control" id="google_address" placeholder="123 Main Street, Springfield">
                                <div id="geocoding-status" style="position: absolute; top: 8px; right: 10px; font-size: 12px; color: var(--text-secondary); display: none;">
                                    <i class="fas fa-map-marker-alt"></i> <span id="geocoding-provider">Loading...</span>
                                </div>
                            </div>
                            <small style="color: var(--text-secondary); display: block; margin-top: 5px;">Type street address and city (e.g., "123 Main St, Springfield") - all location details will auto-populate</small>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Street Address *</label>
                                    <input type="text" class="form-control" name="address" id="address" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">City *</label>
                                    <input type="text" class="form-control" name="city" id="city" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">State *</label>
                                    <select class="form-control" name="state_id" id="state_select" required>
                                        <option value="">Select State</option>
                                    </select>
                                    <input type="hidden" name="state" id="state">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">County *</label>
                                    <select class="form-control" name="county_id" id="county_select" required disabled>
                                        <option value="">Select County</option>
                                    </select>
                                    <input type="hidden" name="county" id="county">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col flex-30">
                                <div class="form-group">
                                    <label class="form-label">ZIP Code</label>
                                    <input type="text" class="form-control" name="zip_code" id="zip_code">
                                </div>
                            </div>
                            <div class="form-col flex-30">
                                <div class="form-group">
                                    <label class="form-label">Building Stories</label>
                                    <input type="number" class="form-control" name="home_stories" id="home_stories" min="1" max="10" placeholder="Number of floors/stories">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Coordinates</label>
                                    <div style="display: flex; gap: 10px;">
                                        <input type="text" class="form-control" name="latitude" id="latitude" placeholder="Latitude" readonly style="flex: 1;">
                                        <input type="text" class="form-control" name="longitude" id="longitude" placeholder="Longitude" readonly style="flex: 1;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 4: Property Owner Information -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-user-circle" style="margin-right: 10px; color: var(--accent-orange);"></i>
                            Property Owner Information
                        </div>
                        <div class="section-actions">
                            <button type="button" class="btn btn-outline btn-sm" id="refreshOwnerDataBtn">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Primary Owner</label>
                                    <input type="text" class="form-control" name="owner1" id="owner1">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Secondary Owner</label>
                                    <input type="text" class="form-control" name="owner2" id="owner2">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Address</label>
                                    <input type="text" class="form-control" name="ownerAddr" id="ownerAddr">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Phone</label>
                                    <input type="text" class="form-control" name="ownerPhone" id="ownerPhone">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Owner Email</label>
                                    <input type="email" class="form-control" name="ownerEmail" id="ownerEmail">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Dwelling Type</label>
                                    <input type="text" class="form-control" name="dwellingType" id="dwellingType">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Year Built</label>
                                    <input type="number" class="form-control" name="yearBuilt" id="yearBuilt" min="1800" max="2030" placeholder="e.g. 1985">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Property Value</label>
                                    <input type="text" class="form-control" name="propertyValue" id="propertyValue" placeholder="e.g. $450,000" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 5: Incident Details -->
                <div class="card section-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-clipboard-list" style="margin-right: 10px; color: var(--text-secondary);"></i>
                            Incident Details
                        </div>
                        <div class="section-status" id="details-status">
                            <i class="fas fa-circle complete" style="color: var(--accent-green);"></i>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Incident Description *</label>
                            <textarea class="form-control" name="updates" id="updates" rows="4" placeholder="Provide detailed information about the incident..."></textarea>
                        </div>

                        <!-- Fire-specific incident details -->
                        <div class="fire-specific-section fire-field">
                            <h4 class="section-subtitle">Fire Incident Details</h4>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Structure Type</label>
                                        <input type="text" class="form-control" name="structure_type" id="structure_type" placeholder="e.g., Single Family Residential">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Smoke Type</label>
                                        <input type="text" class="form-control" name="smoke_type" id="smoke_type" placeholder="e.g., Heavy Black Smoke">
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Smoke Severity</label>
                                        <select class="form-control" name="smoke_severity" id="smoke_severity">
                                            <option value="">Select Smoke Severity</option>
                                            <option value="No Visible Smoke">No Visible Smoke</option>
                                            <option value="Light">Light</option>
                                            <option value="Moderate">Moderate</option>
                                            <option value="Heavy">Heavy</option>
                                            <option value="Dense">Dense</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <!-- Empty for layout balance -->
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">People Affected</label>
                                        <input type="number" class="form-control" name="people_affected" id="people_affected" min="0" value="0">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Evacuation Status</label>
                                        <select class="form-control" name="evacuation_status" id="evacuation_status">
                                            <option value="none">None</option>
                                            <option value="advisory">Advisory</option>
                                            <option value="mandatory">Mandatory</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Estimated Damage ($)</label>
                                        <input type="number" class="form-control" name="estimated_damage" id="estimated_damage" min="0" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Responder Count</label>
                                        <input type="number" class="form-control" name="responder_count" id="responder_count" min="0">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Fire Operation Details</label>
                                <textarea class="form-control" name="fire_op" id="fire_op" rows="3" placeholder="Details about fire suppression operations..."></textarea>
                            </div>
                        </div>

                        <!-- Water-specific incident details -->
                        <div class="water-specific-section water-field" style="display: none;">
                            <h4 class="section-subtitle">Water Incident Details</h4>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Water Level</label>
                                        <input type="text" class="form-control" name="water_level" id="water_level" placeholder="e.g., 2 feet above normal">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Damage Extent</label>
                                        <input type="text" class="form-control" name="damage_extent" id="damage_extent" placeholder="e.g., Basement flooding">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Area Affected</label>
                                <select class="form-control" name="area_affected" id="area_affected">
                                    <option value="">Select Affected Area</option>
                                    <option value="small">Small (Single Location)</option>
                                    <option value="medium">Medium (Multiple Blocks)</option>
                                    <option value="large">Large (Neighborhood)</option>
                                    <option value="widespread">Widespread (Multiple Areas)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Response Details</label>
                            <textarea class="form-control" name="response_details" id="response_details" rows="3" placeholder="Information about responding units, actions taken..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Conditions on Arrival</label>
                            <textarea class="form-control" name="conditions_on_arrival" id="conditions_on_arrival" rows="3" placeholder="Describe the scene conditions when first responders arrived..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-control" name="notes" id="notes" rows="2" placeholder="Any additional information..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Additional Details</label>
                            <textarea class="form-control" name="add_details" id="add_details" rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Dispatch Information</label>
                            <textarea class="form-control" name="dispatch_info" id="dispatch_info" rows="2" placeholder="Dispatch details and unit information..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Quick Dispatch IDs</label>
                            <input type="text" class="form-control" name="dispatch_id" id="dispatch_id" placeholder="Comma separated dispatch IDs">
                        </div>

                        <!-- Current Images -->
                        <div class="form-group">
                            <label class="form-label">Current Images</label>
                            <div class="incident-images" id="current-images">
                                <!-- Current images will be dynamically populated -->
                            </div>
                        </div>

                        <!-- Upload More Images -->
                        <div class="form-group">
                            <label class="form-label">Upload More Images</label>
                            <input type="file" class="form-control" name="images" multiple accept="image/*">
                            <small style="color: var(--text-secondary); display: block; margin-top: 5px;">You can upload multiple images (Max 5MB each)</small>
                        </div>

                        <!-- Notification Options -->
                        <div class="form-group">
                            <label class="form-check">
                                <input type="checkbox" name="notify_subscribers" checked>
                                <span style="margin-left: 10px;">Send update notification to subscribers</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Submit Section -->
                <div class="card">
                    <div class="card-content">
                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <a href="#" class="btn btn-outline" id="cancel-link">Cancel</a>
                            <button type="submit" class="btn btn-primary" id="update-incident-btn">
                                <i class="fas fa-save"></i> Update Incident
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Updating incident...</span>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <!-- API Keys Management Utility -->
    <script src="js/api-keys.js"></script>
    <!-- API Utility -->
    <script src="js/api.js"></script>
    <script>
        let incidentId = null; // Store incident ID globally
        let autocomplete;

        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        // Global geocoding state management
        window.geocodingState = {
            fallbackTimer: null,
            googleMapsAttempted: false,
            fallbackInitialized: false,
            currentProvider: null,
            googleMapsBlocked: false
        };

        // Global Google Maps error handler - must be defined before any Google Maps API loading
        window.gm_authFailure = function() {
            console.error('Google Maps authentication failed - using OpenStreetMap fallback');
            window.geocodingState.googleMapsBlocked = true;
            initializeFallbackGeocoding();
        };

        // Handle other Google Maps errors
        window.addEventListener('error', function(e) {
            if (e.message && (e.message.includes('Google Maps') || e.message.includes('InvalidKeyMapError'))) {
                console.warn('🔄 Google Maps API error detected, switching to OpenStreetMap fallback');
                window.geocodingState.googleMapsBlocked = true;
                if (!window.geocodingState.fallbackInitialized) {
                    initializeFallbackGeocoding();
                }
                // Prevent the error from propagating to avoid console clutter
                e.preventDefault();
                return false;
            }
        });

        // Suppress Google Maps console errors to keep console clean
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('Google Maps') ||
                message.includes('InvalidKeyMapError') ||
                message.includes('gm_authFailure') ||
                message.includes('maps.googleapis.com')) {
                // Suppress Google Maps errors, but log our own message
                console.warn('🔄 Google Maps unavailable, using OpenStreetMap fallback');
                return;
            }
            // Call original console.error for other errors
            originalConsoleError.apply(console, args);
        };

        // Google Maps Autocomplete Integration
        function initAutocomplete() {
            console.log('Google Maps API loaded successfully, initializing autocomplete');

            // Check if Google Maps is blocked or fallback is already initialized
            if (window.geocodingState.googleMapsBlocked) {
                console.log('Google Maps is blocked, skipping initialization');
                return;
            }

            if (window.geocodingState.fallbackInitialized) {
                console.log('Fallback already initialized, skipping Google Maps setup');
                return;
            }

            // Clear the fallback timer since Google Maps loaded successfully
            if (window.geocodingState.fallbackTimer) {
                clearTimeout(window.geocodingState.fallbackTimer);
                window.geocodingState.fallbackTimer = null;
            }

            const addressInput = document.getElementById('google_address');
            if (!addressInput) {
                console.warn('Google address input not found');
                // Initialize fallback geocoding if Google Maps fails
                initializeFallbackGeocoding();
                return;
            }

            try {
                // Try modern PlaceAutocompleteElement first
                if (google.maps.places.PlaceAutocompleteElement) {
                    console.log('Using modern PlaceAutocompleteElement API');
                    const autocompleteElement = new google.maps.places.PlaceAutocompleteElement({
                        inputElement: addressInput,
                        types: ['address'],
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry', 'formatted_address']
                    });

                    autocompleteElement.addListener('place_changed', async () => {
                        const place = await autocompleteElement.getPlace();
                        fillInAddress(place);
                    });
                } else {
                    throw new Error('PlaceAutocompleteElement not available');
                }
            } catch (e) {
                console.log('Using legacy Autocomplete API (PlaceAutocompleteElement not yet available)');

                // Fallback to legacy Autocomplete API
                const autocomplete = new google.maps.places.Autocomplete(
                    addressInput,
                    {
                        types: ['address'],
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry', 'formatted_address']
                    }
                );

                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();
                    fillInAddress(place);
                });
            }

            // Set provider state and show status
            window.geocodingState.currentProvider = 'google';
            updateGeocodingStatus('Google Maps', 'var(--accent-blue)');
            console.log('Google Maps geocoding initialized successfully');
        }

        function updateGeocodingStatus(provider, color) {
            const statusElement = document.getElementById('geocoding-status');
            const providerElement = document.getElementById('geocoding-provider');

            if (statusElement && providerElement) {
                providerElement.textContent = provider;
                statusElement.style.color = color;
                statusElement.style.display = 'block';
            }
        }

        function fillInAddress(place) {
            if (!place || !place.geometry) {
                console.warn('No place geometry available');
                return;
            }

            // Extract coordinates
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();

            console.log('Geocoded coordinates:', lat, lng);

            // Fill coordinate fields
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;

            // Parse address components
            let addressData = {};
            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('street_number')) {
                    addressData.streetNumber = component.long_name;
                } else if (types.includes('route')) {
                    addressData.street = component.long_name;
                } else if (types.includes('locality')) {
                    addressData.city = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                    addressData.state = component.short_name;
                } else if (types.includes('postal_code')) {
                    addressData.zip = component.long_name;
                } else if (types.includes('administrative_area_level_2')) {
                    addressData.county = component.long_name.replace(' County', '');
                }
            });

            // Fill address fields
            if (addressData.streetNumber && addressData.street) {
                document.getElementById('address').value = `${addressData.streetNumber} ${addressData.street}`;
            } else if (place.formatted_address) {
                // Use first part of formatted address as fallback
                const addressParts = place.formatted_address.split(',');
                document.getElementById('address').value = addressParts[0];
            }

            if (addressData.city) {
                document.getElementById('city').value = addressData.city;
            }

            if (addressData.zip) {
                document.getElementById('zip_code').value = addressData.zip;
            }

            // Handle state and county dropdowns
            if (addressData.state) {
                const stateSelect = document.getElementById('state_select');
                const stateHidden = document.getElementById('state');

                if (stateSelect && stateHidden) {
                    // Find and select the state in dropdown
                    for (let i = 0; i < stateSelect.options.length; i++) {
                        if (stateSelect.options[i].dataset.abbr === addressData.state) {
                            stateSelect.selectedIndex = i;
                            stateHidden.value = addressData.state;

                            // Trigger change event to load counties
                            const event = new Event('change');
                            stateSelect.dispatchEvent(event);

                            // Set county after a delay to allow counties to load
                            if (addressData.county) {
                                setTimeout(() => {
                                    const countySelect = document.getElementById('county_select');
                                    const countyHidden = document.getElementById('county');

                                    if (countySelect && countyHidden) {
                                        for (let j = 0; j < countySelect.options.length; j++) {
                                            if (countySelect.options[j].textContent.includes(addressData.county)) {
                                                countySelect.selectedIndex = j;
                                                countyHidden.value = addressData.county;
                                                break;
                                            }
                                        }
                                    }
                                }, 1000);
                            }
                            break;
                        }
                    }
                }
            }

            showNotification('Address details populated successfully!', 'success');
        }

        // Helper functions to set dropdown values
        function setStateDropdownValue(stateAbbr) {
            if (!stateAbbr) return;

            const stateSelect = document.getElementById('state_select');
            const options = stateSelect.options;

            for (let i = 0; i < options.length; i++) {
                if (options[i].dataset.abbr === stateAbbr) {
                    stateSelect.value = options[i].value;
                    // Trigger change event to load counties
                    const event = new Event('change');
                    stateSelect.dispatchEvent(event);
                    break;
                }
            }
        }

        function setCountyDropdownValue(countyName) {
            if (!countyName) return;

            // Wait a bit for counties to load after state selection
            setTimeout(() => {
                const countySelect = document.getElementById('county_select');
                const options = countySelect.options;

                for (let i = 0; i < options.length; i++) {
                    if (options[i].textContent === countyName || options[i].dataset.name === countyName) {
                        countySelect.value = options[i].value;
                        // Trigger change event to update hidden field
                        const event = new Event('change');
                        countySelect.dispatchEvent(event);
                        break;
                    }
                }
            }, 500);
        }

        // OpenStreetMap/Nominatim Fallback Geocoding System
        function initializeFallbackGeocoding() {
            // Prevent duplicate initialization
            if (window.geocodingState.fallbackInitialized) {
                console.log('Fallback geocoding already initialized');
                return;
            }

            console.log('Initializing OpenStreetMap/Nominatim fallback geocoding');
            const addressInput = document.getElementById('google_address');

            if (!addressInput) {
                console.warn('Address input not found for fallback geocoding');
                return;
            }

            // Clear any existing Google Maps autocomplete and prevent interference
            try {
                if (typeof google !== 'undefined' && google.maps && google.maps.places) {
                    // Remove any existing Google Maps autocomplete listeners
                    google.maps.event.clearInstanceListeners(addressInput);
                }

                // Remove any Google Maps related attributes or classes
                addressInput.removeAttribute('data-gm-autocomplete');
                addressInput.classList.remove('pac-target-input');

                // Clear any existing autocomplete containers
                const existingContainers = document.querySelectorAll('.pac-container');
                existingContainers.forEach(container => container.remove());

            } catch (e) {
                console.log('Cleared Google Maps interference:', e.message);
            }

            // Mark fallback as initialized
            window.geocodingState.fallbackInitialized = true;
            window.geocodingState.currentProvider = 'openstreetmap';

            // Create autocomplete container
            const autocompleteContainer = document.createElement('div');
            autocompleteContainer.id = 'nominatim-autocomplete';
            autocompleteContainer.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--secondary-dark);
                border: 1px solid var(--border-color);
                border-top: none;
                border-radius: 0 0 8px 8px;
                max-height: 200px;
                overflow-y: auto;
                z-index: 1000;
                display: none;
            `;

            // Make the parent container relative for positioning
            const parentContainer = addressInput.parentElement;
            parentContainer.style.position = 'relative';
            parentContainer.appendChild(autocompleteContainer);

            let searchTimeout;
            let currentResults = [];

            // Remove any existing input event listeners to prevent conflicts
            const newAddressInput = addressInput.cloneNode(true);
            addressInput.parentNode.replaceChild(newAddressInput, addressInput);

            // Add input event listener for autocomplete
            newAddressInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);

                if (query.length < 3) {
                    hideAutocomplete();
                    return;
                }

                // Debounce the search
                searchTimeout = setTimeout(() => {
                    searchAddresses(query);
                }, 300);
            });

            // Hide autocomplete when clicking outside
            document.addEventListener('click', function(e) {
                if (!parentContainer.contains(e.target)) {
                    hideAutocomplete();
                }
            });

            function searchAddresses(query) {
                // Show loading state
                autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--text-secondary);">Searching addresses...</div>';
                autocompleteContainer.style.display = 'block';

                // Improve search query for better user input handling
                const optimizedQuery = optimizeSearchQuery(query);

                // Search using Nominatim API with improved parameters
                const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(optimizedQuery)}&countrycodes=us&limit=5&addressdetails=1&extratags=1&namedetails=1`;

                fetch(nominatimUrl)
                    .then(response => response.json())
                    .then(data => {
                        // Filter and sort results for better relevance
                        const filteredResults = filterAndSortResults(data, query);
                        currentResults = filteredResults;
                        displayAutocompleteResults(filteredResults);
                    })
                    .catch(error => {
                        console.error('Nominatim geocoding error:', error);
                        autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--accent-red);">Error searching addresses</div>';
                    });
            }

            // Optimize search query for better Nominatim results
            function optimizeSearchQuery(query) {
                // Handle common user input patterns
                let optimized = query.trim();

                // If query contains comma, treat parts appropriately
                if (optimized.includes(',')) {
                    const parts = optimized.split(',').map(part => part.trim());

                    // If user typed "123 Main St, Springfield" format
                    if (parts.length === 2) {
                        const [streetPart, cityPart] = parts;
                        // Enhance query to help Nominatim find better matches
                        optimized = `${streetPart}, ${cityPart}, USA`;
                    }
                }

                return optimized;
            }

            // Filter and sort results for better relevance
            function filterAndSortResults(results, originalQuery) {
                if (!results || results.length === 0) return results;

                // Filter out results that are too generic or not useful
                const filtered = results.filter(result => {
                    const address = result.address || {};

                    // Must have at least a road/street name
                    if (!address.road && !address.house_number) {
                        return false;
                    }

                    // Prefer results with house numbers for specific addresses
                    if (originalQuery.match(/^\d+/)) {
                        return address.house_number || address.road;
                    }

                    return true;
                });

                // Sort by relevance (prefer results with house numbers if query starts with number)
                const sorted = filtered.sort((a, b) => {
                    const aAddress = a.address || {};
                    const bAddress = b.address || {};

                    // If original query starts with a number, prioritize results with house numbers
                    if (originalQuery.match(/^\d+/)) {
                        if (aAddress.house_number && !bAddress.house_number) return -1;
                        if (!aAddress.house_number && bAddress.house_number) return 1;
                    }

                    // Prefer more complete addresses
                    const aCompleteness = (aAddress.house_number ? 1 : 0) +
                                         (aAddress.road ? 1 : 0) +
                                         (aAddress.city || aAddress.town ? 1 : 0) +
                                         (aAddress.state ? 1 : 0);
                    const bCompleteness = (bAddress.house_number ? 1 : 0) +
                                         (bAddress.road ? 1 : 0) +
                                         (bAddress.city || bAddress.town ? 1 : 0) +
                                         (bAddress.state ? 1 : 0);

                    return bCompleteness - aCompleteness;
                });

                return sorted.slice(0, 5); // Limit to top 5 results
            }

            function displayAutocompleteResults(results) {
                if (!results || results.length === 0) {
                    autocompleteContainer.innerHTML = '<div style="padding: 10px; color: var(--text-secondary);">No addresses found</div>';
                    return;
                }

                autocompleteContainer.innerHTML = '';

                results.forEach((result, index) => {
                    const resultItem = document.createElement('div');
                    resultItem.style.cssText = `
                        padding: 10px;
                        cursor: pointer;
                        border-bottom: 1px solid var(--border-color);
                        color: var(--text-light);
                        transition: background-color 0.2s ease;
                    `;

                    // Format address in standard US format: "Street Address, City, State Zipcode"
                    const formattedAddress = formatAddressForDisplay(result);

                    resultItem.innerHTML = `
                        <div style="font-weight: 500;">${formattedAddress}</div>
                        <small style="color: var(--text-secondary);">Lat: ${result.lat}, Lng: ${result.lon}</small>
                    `;

                    // Hover effects
                    resultItem.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'var(--hover-bg)';
                    });

                    resultItem.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                    });

                    // Click handler
                    resultItem.addEventListener('click', function() {
                        selectNominatimResult(result);
                        hideAutocomplete();
                    });

                    autocompleteContainer.appendChild(resultItem);
                });
            }

            // Format address for display in standard US format
            function formatAddressForDisplay(result) {
                const address = result.address || {};
                let formattedParts = [];

                // Street address (house number + road)
                let streetAddress = '';
                if (address.house_number && address.road) {
                    streetAddress = `${address.house_number} ${address.road}`;
                } else if (address.road) {
                    streetAddress = address.road;
                } else {
                    // Use first part of display name as fallback
                    streetAddress = result.display_name.split(',')[0];
                }
                formattedParts.push(streetAddress);

                // City
                const city = address.city || address.town || address.village || address.hamlet || '';
                if (city) {
                    formattedParts.push(city);
                }

                // State and ZIP (use abbreviations to match Google Maps format)
                let stateZip = '';
                if (address.state) {
                    const stateAbbr = getStateAbbreviation(address.state);
                    stateZip = stateAbbr;
                    if (address.postcode) {
                        stateZip += ` ${address.postcode}`;
                    }
                    formattedParts.push(stateZip);
                } else if (address.postcode) {
                    formattedParts.push(address.postcode);
                }

                return formattedParts.join(', ');
            }

            function selectNominatimResult(result) {
                console.log('Selected Nominatim result:', result);

                // Fill coordinate fields
                document.getElementById('latitude').value = result.lat;
                document.getElementById('longitude').value = result.lon;

                // Parse address components from Nominatim result
                const address = result.address || {};

                // Fill address field
                let streetAddress = '';
                if (address.house_number && address.road) {
                    streetAddress = `${address.house_number} ${address.road}`;
                } else if (address.road) {
                    streetAddress = address.road;
                } else {
                    // Use first part of display name as fallback
                    streetAddress = result.display_name.split(',')[0];
                }
                document.getElementById('address').value = streetAddress;

                // Fill city field
                const city = address.city || address.town || address.village || address.hamlet || '';
                if (city) {
                    document.getElementById('city').value = city;
                }

                // Fill zip code
                if (address.postcode) {
                    document.getElementById('zip_code').value = address.postcode;
                }

                // Handle state
                if (address.state) {
                    const stateSelect = document.getElementById('state_select');
                    const stateHidden = document.getElementById('state');

                    if (stateSelect && stateHidden) {
                        // Try to find state by name or abbreviation
                        const stateName = address.state;
                        for (let i = 0; i < stateSelect.options.length; i++) {
                            const option = stateSelect.options[i];
                            if (option.dataset.name === stateName ||
                                option.textContent.includes(stateName) ||
                                option.dataset.abbr === getStateAbbreviation(stateName)) {
                                stateSelect.selectedIndex = i;
                                stateHidden.value = option.dataset.abbr || getStateAbbreviation(stateName);

                                // Trigger change event to load counties
                                const event = new Event('change');
                                stateSelect.dispatchEvent(event);

                                // Set county after delay
                                if (address.county) {
                                    setTimeout(() => {
                                        setCountyFromNominatim(address.county);
                                    }, 1000);
                                }
                                break;
                            }
                        }
                    }
                }

                // Update the search input with the formatted address (without county)
                const formattedDisplayAddress = formatAddressForDisplay(result);
                newAddressInput.value = formattedDisplayAddress;

                showNotification('Address geocoded successfully using OpenStreetMap!', 'success');
            }

            function setCountyFromNominatim(countyName) {
                const countySelect = document.getElementById('county_select');
                const countyHidden = document.getElementById('county');

                if (countySelect && countyHidden) {
                    // Clean county name (remove "County" suffix if present)
                    const cleanCountyName = countyName.replace(' County', '');

                    for (let j = 0; j < countySelect.options.length; j++) {
                        if (countySelect.options[j].textContent.includes(cleanCountyName)) {
                            countySelect.selectedIndex = j;
                            countyHidden.value = cleanCountyName;
                            break;
                        }
                    }
                }
            }

            function hideAutocomplete() {
                autocompleteContainer.style.display = 'none';
            }

            // Helper function to get state abbreviation from full name
            function getStateAbbreviation(stateName) {
                const stateMap = {
                    'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR', 'California': 'CA',
                    'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE', 'Florida': 'FL', 'Georgia': 'GA',
                    'Hawaii': 'HI', 'Idaho': 'ID', 'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA',
                    'Kansas': 'KS', 'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
                    'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS', 'Missouri': 'MO',
                    'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ',
                    'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', 'Ohio': 'OH',
                    'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC',
                    'South Dakota': 'SD', 'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
                    'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV', 'Wisconsin': 'WI', 'Wyoming': 'WY'
                };
                return stateMap[stateName] || stateName;
            }

            // Show OpenStreetMap status
            updateGeocodingStatus('OpenStreetMap', 'var(--accent-green)');

            console.log('✅ OpenStreetMap/Nominatim fallback geocoding initialized successfully');
            console.log('✅ Address input field is now fully functional');

            // Notify user that geocoding is ready
            showNotification('Address geocoding ready (OpenStreetMap)', 'info');
        }

        // Function to populate form with incident data (with dropdown coordination)
        function populateForm(incident) {
            if (!incident) return;

            console.log('📋 populateForm called with incident:', incident.id);

            // Check if dropdowns are initialized
            if (!dropdownsInitialized) {
                console.log('⏳ Dropdowns not ready, storing incident data for later');
                pendingIncidentData = incident;
                return;
            }

            // Dropdowns are ready, populate immediately
            populateFormWithDropdowns(incident);
        }

        // Function to populate form when dropdowns are ready
        function populateFormWithDropdowns(incident) {
            if (!incident) return;

            console.log('✅ Populating form with dropdowns ready for incident:', incident.id);

            // Update the incident ID in the header
            document.getElementById('incident-id').textContent = incident.id || '';

            // Basic incident fields
            document.getElementById('title').value = incident.title || '';
            document.getElementById('address').value = incident.address || '';
            document.getElementById('city').value = incident.city || '';
            document.getElementById('state').value = incident.state || '';
            document.getElementById('zip_code').value = incident.zip || incident.zip_code || '';
            document.getElementById('county').value = incident.county || '';
            document.getElementById('latitude').value = incident.latitude !== null ? incident.latitude : '';
            document.getElementById('longitude').value = incident.longitude !== null ? incident.longitude : '';
            document.getElementById('updates').value = incident.updates || '';

            // New fields added
            document.getElementById('severity').value = incident.severity || 'moderate';
            document.getElementById('alarm_level').value = incident.alarmLevel || 1;
            document.getElementById('cross_street').value = incident.crossStreet || '';
            document.getElementById('dispatch_info').value = incident.dispatchInfo || '';

            // Format and set incident date
            if (incident.incidentDate) {
                const incidentDate = new Date(incident.incidentDate);
                const formattedDate = incidentDate.toISOString().slice(0, 16); // Format for datetime-local input
                document.getElementById('incident_date').value = formattedDate;
            }

            // Set status dropdown immediately (dropdowns are already initialized)
            if (incident.statusId || incident.status) {
                const statusSelect = document.getElementById('status');
                if (statusSelect) {
                    let statusValue = '';

                    // Try to use the status name directly first
                    if (incident.status && incident.status.name) {
                        statusValue = incident.status.name;
                    } else if (incident.status && typeof incident.status === 'string') {
                        statusValue = incident.status;
                    } else if (incident.statusId) {
                        // Map statusId to the dropdown value as fallback
                        switch(incident.statusId) {
                            case 1: statusValue = 'active'; break;
                            case 2: statusValue = 'resolved'; break;
                            case 3: statusValue = 'pending'; break;
                            case 4: statusValue = 'critical'; break;
                            default: statusValue = 'active';
                        }
                    }

                    // Set the value and verify it worked
                    statusSelect.value = statusValue;
                    console.log('🎯 Set status dropdown to:', statusValue, 'for statusId:', incident.statusId, 'status object:', incident.status);

                    // Verify the selection took effect
                    if (statusSelect.value !== statusValue) {
                        console.warn('⚠️ Status dropdown value did not set correctly. Available options:',
                            Array.from(statusSelect.options).map(opt => opt.value));
                    }
                }
            }

            // Set state and county dropdowns (with minimal delay for state->county dependency)
            setStateDropdownValue(incident.state);
            setTimeout(() => {
                setCountyDropdownValue(incident.county);
            }, 500); // Reduced delay, just enough for county loading

            // Set incident type icon and display
            updateIncidentTypeIcon(incident.incidentType);
            updateIncidentTypeDisplay(incident.incidentType);

            // Determine incident type from incidentTypeId (1-4: fire, 5-10: water)
            const incidentTypeId = incident.incidentTypeId;
            const isFireIncident = (incidentTypeId >= 1 && incidentTypeId <= 4) || (incidentTypeId >= 11);
            const isWaterIncident = (incidentTypeId >= 5 && incidentTypeId <= 10);

            console.log('🔥 Incident type determination:', {
                incidentTypeId,
                isFireIncident,
                isWaterIncident,
                incidentTypeObject: incident.incidentType
            });

            // Set the hidden incident type field immediately
            const incidentTypeField = document.getElementById('incident_type');
            if (incidentTypeField) {
                const typeValue = isFireIncident ? 'fire' : (isWaterIncident ? 'water' : 'fire');
                incidentTypeField.value = typeValue;
                console.log('🎯 Set incident_type field to:', typeValue);
            }

            // Show/hide appropriate fields based on incident type
            const fireFields = document.querySelectorAll('.fire-field');
            const waterFields = document.querySelectorAll('.water-field');

            fireFields.forEach(field => field.style.display = isFireIncident ? 'block' : 'none');
            waterFields.forEach(field => field.style.display = isWaterIncident ? 'block' : 'none');

            // Handle incident type specific selections immediately
            if (isFireIncident) {
                let fireTypeValue = '';
                switch(incidentTypeId) {
                    case 1: fireTypeValue = '1'; break; // Structure Fire - Residential
                    case 2: fireTypeValue = '2'; break; // Structure Fire - Commercial
                    case 3: fireTypeValue = '3'; break; // Vehicle Fire
                    case 4: fireTypeValue = '4'; break; // Brush/Wildland Fire
                    case 11: fireTypeValue = '11'; break; // Electrical Fire
                    case 12: fireTypeValue = '12'; break; // Chimney Fire
                    case 13: fireTypeValue = '13'; break; // Explosion
                    case 14: fireTypeValue = '14'; break; // Hazardous Material
                    case 15: fireTypeValue = '15'; break; // Other
                    default: fireTypeValue = '1'; // Default to residential
                }
                const fireTypeField = document.getElementById('fire_type');
                if (fireTypeField) {
                    fireTypeField.value = fireTypeValue;
                    console.log('🔥 Set fire_type to:', fireTypeValue, 'for incidentTypeId:', incidentTypeId);

                    // Verify the selection took effect
                    if (fireTypeField.value !== fireTypeValue) {
                        console.warn('⚠️ Fire type dropdown value did not set correctly. Available options:',
                            Array.from(fireTypeField.options).map(opt => `${opt.value}: ${opt.text}`));
                    } else {
                        console.log('✅ Fire type dropdown set successfully');
                    }
                }

                // Populate fire-specific fields from incidentDetail
                if (incident.incidentDetail) {
                    const detail = incident.incidentDetail;
                    document.getElementById('fire_op').value = detail.notes || '';
                    document.getElementById('structure_type').value = detail.structureType || '';
                    document.getElementById('smoke_type').value = detail.smokeType || '';
                    document.getElementById('people_affected').value = detail.peopleAffected || 0;
                    document.getElementById('evacuation_status').value = detail.evacuationStatus || 'none';
                    document.getElementById('estimated_damage').value = detail.estimatedDamage || '';
                    document.getElementById('responder_count').value = detail.responderCount || '';

                    // Set smoke severity if available
                    const smokeSeverityField = document.getElementById('smoke_severity');
                    if (smokeSeverityField && detail.smokeSeverity) {
                        smokeSeverityField.value = detail.smokeSeverity;
                    }
                }
            }
            else if (isWaterIncident) {
                let waterTypeValue = '';
                switch(incidentTypeId) {
                    case 5: waterTypeValue = '5'; break; // Water Main Break
                    case 6: waterTypeValue = '6'; break; // Flooding
                    case 7: waterTypeValue = '7'; break; // Fire Hydrant Damage
                    case 8: waterTypeValue = '8'; break; // Sewer Backup
                    case 9: waterTypeValue = '9'; break; // Water Rescue
                    case 10: waterTypeValue = '10'; break; // Dam/Levee Breach
                    case 16: waterTypeValue = '16'; break; // Water Treatment Issue
                    case 17: waterTypeValue = '17'; break; // Water Supply Contamination
                    case 18: waterTypeValue = '18'; break; // Other
                    default: waterTypeValue = '5'; // Default to main break
                }
                const waterTypeField = document.getElementById('water_type');
                if (waterTypeField) {
                    waterTypeField.value = waterTypeValue;
                    console.log('💧 Set water_type to:', waterTypeValue, 'for incidentTypeId:', incidentTypeId);

                    // Verify the selection took effect
                    if (waterTypeField.value !== waterTypeValue) {
                        console.warn('⚠️ Water type dropdown value did not set correctly. Available options:',
                            Array.from(waterTypeField.options).map(opt => `${opt.value}: ${opt.text}`));
                    } else {
                        console.log('✅ Water type dropdown set successfully');
                    }
                }

                // Populate water-specific fields from incidentDetail
                if (incident.incidentDetail) {
                    const detail = incident.incidentDetail;
                    document.getElementById('area_affected').value = detail.areaAffected || '';
                    document.getElementById('water_level').value = detail.waterLevel || '';
                    document.getElementById('damage_extent').value = detail.damageExtent || '';
                }
            }

            // Populate owner info and other details if available from incidentDetail
            if (incident.incidentDetail) {
                const detail = incident.incidentDetail;

                // Owner information
                document.getElementById('owner1').value = detail.propertyOwner || '';
                document.getElementById('owner2').value = detail.ownerContact || detail.secondaryOwner || '';

                // Additional owner details
                const ownerPhoneField = document.getElementById('ownerPhone');
                if (ownerPhoneField) ownerPhoneField.value = detail.ownerPhone || '';

                const ownerEmailField = document.getElementById('ownerEmail');
                if (ownerEmailField) ownerEmailField.value = detail.ownerEmail || '';

                const ownerAddrField = document.getElementById('ownerAddr');
                if (ownerAddrField) ownerAddrField.value = detail.ownerAddress || '';

                const dwellingTypeField = document.getElementById('dwellingType');
                if (dwellingTypeField) dwellingTypeField.value = detail.dwellingType || '';

                const yearBuiltField = document.getElementById('yearBuilt');
                if (yearBuiltField) yearBuiltField.value = detail.yearBuilt || '';

                const propertyValueField = document.getElementById('propertyValue');
                if (propertyValueField && detail.propertyValue) {
                    // Format the property value as currency
                    propertyValueField.value = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(detail.propertyValue);
                }

                const homeStoriesField = document.getElementById('home_stories');
                if (homeStoriesField) homeStoriesField.value = detail.homeStories || '';

                // Common incident detail fields
                const responseDetailsField = document.getElementById('response_details');
                if (responseDetailsField) responseDetailsField.value = detail.responseDetails || '';

                const conditionsOnArrivalField = document.getElementById('conditions_on_arrival');
                if (conditionsOnArrivalField) conditionsOnArrivalField.value = detail.conditionsOnArrival || '';

                const notesField = document.getElementById('notes');
                if (notesField) notesField.value = detail.notes || '';

                // Additional type-specific fields (fire_type and water_type are already set above)
                if (isFireIncident) {
                    const fireOpField = document.getElementById('fire_op');
                    if (fireOpField && !fireOpField.value) {
                        fireOpField.value = detail.responseDetails || detail.notes || '';
                    }
                } else if (isWaterIncident) {
                    // Water-specific additional fields are already handled above
                }
            }

            // Populate additional details
            document.getElementById('add_details').value = incident.description || '';

            // Enhanced debug logging to track dropdown values
            console.log('📊 Form population completed. Final dropdown values:', {
                incidentId: incident.id,
                statusDropdown: document.getElementById('status')?.value,
                fireTypeDropdown: document.getElementById('fire_type')?.value,
                waterTypeDropdown: document.getElementById('water_type')?.value,
                incidentTypeHidden: document.getElementById('incident_type')?.value,
                originalData: {
                    statusId: incident.statusId,
                    incidentTypeId: incident.incidentTypeId,
                    status: incident.status,
                    incidentType: incident.incidentType
                }
            });

            // Verify all critical dropdowns have correct values
            const statusSelect = document.getElementById('status');
            const fireTypeSelect = document.getElementById('fire_type');
            const waterTypeSelect = document.getElementById('water_type');

            if (statusSelect && !statusSelect.value && incident.statusId) {
                console.error('❌ Status dropdown is empty after population!');
                console.log('Available status options:', Array.from(statusSelect.options).map(opt => `${opt.value}: ${opt.text}`));
            }
            if (isFireIncident && fireTypeSelect) {
                if (!fireTypeSelect.value) {
                    console.error('❌ Fire type dropdown is empty after population!');
                    console.log('Available fire type options:', Array.from(fireTypeSelect.options).map(opt => `${opt.value}: ${opt.text}`));
                } else {
                    console.log('✅ Fire type dropdown populated successfully with value:', fireTypeSelect.value);
                }
            }
            if (isWaterIncident && waterTypeSelect) {
                if (!waterTypeSelect.value) {
                    console.error('❌ Water type dropdown is empty after population!');
                    console.log('Available water type options:', Array.from(waterTypeSelect.options).map(opt => `${opt.value}: ${opt.text}`));
                } else {
                    console.log('✅ Water type dropdown populated successfully with value:', waterTypeSelect.value);
                }
            }
        }

        function updateIncidentTypeIcon(incidentType) {
            const iconElement = document.getElementById('incident-icon');
            if (!incidentType || !iconElement) return;

            if (incidentType.category === 'fire') {
                iconElement.className = 'fas fa-fire';
                iconElement.style.color = 'var(--accent-red)';
            } else if (incidentType.category === 'water') {
                iconElement.className = 'fas fa-water';
                iconElement.style.color = 'var(--accent-blue)';
            }
        }

        function updateIncidentTypeDisplay(incidentType) {
            const badge = document.getElementById('incident-type-badge');
            const text = document.getElementById('incident-type-text');
            const icon = badge?.querySelector('i');

            if (!incidentType || !badge || !text || !icon) return;

            if (incidentType.category === 'fire') {
                badge.className = 'incident-type-badge fire';
                icon.className = 'fas fa-fire';
                text.textContent = 'Fire Incident';
            } else if (incidentType.category === 'water') {
                badge.className = 'incident-type-badge water';
                icon.className = 'fas fa-water';
                text.textContent = 'Water Incident';
            }
        }

        // Function to load incident data
        function loadIncidentData() {
            const urlParams = new URLSearchParams(window.location.search);
            incidentId = urlParams.get('id');

            if (!incidentId) {
                showNotification('Error: No incident ID specified', 'error');
                // Disable form or redirect
                document.getElementById('update-incident-btn').disabled = true;
                return;
            }

            // Update cancel link
            const cancelLink = document.getElementById('cancel-link');
            if(cancelLink) cancelLink.href = `view-incident.html?id=${incidentId}`;

            showNotification('Loading incident data...', 'info'); // Loading indicator

            API.incidents.getById(incidentId)
                .then(data => {
                    console.log('Incident data received:', data);
                    if (data && data.success !== false) {
                        // Handle both new standardized format and legacy format
                        const incidentData = data.data || data;
                        populateForm(incidentData);
                        showNotification('Incident data loaded.', 'success');
                    } else {
                        const errorMsg = data?.msg || data?.message || 'Unknown error';
                        showNotification('Error loading incident data: ' + errorMsg, 'error');
                        document.getElementById('update-incident-btn').disabled = true;
                    }
                })
                .catch(error => {
                    console.error('Error fetching incident data:', error);
                    showNotification('Error connecting to server.', 'error');
                    document.getElementById('update-incident-btn').disabled = true;
                });
        }

        // Populate dropdowns from API - same as add-incident.html
        async function populateStatesDropdown() {
            const stateSelect = document.getElementById('state_select');

            try {
                stateSelect.innerHTML = '<option value="">Loading states...</option>';
                stateSelect.disabled = true;

                const response = await API.locations.getStates();

                if (response && Array.isArray(response) && response.length > 0) {
                    stateSelect.innerHTML = '<option value="">Select State</option>';

                    response.forEach(state => {
                        const option = document.createElement('option');
                        option.value = state.id;
                        option.textContent = `${state.name} (${state.abbreviation})`;
                        option.dataset.abbr = state.abbreviation;
                        option.dataset.name = state.name;
                        stateSelect.appendChild(option);
                    });

                    stateSelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} states from API`);
                } else {
                    throw new Error('No states received from API');
                }
            } catch (error) {
                console.error('Error loading states:', error);
                stateSelect.innerHTML = '<option value="">Error loading states</option>';
                stateSelect.disabled = true;
                showNotification('Failed to load states from server.', 'error');
            }
        }

        async function populateCountiesDropdown(stateId) {
            const countySelect = document.getElementById('county_select');

            if (!stateId) {
                countySelect.innerHTML = '<option value="">Select County</option>';
                countySelect.disabled = true;
                return;
            }

            try {
                countySelect.innerHTML = '<option value="">Loading counties...</option>';
                countySelect.disabled = true;

                const response = await API.locations.getCounties(stateId);

                if (response && Array.isArray(response) && response.length > 0) {
                    countySelect.innerHTML = '<option value="">Select County</option>';

                    response.forEach(county => {
                        const option = document.createElement('option');
                        option.value = county.id;
                        option.textContent = county.name;
                        option.dataset.name = county.name;
                        countySelect.appendChild(option);
                    });

                    countySelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} counties for state ID ${stateId} from API`);
                } else {
                    countySelect.innerHTML = '<option value="">No counties available</option>';
                    countySelect.disabled = true;
                }
            } catch (error) {
                console.error('Error loading counties:', error);
                countySelect.innerHTML = '<option value="">Error loading counties</option>';
                countySelect.disabled = true;
                showNotification('Failed to load counties from server.', 'error');
            }
        }

        async function populateIncidentStatusesDropdown() {
            const statusSelect = document.getElementById('status');

            if (!statusSelect) {
                console.warn('Status select element not found');
                return;
            }

            console.log('🔄 Populating incident statuses dropdown...');

            try {
                statusSelect.innerHTML = '<option value="">Loading statuses...</option>';
                statusSelect.disabled = true;

                // Check if API method exists
                if (!API.settings || typeof API.settings.getIncidentStatuses !== 'function') {
                    throw new Error('API method getIncidentStatuses not available');
                }

                const response = await API.settings.getIncidentStatuses();

                if (response && Array.isArray(response) && response.length > 0) {
                    statusSelect.innerHTML = '<option value="">Select Status</option>';

                    response.forEach(status => {
                        const option = document.createElement('option');
                        option.value = status.name || status.status;
                        option.textContent = status.name || status.status;
                        statusSelect.appendChild(option);
                    });

                    statusSelect.disabled = false;
                    console.log(`✅ Loaded ${response.length} incident statuses from API:`,
                        response.map(s => s.name || s.status));
                } else {
                    throw new Error('No incident statuses received from API');
                }
            } catch (error) {
                console.error('Error loading incident statuses:', error);

                // Provide basic fallback statuses
                statusSelect.innerHTML = '<option value="">Select Status</option>';
                const basicStatuses = [
                    { name: 'active', display: 'Active' },
                    { name: 'pending', display: 'Pending' },
                    { name: 'critical', display: 'Critical' },
                    { name: 'resolved', display: 'Resolved' }
                ];

                basicStatuses.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.name;
                    option.textContent = status.display;
                    statusSelect.appendChild(option);
                });

                statusSelect.disabled = false;
                console.log('Using fallback incident statuses');
                showNotification('Using basic incident statuses (API unavailable)', 'warning');
            }
        }



        // Cache for states and counties data
        let statesCache = null;
        let countiesCache = new Map();

        // Track dropdown initialization state
        let dropdownsInitialized = false;
        let pendingIncidentData = null;

        // Initialize dropdowns with performance optimizations
        async function initializeDropdownsWithCaching() {
            try {
                console.log('🔄 Initializing dropdowns...');

                // Load states and statuses in parallel for better performance
                const [statesResult, statusesResult] = await Promise.all([
                    loadStatesWithCaching(),
                    populateIncidentStatusesDropdown()
                ]);

                dropdownsInitialized = true;
                console.log('✅ Dropdowns initialized successfully');

                // If we have pending incident data, populate the form now
                if (pendingIncidentData) {
                    console.log('📝 Populating form with pending incident data');
                    populateFormWithDropdowns(pendingIncidentData);
                    pendingIncidentData = null;
                }
            } catch (error) {
                console.error('Error initializing dropdowns:', error);
                showNotification('Error loading dropdown data', 'error');
            }
        }

        // Load states with caching
        async function loadStatesWithCaching() {
            try {
                // Check if states are already cached
                if (statesCache) {
                    console.log('Using cached states data');
                    populateStatesDropdownFromCache(statesCache);
                    return statesCache;
                }

                // Load states from API
                const states = await API.locations.getStates();
                if (states && states.length > 0) {
                    statesCache = states;
                    populateStatesDropdownFromCache(states);
                    return states;
                } else {
                    throw new Error('No states data received');
                }
            } catch (error) {
                console.error('Error loading states:', error);
                showNotification('Failed to load states data', 'error');
                throw error;
            }
        }

        // Populate states dropdown from cached data
        function populateStatesDropdownFromCache(states) {
            const stateSelect = document.getElementById('state_select');

            if (!stateSelect) {
                console.warn('State select element not found');
                return;
            }

            stateSelect.innerHTML = '<option value="">Select State</option>';

            states.forEach(state => {
                const option = document.createElement('option');
                option.value = state.id;
                option.textContent = `${state.name} (${state.abbreviation})`;
                option.dataset.abbr = state.abbreviation;
                option.dataset.name = state.name;
                stateSelect.appendChild(option);
            });

            stateSelect.disabled = false;
            console.log(`✅ Loaded ${states.length} states from cache`);
        }

        // Load counties with caching
        async function loadCountiesWithCaching(stateId) {
            try {
                // Check if counties for this state are already cached
                if (countiesCache.has(stateId)) {
                    console.log(`Using cached counties data for state ${stateId}`);
                    populateCountiesDropdownFromCache(countiesCache.get(stateId));
                    return countiesCache.get(stateId);
                }

                // Load counties from API
                const counties = await API.locations.getCounties(stateId);
                if (counties && counties.length > 0) {
                    countiesCache.set(stateId, counties);
                    populateCountiesDropdownFromCache(counties);
                    return counties;
                } else {
                    // Cache empty result to avoid repeated API calls
                    countiesCache.set(stateId, []);
                    populateCountiesDropdownFromCache([]);
                    return [];
                }
            } catch (error) {
                console.error('Error loading counties:', error);
                showNotification('Failed to load counties data', 'error');
                throw error;
            }
        }

        // Populate counties dropdown from cached data
        function populateCountiesDropdownFromCache(counties) {
            const countySelect = document.getElementById('county_select');

            if (!countySelect) {
                console.warn('County select element not found');
                return;
            }

            countySelect.innerHTML = '<option value="">Select County</option>';

            if (counties.length === 0) {
                countySelect.innerHTML = '<option value="">No counties found</option>';
                countySelect.disabled = true;
                return;
            }

            counties.forEach(county => {
                const option = document.createElement('option');
                option.value = county.id;
                option.textContent = county.name;
                option.dataset.name = county.name;
                countySelect.appendChild(option);
            });

            countySelect.disabled = false;
            console.log(`✅ Loaded ${counties.length} counties from cache`);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('edit-incident');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Check if Google Maps is already blocked by errors
            if (window.geocodingState.googleMapsBlocked) {
                console.log('Google Maps already blocked by errors, using OpenStreetMap');
                initializeFallbackGeocoding();
            } else if (window.apiKeys) {
                console.log('Attempting to load Google Maps API...');
                window.geocodingState.googleMapsAttempted = true;

                try {
                    // Add a pre-check for API key validity (async)
                    window.apiKeys.getKey('google_maps').then(apiKey => {
                        if (!apiKey || apiKey === 'AIzaSyDefaultKeyForDevelopment') {
                            console.warn('Invalid or default Google Maps API key detected, using OpenStreetMap fallback');
                            initializeFallbackGeocoding();
                        } else {
                            window.apiKeys.loadGoogleMapsApi('initAutocomplete', ['places']);

                            // Set a much shorter fallback timer (2 seconds)
                            window.geocodingState.fallbackTimer = setTimeout(() => {
                                if (!window.geocodingState.currentProvider && !window.geocodingState.googleMapsBlocked) {
                                    console.warn('Google Maps API failed to load within 2 seconds, initializing fallback');
                                    initializeFallbackGeocoding();
                                }
                            }, 2000);
                        }
                    }).catch(error => {
                        console.error('Error retrieving Google Maps API key:', error);
                        window.geocodingState.googleMapsBlocked = true;
                        initializeFallbackGeocoding();
                    });
                } catch (error) {
                    console.error('Error loading Google Maps API:', error);
                    window.geocodingState.googleMapsBlocked = true;
                    initializeFallbackGeocoding();
                }
            } else {
                console.warn("API Keys utility not loaded. Using OpenStreetMap fallback.");
                initializeFallbackGeocoding();
            }

            // Initialize fallback immediately if no Google Maps provider is set within 500ms
            // This ensures the address field is always functional
            setTimeout(() => {
                if (!window.geocodingState.currentProvider && !window.geocodingState.fallbackInitialized) {
                    console.log('No geocoding provider initialized within 500ms, using OpenStreetMap fallback');
                    initializeFallbackGeocoding();
                }
            }, 500);

            // Also initialize fallback immediately if user starts typing before Google Maps loads
            const addressInput = document.getElementById('google_address');
            if (addressInput) {
                let userStartedTyping = false;
                addressInput.addEventListener('input', function() {
                    if (!userStartedTyping && !window.geocodingState.currentProvider) {
                        userStartedTyping = true;
                        console.log('User started typing before geocoding initialized, using fallback');
                        if (window.geocodingState.fallbackTimer) {
                            clearTimeout(window.geocodingState.fallbackTimer);
                        }
                        initializeFallbackGeocoding();
                    }
                });
            }

            // Initialize all dropdowns from API with performance optimizations
            initializeDropdownsWithCaching();

            // Load incident data
            loadIncidentData();

            // State selection handler
            const stateSelect = document.getElementById('state_select');
            if (stateSelect) {
                stateSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const stateAbbr = selectedOption.dataset.abbr || '';
                    const stateName = selectedOption.dataset.name || '';

                    // Update hidden state field with abbreviation
                    document.getElementById('state').value = stateAbbr;

                    // Load counties for selected state using cached function
                    if (this.value) {
                        loadCountiesWithCaching(this.value);
                    } else {
                        // Clear counties if no state selected
                        const countySelect = document.getElementById('county_select');
                        countySelect.innerHTML = '<option value="">Select County</option>';
                        countySelect.disabled = true;
                        document.getElementById('county').value = '';
                    }
                });
            }

            // County selection handler
            const countySelect = document.getElementById('county_select');
            if (countySelect) {
                countySelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const countyName = selectedOption.dataset.name || selectedOption.textContent;

                    // Update hidden county field with the county name
                    document.getElementById('county').value = countyName;
                });
            }

            // Add toggle history button functionality
            document.getElementById('toggleHistoryBtn').addEventListener('click', function() {
                const historyDiv = document.getElementById('incident-history');
                if (historyDiv.style.display === 'none') {
                    historyDiv.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i> Hide History';
                } else {
                    historyDiv.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-eye"></i> Show History';
                }
            });
        });

        // Handle Form Submission
        document.getElementById('editIncidentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            if (!incidentId) {
                showNotification('Cannot update: Incident ID is missing.', 'error');
                return;
            }

            const submitButton = document.getElementById('update-incident-btn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const formData = new FormData(this);

            // Find incident type ID based on incident type and subtype
            let incidentTypeId;
            const incidentType = formData.get('incident_type');

            if (incidentType === 'fire') {
                const fireType = formData.get('fire_type');
                // Fire type dropdown now uses numeric values directly
                incidentTypeId = parseInt(fireType) || 1; // Default to residential (1)
                console.log('🔥 Form submission - Fire type:', fireType, 'mapped to incidentTypeId:', incidentTypeId);
            } else if (incidentType === 'water') {
                const waterType = formData.get('water_type');
                // Water type dropdown now uses numeric values directly
                incidentTypeId = parseInt(waterType) || 5; // Default to main break (5)
                console.log('💧 Form submission - Water type:', waterType, 'mapped to incidentTypeId:', incidentTypeId);
            } else {
                // Fallback for unknown incident types
                incidentTypeId = 1;
                console.warn('⚠️ Unknown incident type:', incidentType, 'defaulting to incidentTypeId 1');
            }

            // Map status to statusId
            let statusId;
            const status = formData.get('status');
            switch(status) {
                case 'active': statusId = 1; break;
                case 'resolved': statusId = 2; break;
                case 'pending': statusId = 3; break;
                case 'critical': statusId = 4; break;
                default: statusId = 1; // Default to active
            }

            // Basic incident data
            const incidentData = {
                id: incidentId,
                title: formData.get('title'),
                address: formData.get('address'),
                city: formData.get('city'),
                state: formData.get('state'),
                zip: formData.get('zip_code'),
                county: formData.get('county'),
                latitude: formData.get('latitude') || null,
                longitude: formData.get('longitude') || null,
                incidentTypeId: incidentTypeId,
                statusId: statusId,
                description: formData.get('add_details'),
                updates: formData.get('updates'),
                severity: formData.get('severity') || 'moderate',
                alarmLevel: parseInt(formData.get('alarm_level')) || 1,
                crossStreet: formData.get('cross_street'),
                dispatchInfo: formData.get('dispatch_info'),
                incidentDate: formData.get('incident_date') ? new Date(formData.get('incident_date')) : new Date()
            };

            // Incident detail data based on type
            const incidentDetail = {
                incidentId: incidentId,
                propertyOwner: formData.get('owner1'),
                ownerContact: formData.get('owner2'),
                ownerPhone: formData.get('ownerPhone'),
                ownerEmail: formData.get('ownerEmail'),
                ownerAddress: formData.get('ownerAddr'),
                dwellingType: formData.get('dwellingType'),
                yearBuilt: formData.get('yearBuilt') ? parseInt(formData.get('yearBuilt')) : null,
                propertyValue: formData.get('propertyValue') ? parseFloat(formData.get('propertyValue').replace(/[$,]/g, '')) : null,
                homeStories: formData.get('home_stories') ? parseInt(formData.get('home_stories')) : null,
                responseDetails: formData.get('response_details'),
                conditionsOnArrival: formData.get('conditions_on_arrival'),
                notes: formData.get('notes')
            };

            // Add type-specific fields to incidentDetail
            if (incidentType === 'fire') {
                incidentDetail.fireType = formData.get('fire_type');
                incidentDetail.structureType = formData.get('structure_type');
                incidentDetail.smokeType = formData.get('smoke_type');
                incidentDetail.smokeSeverity = formData.get('smoke_severity');
                incidentDetail.peopleAffected = parseInt(formData.get('people_affected')) || 0;
                incidentDetail.evacuationStatus = formData.get('evacuation_status') || 'none';
                incidentDetail.estimatedDamage = parseFloat(formData.get('estimated_damage')) || null;
                incidentDetail.responderCount = parseInt(formData.get('responder_count')) || null;
                // Fire operation details go to responseDetails field
                if (formData.get('fire_op')) {
                    incidentDetail.responseDetails = formData.get('fire_op');
                }
            } else if (incidentType === 'water') {
                incidentDetail.waterType = formData.get('water_type');
                incidentDetail.areaAffected = formData.get('area_affected');
                incidentDetail.waterLevel = formData.get('water_level');
                incidentDetail.damageExtent = formData.get('damage_extent');
            }

            // Complete payload with both incident data and incident detail
            const payload = {
                ...incidentData,
                incidentDetail: incidentDetail,
                notifySubscribers: formData.get('notify_subscribers') === 'on'
            };

            // Show loading state
            loadingOverlay.classList.add('active');
            submitButton.disabled = true;

            // Use API to update the incident with incident details
            API.incidents.update(incidentId, incidentData, incidentDetail)
                .then(async response => {
                    console.log('Update response:', response);

                    if (response && response.success !== false) {
                        const successMsg = response.msg || 'Incident updated successfully!';
                        showNotification(successMsg, 'success');

                        // If notifications are requested, send them after successful update
                        const shouldNotify = formData.get('notify_subscribers') === 'on';
                        if (shouldNotify) {
                            console.log('Sending update notifications to subscribers...');
                            try {
                                // Call the notification endpoint with update flag
                                const notificationResponse = await fetch(`/api/incidents/${incidentId}/notify`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token')}`
                                    },
                                    body: JSON.stringify({ isUpdate: true })
                                }).then(res => res.json());

                                console.log('Notification response:', notificationResponse);

                                if (notificationResponse && notificationResponse.success !== false) {
                                    const notificationCount = notificationResponse.notificationsSent || 0;
                                    const usersFound = notificationResponse.usersFound || 0;

                                    if (notificationCount > 0) {
                                        showNotification(`Update notifications sent to ${notificationCount} subscribers!`, 'success');
                                    } else if (usersFound === 0) {
                                        showNotification('No subscribers found in the affected area for notifications.', 'warning');
                                    } else {
                                        showNotification('No subscribers opted in for this type of incident notification.', 'info');
                                    }
                                } else {
                                    const notificationError = notificationResponse?.msg || 'Unknown notification error';
                                    showNotification(`Incident updated, but notification failed: ${notificationError}`, 'warning');
                                }
                            } catch (notificationError) {
                                console.error('Error sending notifications:', notificationError);
                                showNotification('Incident updated, but failed to send notifications.', 'warning');
                            }
                        }

                        // Redirect back to view page after a delay
                        setTimeout(() => {
                            window.location.href = `view-incident.html?id=${incidentId}`;
                        }, 2000); // Increased delay to show notification messages
                    } else {
                        const errorMsg = response?.msg || response?.message || 'Unknown error';
                        showNotification('Error updating incident: ' + errorMsg, 'error');
                    }

                    loadingOverlay.classList.remove('active');
                    submitButton.disabled = false;
                })
                .catch(error => {
                    loadingOverlay.classList.remove('active');
                    submitButton.disabled = false;
                    console.error('Error updating incident:', error);
                    showNotification('Error connecting to server.', 'error');
                });
        });

        // Owner Data Refresh Button
        document.getElementById('refreshOwnerDataBtn')?.addEventListener('click', function() {
            const address = document.getElementById('address').value;
            const city = document.getElementById('city').value;
            const state = document.getElementById('state').value;
            const zip = document.getElementById('zip_code').value;

            if (!address || !city || !state || !zip) {
                showNotification('Please ensure the address fields are filled out correctly', 'error');
                return;
            }

            const button = this;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Fetching data...';
            button.disabled = true;

            // Make real API call to Estated
            fetch('/api/locations/property', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                },
                body: JSON.stringify({
                    address,
                    city,
                    state,
                    zip
                })
            })
            .then(response => {
                if (!response.ok) {
                    // Try to parse error message from response
                    return response.json().then(errorData => {
                        throw new Error(errorData.error || 'Failed to retrieve owner data');
                    }).catch(() => {
                        throw new Error('Failed to retrieve owner data');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Populate owner fields with real data
                    const owner1Field = document.getElementById('owner1');
                    if (owner1Field) owner1Field.value = data.property.owner1 || '';

                    const owner2Field = document.getElementById('owner2');
                    if (owner2Field) owner2Field.value = data.property.owner2 || '';

                    const ownerAddrField = document.getElementById('ownerAddr');
                    if (ownerAddrField) ownerAddrField.value = data.property.ownerAddress || '';

                    const ownerPhoneField = document.getElementById('ownerPhone');
                    if (ownerPhoneField) ownerPhoneField.value = data.property.ownerPhone || '';

                    const ownerEmailField = document.getElementById('ownerEmail');
                    if (ownerEmailField) ownerEmailField.value = data.property.ownerEmail || '';

                    const dwellingTypeField = document.getElementById('dwellingType');
                    if (dwellingTypeField) dwellingTypeField.value = data.property.dwellingType || '';

                    const yearBuiltField = document.getElementById('yearBuilt');
                    if (yearBuiltField) yearBuiltField.value = data.property.yearBuilt || '';

                    const propertyValueField = document.getElementById('propertyValue');
                    if (propertyValueField) propertyValueField.value = data.property.valuationFormatted || '';

                    // Show success message
                    showNotification('Property owner information refreshed successfully from Estated API', 'success');
                } else {
                    throw new Error(data.error || 'Failed to retrieve property data');
                }
            })
            .catch(error => {
                console.error('Error fetching owner data:', error);
                showNotification('Failed to retrieve property data: ' + error.message, 'error');
            })
            .finally(() => {
                // Reset button state
                button.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
                button.disabled = false;
            });
        });
    </script>

    <!-- Load shared utilities -->
    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
</body>
</html>
