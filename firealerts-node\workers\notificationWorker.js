/**
 * Notification Worker
 *
 * This worker processes pending notifications from the database and sends them
 * via the appropriate channels (email, SMS, push notifications).
 */

const db = require('../models');
const nodemailer = require('nodemailer');
// Add Mailgun client
const formData = require('form-data');
const Mailgun = require('mailgun.js');
const mailgun = new Mailgun(formData);

// Initialize emailTransporter that will be set later
let emailTransporter = null;
let mailgunClient = null;

// Function to initialize email providers
async function initializeEmailProviders() {
  try {
    console.log('Initializing email providers...');

    // Try unified Mailgun configuration first
    let mailgunApiKey = null;
    let mailgunDomain = null;

    const mailgunConfig = await db.systemSetting.getValue('mailgun_config');
    if (mailgunConfig) {
      try {
        const config = JSON.parse(mailgunConfig);
        mailgunApiKey = config.api_key;
        mailgunDomain = config.domain;
        console.log('Using unified Mailgun configuration');
      } catch (parseError) {
        console.error('Error parsing unified Mailgun configuration:', parseError);
      }
    }

    // Fallback to separate entries for backward compatibility
    if (!mailgunApiKey || !mailgunDomain) {
      console.log('Unified config not found, checking separate entries...');
      mailgunApiKey = mailgunApiKey || await db.systemSetting.getValue('mailgun_api_key');
      mailgunDomain = mailgunDomain || await db.systemSetting.getValue('mailgun_domain');
    }

    if (mailgunApiKey && mailgunDomain) {
      console.log('Using Mailgun for email delivery');
      // Initialize Mailgun client
      mailgunClient = mailgun.client({ username: 'api', key: mailgunApiKey });
      // Store domain for later use
      global.mailgunDomain = mailgunDomain;
      console.log(`✅ Mailgun configured with domain: ${mailgunDomain}`);
    } else {
      console.log('Mailgun not configured, using SMTP fallback');
    }

    // Set up nodemailer as fallback
    emailTransporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || await db.systemSetting.getValue('smtp_host', 'smtp.example.com'),
      port: process.env.SMTP_PORT || await db.systemSetting.getValue('smtp_port', '587'),
      secure: process.env.SMTP_SECURE === 'true' || await db.systemSetting.getValue('smtp_secure') === 'true',
      auth: {
        user: process.env.SMTP_USER || await db.systemSetting.getValue('smtp_user', '<EMAIL>'),
        pass: process.env.SMTP_PASSWORD || await db.systemSetting.getValue('smtp_password', 'password')
      }
    });
  } catch (error) {
    console.error('Error initializing email providers:', error);
    throw error;
  }
}

// Import the real SMS service
const smsService = require('../services/smsService');

// Configure push notification provider (placeholder implementation)
// This would be replaced with Firebase Cloud Messaging or another push service
const pushProvider = {
  sendPush: async (deviceToken, title, body) => {
    console.log(`[PUSH SIMULATION] Sending push notification to device ${deviceToken}: ${title} - ${body}`);
    // In a real implementation, this would call a push notification API
    return { success: true, messageId: 'push_' + Date.now() };
  }
};

/**
 * Process a batch of pending notifications
 */
async function processNotifications(batchSize = 50) {
  console.log('Starting notification processing job...');

  // Initialize email providers if they haven't been initialized yet
  if (!emailTransporter) {
    await initializeEmailProviders();
  }

  try {    // Find pending notifications
    const pendingNotifications = await db.notification.findAll({
      where: { status: 'pending' },
      include: [
        {
          model: db.incident,
          include: [
            db.incidentType,
            db.status,
            db.incidentDetail
          ]
        },
        {
          model: db.user,
          include: [db.company],
          required: true  // Ensure user is included
        }
      ],
      limit: batchSize,
      order: [['createdAt', 'ASC']]
    });

    console.log(`Found ${pendingNotifications.length} pending notifications to process`);

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
        await processNotification(notification);
      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error);

        // Update notification with error status
        await notification.update({
          status: 'failed',
          errorMessage: error.message,
          retryCount: notification.retryCount + 1
        });
      }
    }

    console.log('Notification processing job completed');
  } catch (error) {
    console.error('Error in notification worker:', error);
  }
}

/**
 * Process a single notification
 */
async function processNotification(notification) {
    console.log(`Processing notification ${notification.id} of type ${notification.type}`);

    // Get the user and incident
    const user = notification.user;
    const incident = notification.incident;

    if (!user) {
        throw new Error('User not found for notification');
    }

    let result;

    // Send the notification based on type
    switch (notification.type) {
        case 'email':
            result = await sendEmailNotification(notification, user, incident);
            break;

        case 'sms':
            result = await sendSmsNotification(notification, user, incident);
            break;

        case 'push':
            result = await sendPushNotification(notification, user, incident);
            break;

        default:
            throw new Error(`Unknown notification type: ${notification.type}`);
    }

    // Update notification status
    await notification.update({
        status: 'sent',
        sentAt: new Date(),
        meta: { ...notification.meta, deliveryResult: result }
    });

    console.log(`Successfully sent ${notification.type} notification ${notification.id}`);
    return result;
}

/**
 * Build comprehensive incident data for email
 */
async function buildIncidentEmailData(incident, user) {
    // Get incident type and status names
    const incidentType = incident?.incidentType?.name || 'Incident';
    const status = incident?.status?.name || 'Active';

    // Format date and time
    const incidentDate = new Date(incident.incidentDate);
    const formattedDate = incidentDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    const formattedTime = incidentDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
    });

    // Build address components
    const fullAddress = `${incident.address}, ${incident.city}, ${incident.county} County, ${incident.state}`;
    const zipCode = incident.zip ? ` ${incident.zip}` : '';
    const addressWithZip = `${fullAddress}${zipCode}`;

    // Create Google Maps link
    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressWithZip)}`;

    // Create incident details link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost';
    const incidentDetailsUrl = `${baseUrl}/view-incident.html?id=${incident.id}`;

    // Create unsubscribe link
    const unsubscribeUrl = `${baseUrl}/notifications.html`;

    // Determine severity color and icon
    const severityConfig = {
        'critical': { color: '#e53935', icon: '🔴', bgColor: '#ffebee' },
        'major': { color: '#ff8f00', icon: '🟠', bgColor: '#fff8e1' },
        'moderate': { color: '#1e88e5', icon: '🟡', bgColor: '#e3f2fd' },
        'minor': { color: '#28a745', icon: '🟢', bgColor: '#e8f5e8' }
    };

    const severityInfo = severityConfig[incident.severity] || severityConfig['moderate'];

    return {
        // Basic incident info
        id: incident.id,
        title: incident.title,
        incidentType,
        status,
        severity: incident.severity || 'moderate',
        severityInfo,

        // Location info
        address: incident.address,
        city: incident.city,
        county: incident.county,
        state: incident.state,
        zip: incident.zip,
        fullAddress,
        addressWithZip,
        mapsUrl,

        // Time info
        incidentDate,
        formattedDate,
        formattedTime,

        // Additional details
        description: incident.description,
        crossStreet: incident.crossStreet,
        alarmLevel: incident.alarmLevel,
        dispatchInfo: incident.dispatchInfo,

        // User info
        userName: `${user.firstName} ${user.lastName}`,
        userEmail: user.email,

        // Links
        incidentDetailsUrl,
        unsubscribeUrl,

        // Incident details (if available)
        incidentDetail: incident.incidentDetail || null
    };
}

/**
 * Build plain text email content
 */
function buildPlainTextEmail(data) {
    return `
FIREALERTS911 EMERGENCY NOTIFICATION

${data.severityInfo.icon} ${data.severity.toUpperCase()} ${data.incidentType.toUpperCase()} ALERT

Incident Details:
- Type: ${data.incidentType}
- Severity: ${data.severity.toUpperCase()}
- Status: ${data.status}
- Incident ID: #${data.id}

Location:
${data.addressWithZip}
${data.crossStreet ? `Cross Street: ${data.crossStreet}` : ''}

Date & Time:
${data.formattedDate} at ${data.formattedTime}

${data.description ? `Description:\n${data.description}\n` : ''}
${data.dispatchInfo ? `Dispatch Info:\n${data.dispatchInfo}\n` : ''}

View on Google Maps: ${data.mapsUrl}
View Full Details: ${data.incidentDetailsUrl}

This alert was sent to ${data.userEmail} because you are subscribed to emergency notifications for ${data.county} County, ${data.state}.

To manage your notification preferences, visit: ${data.unsubscribeUrl}

FireAlerts911 - Keeping Communities Safe
    `.trim();
}

/**
 * Build HTML email content with professional styling
 */
function buildHtmlEmail(data) {
    const incidentDetailsSection = data.incidentDetail ? buildIncidentDetailsSection(data.incidentDetail) : '';

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 Emergency Notification</title>
    <style>
        /* Reset styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* Base styles */
        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #1a2035 0%, #272e48 100%);
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .alert-badge {
            display: inline-block;
            background-color: ${data.severityInfo.color};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            margin-top: 10px;
        }

        /* Alert banner */
        .alert-banner {
            background-color: ${data.severityInfo.bgColor};
            border-left: 4px solid ${data.severityInfo.color};
            padding: 20px;
            text-align: center;
        }

        .alert-title {
            font-size: 20px;
            font-weight: bold;
            color: ${data.severityInfo.color};
            margin-bottom: 10px;
        }

        .alert-subtitle {
            font-size: 16px;
            color: #666;
        }

        /* Content sections */
        .content {
            padding: 20px;
        }

        .section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1a2035;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 10px;
            font-size: 20px;
        }

        /* Info grid */
        .info-grid {
            display: table;
            width: 100%;
        }

        .info-row {
            display: table-row;
        }

        .info-label {
            display: table-cell;
            font-weight: bold;
            color: #666;
            padding: 8px 15px 8px 0;
            vertical-align: top;
            width: 120px;
        }

        .info-value {
            display: table-cell;
            padding: 8px 0;
            vertical-align: top;
        }

        /* Buttons */
        .button-container {
            text-align: center;
            margin: 25px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px 10px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #1e88e5;
            color: white;
        }

        .btn-secondary {
            background-color: #28a745;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        /* Footer */
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        .footer-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }

        .footer-links {
            font-size: 12px;
        }

        .footer-links a {
            color: #1e88e5;
            text-decoration: none;
            margin: 0 10px;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }

            .content {
                padding: 15px !important;
            }

            .info-label {
                display: block !important;
                width: auto !important;
                padding-bottom: 5px !important;
            }

            .info-value {
                display: block !important;
                padding-top: 0 !important;
                margin-bottom: 15px !important;
            }

            .btn {
                display: block !important;
                margin: 10px 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚨 FireAlerts911</div>
            <div>Emergency Notification System</div>
            <div class="alert-badge">${data.severityInfo.icon} ${data.severity.toUpperCase()} ALERT</div>
        </div>

        <!-- Alert Banner -->
        <div class="alert-banner">
            <div class="alert-title">${data.incidentType} Reported</div>
            <div class="alert-subtitle">${data.fullAddress}</div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Incident Details -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📋</span>
                    Incident Information
                </div>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Incident ID:</div>
                        <div class="info-value">#${data.id}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Type:</div>
                        <div class="info-value">${data.incidentType}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Severity:</div>
                        <div class="info-value">
                            <span style="color: ${data.severityInfo.color}; font-weight: bold;">
                                ${data.severityInfo.icon} ${data.severity.toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Status:</div>
                        <div class="info-value">${data.status}</div>
                    </div>
                    ${data.alarmLevel ? `
                    <div class="info-row">
                        <div class="info-label">Alarm Level:</div>
                        <div class="info-value">${data.alarmLevel}</div>
                    </div>
                    ` : ''}
                </div>
            </div>

            <!-- Location Details -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📍</span>
                    Location Details
                </div>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Address:</div>
                        <div class="info-value">${data.addressWithZip}</div>
                    </div>
                    ${data.crossStreet ? `
                    <div class="info-row">
                        <div class="info-label">Cross Street:</div>
                        <div class="info-value">${data.crossStreet}</div>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <div class="info-label">County:</div>
                        <div class="info-value">${data.county} County, ${data.state}</div>
                    </div>
                </div>
            </div>

            <!-- Time Information -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">🕐</span>
                    Time Information
                </div>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Date:</div>
                        <div class="info-value">${data.formattedDate}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Time:</div>
                        <div class="info-value">${data.formattedTime}</div>
                    </div>
                </div>
            </div>

            ${data.description ? `
            <!-- Description -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📝</span>
                    Description
                </div>
                <p style="color: #666; line-height: 1.6;">${data.description}</p>
            </div>
            ` : ''}

            ${data.dispatchInfo ? `
            <!-- Dispatch Information -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📡</span>
                    Dispatch Information
                </div>
                <p style="color: #666; line-height: 1.6;">${data.dispatchInfo}</p>
            </div>
            ` : ''}

            ${incidentDetailsSection}

            <!-- Action Buttons -->
            <div class="button-container">
                <a href="${data.mapsUrl}" class="btn btn-primary" target="_blank">
                    📍 View on Google Maps
                </a>
                <a href="${data.incidentDetailsUrl}" class="btn btn-secondary" target="_blank">
                    🔍 View Full Details
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This alert was sent to <strong>${data.userEmail}</strong> because you are subscribed to emergency notifications for <strong>${data.county} County, ${data.state}</strong>.
            </div>
            <div class="footer-links">
                <a href="${data.unsubscribeUrl}" target="_blank">Manage Notifications</a> |
                <a href="${data.incidentDetailsUrl}" target="_blank">View Incident</a> |
                <a href="mailto:<EMAIL>">Contact Support</a>
            </div>
            <div style="margin-top: 15px; font-size: 11px; color: #999;">
                FireAlerts911 - Keeping Communities Safe<br>
                Emergency Notification System
            </div>
        </div>
    </div>
</body>
</html>
    `.trim();
}

/**
 * Build incident details section for HTML email
 */
function buildIncidentDetailsSection(incidentDetail) {
    if (!incidentDetail) return '';

    const details = [];

    // Fire-specific details
    if (incidentDetail.structureType) details.push(['Structure Type', incidentDetail.structureType]);
    if (incidentDetail.fireType) details.push(['Fire Type', incidentDetail.fireType]);
    if (incidentDetail.smokeType) details.push(['Smoke Type', incidentDetail.smokeType]);

    // Water-specific details
    if (incidentDetail.waterType) details.push(['Water Type', incidentDetail.waterType]);
    if (incidentDetail.waterLevel) details.push(['Water Level', incidentDetail.waterLevel]);

    // General details
    if (incidentDetail.damageExtent) details.push(['Damage Extent', incidentDetail.damageExtent]);
    if (incidentDetail.areaAffected) details.push(['Area Affected', incidentDetail.areaAffected]);
    if (incidentDetail.evacuationStatus && incidentDetail.evacuationStatus !== 'none') {
        details.push(['Evacuation Status', incidentDetail.evacuationStatus.toUpperCase()]);
    }
    if (incidentDetail.peopleAffected > 0) details.push(['People Affected', incidentDetail.peopleAffected]);
    if (incidentDetail.responderCount) details.push(['Responders', incidentDetail.responderCount]);

    // Property details
    if (incidentDetail.propertyOwner) details.push(['Property Owner', incidentDetail.propertyOwner]);
    if (incidentDetail.dwellingType) details.push(['Dwelling Type', incidentDetail.dwellingType]);
    if (incidentDetail.yearBuilt) details.push(['Year Built', incidentDetail.yearBuilt]);

    if (details.length === 0) return '';

    const detailRows = details.map(([label, value]) => `
        <div class="info-row">
            <div class="info-label">${label}:</div>
            <div class="info-value">${value}</div>
        </div>
    `).join('');

    return `
        <!-- Additional Details -->
        <div class="section">
            <div class="section-title">
                <span class="section-icon">🔍</span>
                Additional Details
            </div>
            <div class="info-grid">
                ${detailRows}
            </div>
        </div>
    `;
}

/**
 * Send email notification
 */
async function sendEmailNotification(notification, user, incident) {
    console.log(`Sending email to ${user.email}`);

    // Get email settings
    const fromEmail = await db.systemSetting.getValue('email_from', process.env.EMAIL_FROM || '<EMAIL>');
    console.log(`Using from address: ${fromEmail}`);

    // Use the enhanced email template system
    const emailTemplates = require('../services/emailTemplates');

    // Determine which template to use based on notification type
    const templateType = notification.templateType || (notification.notificationType === 'update' ? 'incidentUpdate' : 'incident');

    console.log(`🔍 Template Detection Debug:`);
    console.log(`   - notification.templateType: ${notification.templateType}`);
    console.log(`   - notification.notificationType: ${notification.notificationType}`);
    console.log(`   - Determined templateType: ${templateType}`);
    console.log(`   - emailTemplates.incidentUpdate exists: ${!!emailTemplates.incidentUpdate}`);

    let template;
    if (templateType === 'incidentUpdate' && emailTemplates.incidentUpdate) {
        console.log('✅ Using enhanced Gmail-compatible incidentUpdate template');
        template = emailTemplates.incidentUpdate.generate(user, incident, notification);
    } else {
        console.log('⚠️  Using standard incident template');
        console.log(`   - Reason: templateType=${templateType}, incidentUpdate exists=${!!emailTemplates.incidentUpdate}`);
        template = emailTemplates.incident.generate(user, incident, notification);
    }

    // Create email content using the template
    const emailContent = {
        from: `"FireAlerts911" <${fromEmail}>`,
        to: user.email,
        subject: template.subject,
        text: template.text,
        html: template.html
    };

    // Send email using Mailgun if available, otherwise fallback to SMTP
    let result;

    try {
        if (mailgunClient) {
            // Use the domain stored during initialization, or fallback to environment/database
            let mailgunDomain = global.mailgunDomain;

            if (!mailgunDomain) {
                // Try to get from unified config first
                const mailgunConfig = await db.systemSetting.getValue('mailgun_config');
                if (mailgunConfig) {
                    try {
                        const config = JSON.parse(mailgunConfig);
                        mailgunDomain = config.domain;
                    } catch (parseError) {
                        console.error('Error parsing Mailgun config for domain:', parseError);
                    }
                }

                // Fallback to separate entry or environment
                if (!mailgunDomain) {
                    mailgunDomain = process.env.MAILGUN_DOMAIN || await db.systemSetting.getValue('mailgun_domain');
                }
            }

            if (!mailgunDomain) {
                throw new Error('Mailgun domain not configured');
            }

            console.log(`Using Mailgun with domain ${mailgunDomain}`);

            // Use Mailgun to send email
            const mgResult = await mailgunClient.messages.create(mailgunDomain, {
                from: emailContent.from,
                to: emailContent.to,
                subject: emailContent.subject,
                text: emailContent.text,
                html: emailContent.html
            });

            console.log(`Email sent via Mailgun to ${user.email}: ${mgResult.id}`);

            result = {
                provider: 'mailgun',
                messageId: mgResult.id
            };
        } else {
            // Fallback to SMTP
            console.log('Falling back to SMTP for email delivery');
            const smtpResult = await emailTransporter.sendMail(emailContent);

            console.log(`Email sent via SMTP to ${user.email}: ${smtpResult.messageId}`);

            result = {
                provider: 'nodemailer',
                messageId: smtpResult.messageId
            };
        }

        return result;
    } catch (error) {
        console.error('Error sending email notification:', error);

        // If Mailgun fails, try SMTP as fallback
        if (error.message.includes('Mailgun') && emailTransporter) {
            console.log('Mailgun failed, trying SMTP fallback');
            try {
                const smtpResult = await emailTransporter.sendMail(emailContent);

                console.log(`Email sent via SMTP fallback to ${user.email}: ${smtpResult.messageId}`);

                return {
                    provider: 'nodemailer_fallback',
                    messageId: smtpResult.messageId
                };
            } catch (smtpError) {
                console.error('SMTP fallback also failed:', smtpError);
                throw new Error(`Email delivery failed: ${error.message}, SMTP fallback: ${smtpError.message}`);
            }
        }

        throw error;
    }
}

/**
 * Send SMS notification
 */
async function sendSmsNotification(notification, user, incident) {
    // Verify phone number exists
    const phoneNumber = user.cellPhone || user.phone;
    if (!phoneNumber) {
        throw new Error('User has no phone number for SMS notification');
    }

    console.log(`Sending SMS to ${phoneNumber}`);

    try {
        // Send SMS using the real SMS service
        const result = await smsService.sendSms(phoneNumber, notification.content);

        return {
            provider: result.provider || 'sms',
            messageId: result.messageId,
            status: result.status,
            cost: result.cost
        };
    } catch (error) {
        console.error(`SMS delivery failed for user ${user.id}:`, error);
        throw new Error(`SMS delivery failed: ${error.message}`);
    }
}

/**
 * Send push notification
 */
async function sendPushNotification(notification, user, incident) {
    // In a real implementation, you would get the device token from the user
    const deviceToken = user.deviceToken || user.id.toString();

    console.log(`Sending push notification to user ${user.id}`);

    // Get incident type
    const incidentTypeText = incident?.incidentType?.name || 'Incident';

    // Send push notification
    const result = await pushProvider.sendPush(
        deviceToken,
        `New ${incidentTypeText} Alert`,
        notification.content
    );

    return {
        provider: 'push',
        messageId: result.messageId
    };
}

module.exports = {
    processNotifications,
    processNotification // Export this for direct use (welcome email)
};
