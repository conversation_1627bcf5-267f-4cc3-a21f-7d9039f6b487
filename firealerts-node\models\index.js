'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
// Assuming config.json or similar exists and is correctly set up
const config = require(__dirname + '/../config/config.js')[env];
const db = {};

let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], config);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, config);
}

fs
  .readdirSync(__dirname)
  .filter(file => {
    // Exclude deleted model files
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      file.slice(-3) === '.js' &&
      file.indexOf('.test.js') === -1
    );
  })
  .forEach(file => {
    const modelModule = require(path.join(__dirname, file));
    if (typeof modelModule === 'function') {
      const model = modelModule(sequelize, Sequelize.DataTypes);
      db[model.name] = model;
    }
  });

Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

// Define manual associations
// User associations
if (db.user && db.incident) {
  db.user.hasMany(db.incident, { foreignKey: 'user_id' });
  db.incident.belongsTo(db.user, { foreignKey: 'user_id' });
}

if (db.user && db.activity) {
  db.user.hasMany(db.activity, { foreignKey: 'user_id' });
  db.activity.belongsTo(db.user, { foreignKey: 'user_id' });
}

// Company associations
if (db.company && db.user) {
  db.company.hasMany(db.user, { foreignKey: 'company_id' });
  db.user.belongsTo(db.company, { foreignKey: 'company_id' });
}

// Incident associations
if (db.incident && db.incidentType) {
  db.incident.belongsTo(db.incidentType, { foreignKey: 'incident_type_id' });
  db.incidentType.hasMany(db.incident, { foreignKey: 'incident_type_id' });
}

if (db.incident && db.status) {
  db.incident.belongsTo(db.status, { foreignKey: 'status_id' });
  db.status.hasMany(db.incident, { foreignKey: 'status_id' });
}

// Remove location association as it doesn't exist in the schema
// if (db.incident && db.location) {
//   db.incident.belongsTo(db.location, { foreignKey: 'location_id' });
//   db.location.hasMany(db.incident, { foreignKey: 'location_id' });
// }

// Add proper association between incident and incidentDetail
if (db.incident && db.incidentDetail) {
  db.incident.hasOne(db.incidentDetail, { foreignKey: 'incident_id', onDelete: 'CASCADE' });
  db.incidentDetail.belongsTo(db.incident, { foreignKey: 'incident_id' });
}

if (db.incident && db.notification) {
  db.incident.hasMany(db.notification, { foreignKey: 'incident_id' });
  db.notification.belongsTo(db.incident, { foreignKey: 'incident_id' });
}

if (db.incident && db.media) {
  db.incident.hasMany(db.media, { foreignKey: 'incident_id', onDelete: 'CASCADE' });
  db.media.belongsTo(db.incident, { foreignKey: 'incident_id' });
}

// Notification associations
if (db.notification && db.user) {
  db.notification.belongsTo(db.user, { foreignKey: 'user_id' });
  db.user.hasMany(db.notification, { foreignKey: 'user_id' });
}

// State location associations
if (db.state) {
  if (db.county) {
    db.state.hasMany(db.county, { foreignKey: 'state_id', as: 'counties' });
    db.county.belongsTo(db.state, { foreignKey: 'state_id', as: 'stateObj' }); // Renamed to avoid collision with 'state' field
  }

  if (db.zip_code) {
    db.state.hasMany(db.zip_code, { foreignKey: 'state_id', as: 'zipCodes' });
    db.zip_code.belongsTo(db.state, { foreignKey: 'state_id', as: 'stateObj' });
  }
}

// County and ZIP code associations
if (db.county && db.zip_code) {
  db.county.hasMany(db.zip_code, { foreignKey: 'county_id', as: 'zipCodes' });
  db.zip_code.belongsTo(db.county, { foreignKey: 'county_id', as: 'countyObj' });
}

// User associations (consolidated from subscriber associations)
if (db.user && db.company) {
  db.user.belongsTo(db.company, { foreignKey: 'company_id' });
  db.company.hasMany(db.user, { foreignKey: 'company_id' });
}

// Add association: User hasOne Subscription (updated from subscriber)
if (db.user && db.subscription) {
  db.user.hasOne(db.subscription, { foreignKey: 'user_id' });
  db.subscription.belongsTo(db.user, { foreignKey: 'user_id' });
}

// Add association: Company belongsTo CompanyType
if (db.company && db.companyType) {
  db.company.belongsTo(db.companyType, { foreignKey: 'company_type_id' });
  db.companyType.hasMany(db.company, { foreignKey: 'company_type_id' });
}

// Add associations for user preferences, locations, and subscriptions
if (db.user && db.userPreference) {
  db.user.hasOne(db.userPreference, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  db.userPreference.belongsTo(db.user, { foreignKey: 'user_id' });
}

if (db.user && db.userLocation) {
  db.user.hasMany(db.userLocation, { foreignKey: 'user_id', as: 'locations', onDelete: 'CASCADE' });
  db.userLocation.belongsTo(db.user, { foreignKey: 'user_id' });
}

if (db.user && db.userSubscription) {
  db.user.hasMany(db.userSubscription, { foreignKey: 'user_id', as: 'subscriptions', onDelete: 'CASCADE' });
  db.userSubscription.belongsTo(db.user, { foreignKey: 'user_id' });
}

if (db.county && db.userSubscription) {
  db.county.hasMany(db.userSubscription, { foreignKey: 'county_id', as: 'subscribers', onDelete: 'CASCADE' });
  db.userSubscription.belongsTo(db.county, { foreignKey: 'county_id' });
}

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
