# FireAlerts911 Docker Setup

This directory contains everything needed to run the FireAlerts911 system using Docker containers.

## Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

## Quick Start

### On Linux/macOS:

```bash
# Start the containers
./start.sh

# View logs
docker-compose logs -f

# Stop the containers
./stop.sh

# Rebuild containers (if you make code changes)
./rebuild.sh

# Reset the database (caution: deletes all data)
./reset-db.sh
```

### On Windows:

```bash
# Start the containers
start.bat

# View logs
docker-compose logs -f

# Stop the containers
docker-compose down

# Rebuild containers (if you make code changes)
docker-compose build --no-cache
docker-compose up -d

# Reset the database (caution: deletes all data)
# First stop containers, then remove volume, then start again
docker-compose down
docker volume rm docker_mysql_data
docker-compose up -d
```

## Default Credentials

### Admin User
- Username: admin
- Password: admin123

### Dispatcher User
- Username: dispatcher
- Password: dispatch123

## System Components

The Docker setup includes:

1. **Node.js Application Container** - Runs the FireAlerts911 application
2. **MySQL Database Container** - Stores all system data
3. **Docker Network** - Provides communication between containers
4. **Persistent Volume** - Keeps the database data between container restarts

## Configuration

You can modify the environment variables in the `.env` file or directly in `docker-compose.yml` to customize the application.

## Troubleshooting

If you encounter issues:

1. **Check logs**:
   ```
   docker-compose logs -f
   ```

2. **Restart containers**:
   ```
   docker-compose restart
   ```

3. **Rebuild from scratch**:
   ```
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

4. **Database connection issues**:
   ```
   docker exec -it firealerts911-db mysql -ufireuser -pfirepass -e "SHOW DATABASES;"
   ```
