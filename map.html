<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Incident Map</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        .map-container {
            height: calc(100vh - 180px);
            min-height: 400px;
        }

        .map-sidebar {
            position: absolute;
            top: 80px;
            right: 20px;
            width: 300px;
            background-color: var(--secondary-dark);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            z-index: 1000;
            overflow: hidden;
        }

        .map-filter-toggle {
            position: absolute;
            top: 80px;
            right: 20px;
            z-index: 1001;
            padding: 10px;
            border-radius: 5px;
            background-color: var(--secondary-dark);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .incident-mini {
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
        }

        .incident-mini:hover {
            background-color: var(--hover-bg);
        }

        @media (max-width: 768px) {
            .map-sidebar {
                display: none;
                width: calc(100% - 40px);
                max-height: 400px;
                overflow-y: auto;
            }

            .map-filter-toggle {
                display: block;
            }

            .map-sidebar.active {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- Map Page Content -->
            <div class="card" style="margin-bottom: 0;">
                <div class="card-header">
                    <div class="card-title">Incident Map</div>
                    <div class="card-actions">
                        <div style="display: flex; gap: 10px;">
                            <button id="toggleAllIncidents" class="btn btn-outline btn-sm active">All</button>
                            <button id="toggleFireIncidents" class="btn btn-danger btn-sm">
                                <i class="fas fa-fire"></i> Fire
                            </button>
                            <button id="toggleWaterIncidents" class="btn btn-primary btn-sm">
                                <i class="fas fa-water"></i> Water
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map Container -->
            <div class="map-container" id="incident-map">
                <!-- Map will be rendered here -->
            </div>

            <!-- Mobile Toggle for Filters -->
            <button id="mobileFilterToggle" class="map-filter-toggle btn-icon">
                <i class="fas fa-list"></i>
            </button>

            <!-- Map Sidebar -->
            <div class="map-sidebar">
                <div style="padding: 15px; border-bottom: 1px solid var(--border-color);">
                    <h4 style="margin: 0 0 10px 0;">Active Incidents</h4>
                    <input type="text" class="form-control" placeholder="Search incidents..." style="width: 100%;">
                </div>
                <div style="max-height: calc(100vh - 300px); overflow-y: auto;">
                    <!-- Incident List for Map -->
                    <div class="incident-mini" data-id="1086" data-type="fire" data-lat="40.7128" data-lng="-74.0060">
                        <div style="display: flex; gap: 10px;">
                            <div class="incident-icon fire" style="width: 30px; height: 30px; font-size: 12px;">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; margin-bottom: 3px;">Residential Structure Fire</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">123 Oak Avenue</div>
                            </div>
                        </div>
                    </div>

                    <div class="incident-mini" data-id="1085" data-type="water" data-lat="40.7200" data-lng="-74.0100">
                        <div style="display: flex; gap: 10px;">
                            <div class="incident-icon water" style="width: 30px; height: 30px; font-size: 12px;">
                                <i class="fas fa-water"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; margin-bottom: 3px;">Water Main Break</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">456 Elm Street</div>
                            </div>
                        </div>
                    </div>

                    <div class="incident-mini" data-id="1084" data-type="fire" data-lat="40.7300" data-lng="-74.0200">
                        <div style="display: flex; gap: 10px;">
                            <div class="incident-icon fire" style="width: 30px; height: 30px; font-size: 12px;">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; margin-bottom: 3px;">Brush Fire</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Westside Park</div>
                            </div>
                        </div>
                    </div>

                    <div class="incident-mini" data-id="1083" data-type="water" data-lat="40.7400" data-lng="-74.0300">
                        <div style="display: flex; gap: 10px;">
                            <div class="incident-icon water" style="width: 30px; height: 30px; font-size: 12px;">
                                <i class="fas fa-water"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; margin-bottom: 3px;">Flooding</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Riverfront District</div>
                            </div>
                        </div>
                    </div>

                    <div class="incident-mini" data-id="1082" data-type="fire" data-lat="40.7500" data-lng="-74.0400">
                        <div style="display: flex; gap: 10px;">
                            <div class="incident-icon fire" style="width: 30px; height: 30px; font-size: 12px;">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 500; margin-bottom: 3px;">Commercial Building Fire</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">789 Main Street</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('map');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            // Initialize map with same default view as index page
            const map = L.map('incident-map').setView([39.8283, -98.5795], 4);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // Create fire and water icons
            const fireIcon = L.divIcon({
                html: '<div style="background-color: #e53935; width: 30px; height: 30px; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white;"><i class="fas fa-fire"></i></div>',
                className: 'custom-marker',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            const waterIcon = L.divIcon({
                html: '<div style="background-color: #1e88e5; width: 30px; height: 30px; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white;"><i class="fas fa-water"></i></div>',
                className: 'custom-marker',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            // Store markers by type
            const markers = {
                fire: [],
                water: []
            };

            // Fetch incidents from API
            function loadIncidentsOnMap() {
                // Clear incident sidebar
                const incidentList = document.querySelector('.map-sidebar > div:nth-child(2)');
                if (incidentList) {
                    incidentList.innerHTML = '<div style="padding: 15px; text-align: center;">Loading incidents...</div>';
                }

                // Clear existing markers
                Object.keys(markers).forEach(type => {
                    markers[type].forEach(marker => {
                        if (map.hasLayer(marker)) {
                            map.removeLayer(marker);
                        }
                    });
                    markers[type] = [];
                });

                API.incidents.getAll({ includeLocation: true })
                    .then(response => {
                        if (!response || (!response.data && !response.incidents) || response.success === false) {
                            showNotification('Failed to load incidents', 'error');
                            return;
                        }

                        // Handle different response formats
                        let incidents = [];
                        if (response.data && Array.isArray(response.data)) {
                            incidents = response.data;
                        } else if (response.incidents && Array.isArray(response.incidents)) {
                            incidents = response.incidents;
                        } else if (Array.isArray(response)) {
                            incidents = response;
                        }

                        if (!incidents.length) {
                            if (incidentList) {
                                incidentList.innerHTML = '<div style="padding: 15px; text-align: center;">No incidents found</div>';
                            }
                            return;
                        }

                        // Clear incident list before populating
                        if (incidentList) {
                            incidentList.innerHTML = '';
                        }

                        // Process each incident
                        incidents.forEach(incident => {
                            // Determine incident type (fire vs water)
                            const incidentTypeId = incident.incidentTypeId;
                            const isFireIncident = (incidentTypeId >= 1 && incidentTypeId <= 4) || (incidentTypeId >= 11);
                            const isWaterIncident = (incidentTypeId >= 5 && incidentTypeId <= 10);
                            const incidentType = isFireIncident ? 'fire' : (isWaterIncident ? 'water' : 'other');

                            // Skip if no location data
                            if (!incident.latitude || !incident.longitude) {
                                return;
                            }

                            // Select appropriate icon
                            const icon = incidentType === 'fire' ? fireIcon : waterIcon;

                            // Create marker
                            const marker = L.marker([incident.latitude, incident.longitude], { icon: icon })
                                .bindPopup(`
                                    <div style="min-width: 200px;">
                                        <h4 style="margin: 0 0 10px 0;">${incident.title}</h4>
                                        <p style="margin: 0 0 5px 0;">${incident.address || ''}, ${incident.city || ''}</p>
                                        <div style="margin-top: 10px;">
                                            <a href="view-incident.html?id=${incident.id}" class="btn btn-sm btn-primary" style="text-decoration: none; padding: 5px 10px; background-color: #1e88e5; color: white; border-radius: 3px; display: inline-block;">View Details</a>
                                        </div>
                                    </div>
                                `);

                            marker.addTo(map);

                            // Store marker by type
                            if (!markers[incidentType]) {
                                markers[incidentType] = [];
                            }
                            markers[incidentType].push(marker);

                            // Add incident to sidebar if present
                            if (incidentList) {
                                const incidentItem = document.createElement('div');
                                incidentItem.className = 'incident-mini';
                                incidentItem.setAttribute('data-id', incident.id);
                                incidentItem.setAttribute('data-type', incidentType);
                                incidentItem.setAttribute('data-lat', incident.latitude);
                                incidentItem.setAttribute('data-lng', incident.longitude);

                                // Status color class based on incident status
                                let statusColor = '#999';
                                if (incident.status) {
                                    if (incident.status.name === 'Active') statusColor = '#66bb6a';
                                    else if (incident.status.name === 'Critical') statusColor = '#e53935';
                                    else if (incident.status.name === 'Pending') statusColor = '#ff9800';
                                }

                                incidentItem.innerHTML = `
                                    <div style="display: flex; gap: 10px;">
                                        <div class="incident-icon ${incidentType}" style="width: 30px; height: 30px; font-size: 12px; background-color: ${incidentType === 'fire' ? '#e53935' : '#1e88e5'}; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white;">
                                            <i class="fas fa-${incidentType === 'fire' ? 'fire' : 'water'}"></i>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 500; margin-bottom: 3px;">${incident.title}</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">${incident.address || ''}</div>
                                            <div style="font-size: 11px; margin-top: 3px;">
                                                <span class="status-badge" style="background-color: ${statusColor}; font-size: 10px; padding: 2px 6px;">${incident.status?.name || 'Unknown'}</span>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                // Add click event to focus map on this incident
                                incidentItem.addEventListener('click', function() {
                                    const lat = parseFloat(this.getAttribute('data-lat'));
                                    const lng = parseFloat(this.getAttribute('data-lng'));

                                    map.setView([lat, lng], 15);

                                    // Find and open the popup
                                    const id = this.getAttribute('data-id');
                                    const type = this.getAttribute('data-type');

                                    if (markers[type]) {
                                        markers[type].forEach(marker => {
                                            const popupContent = marker.getPopup().getContent();
                                            if (popupContent.includes(`id=${id}`)) {
                                                marker.openPopup();
                                            }
                                        });
                                    }

                                    // On mobile, hide the sidebar after clicking
                                    if (window.innerWidth <= 768) {
                                        document.querySelector('.map-sidebar').classList.remove('active');
                                    }
                                });

                                incidentList.appendChild(incidentItem);
                            }
                        });
                    })
                    .catch(error => {
                        console.error('Error loading incidents:', error);
                        showNotification('Error loading incident data', 'error');
                        if (incidentList) {
                            incidentList.innerHTML = '<div style="padding: 15px; text-align: center; color: #f44336;">Failed to load incidents</div>';
                        }
                    });
            }

            // Load incidents when page loads
            loadIncidentsOnMap();

            // Refresh button event handler
            const refreshButton = document.createElement('button');
            refreshButton.className = 'btn btn-icon';
            refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshButton.style.position = 'absolute';
            refreshButton.style.bottom = '20px';
            refreshButton.style.right = '20px';
            refreshButton.style.zIndex = '999';
            refreshButton.style.backgroundColor = 'var(--secondary-dark)';
            refreshButton.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
            refreshButton.title = 'Refresh incidents';
            document.querySelector('.map-container').appendChild(refreshButton);

            refreshButton.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                loadIncidentsOnMap();
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-sync-alt"></i>';
                }, 1000);
            });

            // Handle filter buttons
            document.getElementById('toggleAllIncidents').addEventListener('click', function() {
                this.classList.add('active');
                document.getElementById('toggleFireIncidents').classList.remove('active');
                document.getElementById('toggleWaterIncidents').classList.remove('active');

                // Show all markers
                markers.fire.forEach(marker => {
                    if (!map.hasLayer(marker)) {
                        map.addLayer(marker);
                    }
                });

                markers.water.forEach(marker => {
                    if (!map.hasLayer(marker)) {
                        map.addLayer(marker);
                    }
                });

                // Show all incidents in sidebar
                document.querySelectorAll('.incident-mini').forEach(item => {
                    item.style.display = 'block';
                });
            });

            document.getElementById('toggleFireIncidents').addEventListener('click', function() {
                this.classList.add('active');
                document.getElementById('toggleAllIncidents').classList.remove('active');
                document.getElementById('toggleWaterIncidents').classList.remove('active');

                // Show only fire markers
                markers.fire.forEach(marker => {
                    if (!map.hasLayer(marker)) {
                        map.addLayer(marker);
                    }
                });

                markers.water.forEach(marker => {
                    if (map.hasLayer(marker)) {
                        map.removeLayer(marker);
                    }
                });

                // Show only fire incidents in sidebar
                document.querySelectorAll('.incident-mini').forEach(item => {
                    if (item.getAttribute('data-type') === 'fire') {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            document.getElementById('toggleWaterIncidents').addEventListener('click', function() {
                this.classList.add('active');
                document.getElementById('toggleAllIncidents').classList.remove('active');
                document.getElementById('toggleFireIncidents').classList.remove('active');

                // Show only water markers
                markers.water.forEach(marker => {
                    if (!map.hasLayer(marker)) {
                        map.addLayer(marker);
                    }
                });

                markers.fire.forEach(marker => {
                    if (map.hasLayer(marker)) {
                        map.removeLayer(marker);
                    }
                });

                // Show only water incidents in sidebar
                document.querySelectorAll('.incident-mini').forEach(item => {
                    if (item.getAttribute('data-type') === 'water') {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            // Handle incident item clicks
            document.querySelectorAll('.incident-mini').forEach(item => {
                item.addEventListener('click', function() {
                    const lat = parseFloat(this.getAttribute('data-lat'));
                    const lng = parseFloat(this.getAttribute('data-lng'));

                    map.setView([lat, lng], 15);

                    // Find and open the popup
                    const id = this.getAttribute('data-id');
                    const type = this.getAttribute('data-type');

                    markers[type].forEach(marker => {
                        const popupContent = marker.getPopup().getContent();
                        if (popupContent.includes(`id=${id}`)) {
                            marker.openPopup();
                        }
                    });

                    // On mobile, hide the sidebar after clicking
                    if (window.innerWidth <= 768) {
                        document.querySelector('.map-sidebar').classList.remove('active');
                    }
                });
            });

            // Mobile toggle for filters
            document.getElementById('mobileFilterToggle').addEventListener('click', function() {
                document.querySelector('.map-sidebar').classList.toggle('active');
            });
        });
    </script>
</body>
</html>
