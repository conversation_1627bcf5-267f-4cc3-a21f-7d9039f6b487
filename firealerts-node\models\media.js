module.exports = (sequelize, DataTypes) => {
  const Media = sequelize.define('media', {
    type: {
      type: DataTypes.ENUM('image', 'video', 'document'),
      defaultValue: 'image'
    },
    fileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'file_name'
    },
    fileSize: {
      type: DataTypes.INTEGER,
      field: 'file_size'
    },
    fileType: {
      type: DataTypes.STRING(50),
      field: 'file_type'
    },
    url: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_public'
    },
    uploadedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'uploaded_at'
    }
  }, {
    tableName: 'media',
    underscored: true,
    indexes: [
      {
        fields: ['incident_id']
      },
      {
        fields: ['type']
      }
    ]
  });

  return Media;
};
