<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Add User</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span id="currentUserName">Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Add User Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-user-plus" style="margin-right: 10px;"></i>
                        Add New User
                    </div>
                    <div class="card-actions">
                        <a href="subscribers.html" class="btn btn-outline btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Subscribers
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <form id="addUserForm" action="#" method="post" data-validate="true">
                        <!-- User Information -->
                        <h3 class="section-title">User Information</h3>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">First Name *</label>
                                    <input type="text" class="form-control" name="first_name" required>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" name="last_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" name="email" required>
                                    <small style="color: var(--text-secondary);">This will be used for login and notifications.</small>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Role *</label>
                                    <select class="form-control" name="role" required>
                                        <option value="admin">Administrator</option>
                                        <option value="user" selected>Standard User</option>
                                        <option value="viewer">Read-Only User</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Subscriber Company *</label>
                                    <select class="form-control" name="subscriber_id" required id="subscriberSelect">
                                        <option value="">Select Company</option>
                                        <!-- Will be populated from API -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Job Title</label>
                                    <input type="text" class="form-control" name="job_title">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <input type="text" class="form-control" name="department">
                                </div>
                            </div>
                        </div>

                        <!-- Access and Permissions -->
                        <h3 class="section-title">Access and Permissions</h3>

                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <select class="form-control" name="status">
                                <option value="active" selected>Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="pending">Pending Activation</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Permissions</label>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="perm_view_incidents" checked>
                                    <span style="margin-left: 10px;">View Incidents</span>
                                </label>
                            </div>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="perm_manage_incidents">
                                    <span style="margin-left: 10px;">Manage Incidents</span>
                                </label>
                            </div>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="perm_view_reports" checked>
                                    <span style="margin-left: 10px;">View Reports</span>
                                </label>
                            </div>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="perm_manage_users">
                                    <span style="margin-left: 10px;">Manage Users</span>
                                </label>
                            </div>
                        </div>

                        <!-- Notification Preferences -->
                        <h3 class="section-title">Notification Preferences</h3>

                        <div class="form-group">
                            <label class="form-label">Notification Methods</label>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="notify_email" checked>
                                    <span style="margin-left: 10px;">Email Notifications</span>
                                </label>
                            </div>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="notify_sms">
                                    <span style="margin-left: 10px;">SMS Notifications</span>
                                </label>
                            </div>
                            <div style="margin-top: 10px;">
                                <label class="form-check">
                                    <input type="checkbox" name="notify_app" checked>
                                    <span style="margin-left: 10px;">Mobile App Notifications</span>
                                </label>
                            </div>
                        </div>

                        <!-- Password Options -->
                        <div class="form-group">
                            <label class="form-check">
                                <input type="checkbox" name="generate_password" checked>
                                <span style="margin-left: 10px;">Generate temporary password and send to user's email</span>
                            </label>
                        </div>

                        <div class="password-fields" style="display: none;">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Password</label>
                                        <input type="password" class="form-control" name="password">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" name="confirm_password">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                            <a href="subscribers.html" class="btn btn-outline">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle"></i> Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Use shared notification function
        function showNotification(message, type = 'info', duration = 4000) {
            if (window.FireAlertsUtils && window.FireAlertsUtils.showNotification) {
                window.FireAlertsUtils.showNotification(message, type, duration);
            } else {
                // Fallback if shared utils not loaded
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('add-user');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }
            // Load subscriber companies for dropdown
            loadSubscriberCompanies();

            // Toggle password fields based on generate password checkbox
            const genPasswordCheck = document.querySelector('input[name="generate_password"]');
            const passwordFields = document.querySelector('.password-fields');

            genPasswordCheck.addEventListener('change', function() {
                passwordFields.style.display = this.checked ? 'none' : 'block';

                // Update required attribute on password fields
                const passInputs = passwordFields.querySelectorAll('input');
                passInputs.forEach(input => {
                    input.required = !this.checked;
                });
            });

            // Form submission
            document.getElementById('addUserForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
                submitBtn.disabled = true;

                // Password validation if manual password
                if (!document.querySelector('input[name="generate_password"]').checked) {
                    const password = this.elements.password.value;
                    const confirmPass = this.elements.confirm_password.value;

                    if (password !== confirmPass) {
                        showNotification('Passwords do not match!', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        return;
                    }

                    if (password.length < 8) {
                        showNotification('Password must be at least 8 characters long!', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        return;
                    }
                }

                // Collect form data
                const formData = new FormData(this);
                const userData = {
                    firstName: formData.get('first_name'),
                    lastName: formData.get('last_name'),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    subscriberId: formData.get('subscriber_id'),
                    role: formData.get('role'),
                    status: formData.get('status'),
                    jobTitle: formData.get('job_title'),
                    department: formData.get('department'),
                    generatePassword: formData.has('generate_password'),
                    permissions: {
                        viewIncidents: formData.has('perm_view_incidents'),
                        manageIncidents: formData.has('perm_manage_incidents'),
                        viewReports: formData.has('perm_view_reports'),
                        manageUsers: formData.has('perm_manage_users')
                    },
                    notificationPreferences: {
                        email: formData.has('notify_email'),
                        sms: formData.has('notify_sms'),
                        app: formData.has('notify_app')
                    }
                };

                // Add password if not generated
                if (!userData.generatePassword) {
                    userData.password = formData.get('password');
                }

                // Send to API
                API.companies.addUser(userData)
                    .then(response => {
                        if (response && response.success !== false) {
                            showNotification('User created successfully!', 'success');

                            // Redirect after success
                            setTimeout(function() {
                                window.location.href = 'subscribers.html';
                            }, 1500);
                        } else {
                            showNotification('Failed to create user: ' + (response?.message || 'Unknown error'), 'error');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error creating user:', error);
                        showNotification('Error connecting to server', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });
        });

        // Function to load subscriber companies for dropdown
        function loadSubscriberCompanies() {
            const select = document.getElementById('subscriberSelect');

            // Show loading option
            select.innerHTML = '<option value="">Loading companies...</option>';

            // Get companies from API
            API.companies.getAll()
                .then(response => {
                    // Clear loading option
                    select.innerHTML = '<option value="">Select Company</option>';

                    if (response && response.success !== false) {
                        const companies = response.data || response;

                        // Add companies to dropdown
                        companies.forEach(company => {
                            const option = document.createElement('option');
                            option.value = company.id;
                            option.textContent = company.company?.name || company.name || `Company #${company.id}`;
                            select.appendChild(option);
                        });
                    } else {
                        select.innerHTML += '<option value="" disabled>Error loading companies</option>';
                        showNotification('Failed to load companies', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading subscribers:', error);
                    select.innerHTML = '<option value="">Select Company</option><option value="" disabled>Error loading companies</option>';
                    showNotification('Error connecting to server', 'error');
                });
        }
    </script>
</body>
</html>