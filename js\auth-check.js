/**
 * FireAlerts911 - Authentication Check Script
 * This script provides client-side UX enhancements for authentication.
 * It works alongside the secure server-side auth system.
 */

(function() {
    // Immediately execute to provide better UX for protected pages

    // List of pages that don't require authentication
    const publicPages = [
        'login.html',
        'forgot-password.html',
        'signup.html'
    ];

    // Function to check if user likely has authentication
    // Note: This is only a UX enhancement - real auth is checked server-side
    function isAuthenticated() {
        // Check local/session storage for token (for API calls)
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');

        // We can't directly check HTTP-only cookies, but we can check if we've recorded login state
        const hasLoginState = localStorage.getItem('isLoggedIn') === 'true';

        // Check if token is expired (if it exists and has expiration info)
        if (token) {
            try {
                // Basic JWT token expiration check (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                if (payload.exp && payload.exp * 1000 < Date.now()) {
                    // Token is expired, clear it
                    localStorage.removeItem('token');
                    sessionStorage.removeItem('token');
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('userInfo');
                    return false;
                }
            } catch (e) {
                // Invalid token format, clear it
                localStorage.removeItem('token');
                sessionStorage.removeItem('token');
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userInfo');
                return false;
            }
        }

        return !!token || hasLoginState;
    }

    // Function to check if auth cookie might exist on server
    async function checkAuthCookie() {
        try {
            const response = await fetch(`${getApiBaseUrl()}/auth/check-cookie`, {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                return data.hasCookie;
            }
        } catch (error) {
            console.error('Error checking auth cookie:', error);
        }
        return false;
    }

    // Get current page name from URL
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';

    // Check if current page is public
    const isPublicPage = publicPages.some(page => currentPage.toLowerCase() === page.toLowerCase());

    // Enhanced authentication check for protected pages
    if (!isPublicPage && !isAuthenticated()) {
        console.log('Auth check: No local authentication detected - checking server for auth cookie');

        // Show a loading indicator while checking authentication
        document.body.style.opacity = '0.5';

        // Instead of immediately redirecting, check if server has auth cookie
        checkAuthCookie().then(hasCookie => {
            if (hasCookie) {
                console.log('Auth check: Server has auth cookie - attempting validation');
                // Server has auth cookie, try to validate it
                return validateAuthWithServer(true); // Pass true to indicate this is from cookie check
            } else {
                console.log('Auth check: No server auth cookie - redirecting to login');
                // No auth cookie on server either, redirect to login
                const currentUrl = window.location.href;
                sessionStorage.setItem('redirectAfterLogin', currentUrl);
                window.location.replace('login.html?reason=auth_required');
            }
        }).catch(error => {
            console.error('Auth check: Error checking cookie, redirecting to login:', error);
            // If we can't check the cookie, redirect to login as fallback
            const currentUrl = window.location.href;
            sessionStorage.setItem('redirectAfterLogin', currentUrl);
            window.location.replace('login.html?reason=auth_required');
        }).finally(() => {
            // Remove loading indicator
            document.body.style.opacity = '1';
        });

        return; // Stop execution for now, let the async check handle it
    }

    // Get API base URL (same logic as api.js)
    function getApiBaseUrl() {
        if (window.location.protocol === 'file:') {
            return 'http://localhost:5000/api';
        }
        if (window.location.hostname === 'localhost' && (window.location.port === '3000' || window.location.port === '80' || window.location.port === '')) {
            return 'http://localhost:5000/api';
        }
        return '/api';
    }

    // Validate authentication with server
    function validateAuthWithServer(fromCookieCheck = false) {
        // If called from cookie check, we don't require local auth state
        if (!fromCookieCheck && !isAuthenticated()) return Promise.resolve();

        return fetch(`${getApiBaseUrl()}/auth/validate`, {
            method: 'GET',
            credentials: 'include', // Important for cookies
            headers: {
                'Content-Type': 'application/json',
                // Include token in header for API compatibility
                'Authorization': `Bearer ${localStorage.getItem('token') || sessionStorage.getItem('token') || ''}`
            }
        })
        .then(response => {
            if (response.status === 401) {
                // Authentication invalid
                if (fromCookieCheck) {
                    // If this was from cookie check, redirect to login
                    const currentUrl = window.location.href;
                    sessionStorage.setItem('redirectAfterLogin', currentUrl);
                    window.location.replace('login.html?reason=session_expired');
                } else {
                    handleLogout();
                }
                return null;
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.valid) {
                if (fromCookieCheck) {
                    // If this was from cookie check, redirect to login
                    const currentUrl = window.location.href;
                    sessionStorage.setItem('redirectAfterLogin', currentUrl);
                    window.location.replace('login.html?reason=session_expired');
                } else {
                    handleLogout();
                }
                return;
            }

            // If this validation came from a cookie check, set the login state
            if (fromCookieCheck) {
                localStorage.setItem('isLoggedIn', 'true');
                console.log('Auth check: Server validation successful - user is authenticated');
            }

            // Store user info including role for use in UI components
            if (data.user) {
                const userInfo = {
                    id: data.user.id,
                    username: data.user.username,
                    firstName: data.user.firstName,
                    lastName: data.user.lastName,
                    role: data.user.role
                };

                // Store user info in localStorage for persistence
                localStorage.setItem('userInfo', JSON.stringify(userInfo));

                // Update sidebar based on role using shared utility only
                if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                    // Determine current page for active highlighting
                    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
                    const pageId = currentPage.replace('.html', '');
                    window.FireAlertsUtils.renderRoleBasedSidebar(pageId);
                } else {
                    console.warn('Shared navigation utilities not available - navigation may not render properly');
                }
            }
        })
        .catch(error => {
            console.error('Auth validation error:', error);
            // If validation fails due to network/server issues, redirect to login
            if (error.message.includes('Failed to fetch') || error.message.includes('ERR_EMPTY_RESPONSE')) {
                console.log('Server authentication validation failed - redirecting to login');
                if (fromCookieCheck) {
                    const currentUrl = window.location.href;
                    sessionStorage.setItem('redirectAfterLogin', currentUrl);
                    window.location.replace('login.html?reason=auth_required');
                } else {
                    handleLogout();
                }
            }
        });
    }

    // If we appear to be authenticated, validate with server
    if (!isPublicPage && isAuthenticated()) {
        validateAuthWithServer();

        // Set up periodic validation (every 5 minutes)
        setInterval(() => {
            if (isAuthenticated()) {
                validateAuthWithServer();
            }
        }, 5 * 60 * 1000); // 5 minutes
    }

    // Add visibility change listener to validate auth when user returns to tab
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden && !isPublicPage && isAuthenticated()) {
            // User returned to tab, validate authentication
            validateAuthWithServer();
        }
    });

    // Set up logout button handlers when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Find all logout links/buttons and add proper logout handling
        const logoutElements = document.querySelectorAll('a[href="login.html"], a[href*="logout"], button[data-action="logout"]');

        logoutElements.forEach(element => {
            // Only handle elements that look like logout buttons
            const text = element.textContent.toLowerCase();
            const href = element.getAttribute('href');

            if (text.includes('logout') || text.includes('sign out') || href === 'login.html') {
                element.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show confirmation for explicit logout actions
                    if (text.includes('logout') || text.includes('sign out')) {
                        if (confirm('Are you sure you want to logout?')) {
                            window.AuthCheck.logout();
                        }
                    } else {
                        // Direct navigation to login (like clicking login.html link)
                        window.AuthCheck.logout();
                    }
                });
            }
        });
    });

    // Handle logout
    function handleLogout() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('userInfo');

        if (!isPublicPage) {
            window.location.href = 'login.html?reason=session_expired';
        }
    }

    // Export helper functions for use in other scripts
    window.AuthCheck = {
        isAuthenticated: isAuthenticated,
        validateAuthWithServer: validateAuthWithServer,
        redirectToLogin: function(reason) {
            const currentUrl = window.location.href;
            sessionStorage.setItem('redirectAfterLogin', currentUrl);
            window.location.href = `login.html?reason=${reason || 'auth_required'}`;
        },
        logout: function() {
            // Call the server logout endpoint to clear the auth cookie
            fetch(`${getApiBaseUrl()}/auth/logout`, {
                method: 'POST',
                credentials: 'include'
            })
            .then(() => {
                handleLogout();
            })
            .catch(error => {
                console.error('Logout error:', error);
                // Still clear local data even if server request fails
                handleLogout();
            });
        },
        getUserRole: function() {
            try {
                const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
                return userInfo.role || '';
            } catch (e) {
                console.error('Error getting user role:', e);
                return '';
            }
        }
    };
})();