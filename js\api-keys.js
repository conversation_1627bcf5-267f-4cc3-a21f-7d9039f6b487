/**
 * API keys management utility
 * This module provides utilities for managing API keys across the FireAlerts911 application
 */

// API Keys management - enhanced security with server-side storage only
(function() {
    // Create a global apiKeys object with enhanced security
    window.apiKeys = {
        // Cache for API keys to avoid repeated server calls
        _cache: {},
        _cacheExpiry: {},

        // Get an API key for a specific service (server-side only)
        getKey: async function(service) {
            // Check cache first (5 minute expiry)
            const cacheKey = service;
            const now = Date.now();

            if (this._cache[cacheKey] && this._cacheExpiry[cacheKey] > now) {
                return this._cache[cacheKey];
            }

            try {
                // Map service names to server-side key names
                const serviceMapping = {
                    'google_maps': 'google_maps_api_key',
                    'twilio_account_sid': 'twilio_account_sid',
                    'twilio_auth_token': 'twilio_auth_token',
                    'twilio_from_number': 'twilio_from_number',
                    'mailgun': 'mailgun_api_key',
                    'mailgun_domain': 'mailgun_domain',
                    'estated': 'estated_api_key',
                    'openweather': 'openweather_api_key'
                };

                const serverKeyName = serviceMapping[service] || service;

                // Get key from server (this will track access automatically)
                // Use the same authentication method as the main API
                const headers = {
                    'Content-Type': 'application/json'
                };

                // Add Bearer token if available (secondary auth method)
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`/api/settings/api-keys/${serverKeyName}`, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Include HTTP-only cookies for primary auth
                });

                if (response.ok) {
                    const keyData = await response.json();

                    // Cache the result for 5 minutes
                    this._cache[cacheKey] = keyData.value;
                    this._cacheExpiry[cacheKey] = now + (5 * 60 * 1000);

                    return keyData.value;
                } else {
                    console.warn(`Failed to retrieve API key for ${service}`);
                    return null;
                }
            } catch (error) {
                console.error(`Error retrieving API key for ${service}:`, error);
                return null;
            }
        },

        // Clear cache (useful for testing or when keys are updated)
        clearCache: function() {
            this._cache = {};
            this._cacheExpiry = {};
            console.log('API key cache cleared');
        },

        // Check if a service has an API key configured
        hasKey: async function(service) {
            const key = await this.getKey(service);
            return key !== null && key !== undefined && key.length > 0;
        },

        // Get multiple keys at once (optimized batch operation)
        getKeys: async function(services) {
            const keys = {};
            const uncachedServices = [];
            const now = Date.now();

            // Check cache first for all services
            for (const service of services) {
                const cacheKey = service;
                if (this._cache[cacheKey] && this._cacheExpiry[cacheKey] > now) {
                    keys[service] = this._cache[cacheKey];
                } else {
                    uncachedServices.push(service);
                }
            }

            // Fetch uncached keys
            for (const service of uncachedServices) {
                try {
                    keys[service] = await this.getKey(service);
                } catch (error) {
                    console.error(`Error fetching key for ${service}:`, error);
                    keys[service] = null;
                }
            }

            return keys;
        },

        // Preload commonly used keys into cache
        preloadKeys: async function() {
            const commonServices = [
                'google_maps',
                'twilio_account_sid',
                'twilio_auth_token',
                'twilio_from_number',
                'mailgun',
                'mailgun_domain',
                'estated',
                'openweather'
            ];

            try {
                console.log('Preloading API keys into cache...');
                const startTime = Date.now();

                await this.getKeys(commonServices);

                const duration = Date.now() - startTime;
                console.log(`API keys preloaded into cache in ${duration}ms`);

                // Log cache status
                const cacheStatus = commonServices.map(service => ({
                    service,
                    cached: !!this._cache[service],
                    expires: this._cacheExpiry[service] ? new Date(this._cacheExpiry[service]).toISOString() : 'N/A'
                }));

                console.log('Cache status:', cacheStatus);
                return true;
            } catch (error) {
                console.error('Error preloading API keys:', error);
                return false;
            }
        },

        // Get cache statistics
        getCacheStats: function() {
            const now = Date.now();
            const stats = {
                totalCached: Object.keys(this._cache).length,
                validCached: 0,
                expiredCached: 0,
                cacheHitRate: 0,
                entries: []
            };

            for (const [key, value] of Object.entries(this._cache)) {
                const isValid = this._cacheExpiry[key] && this._cacheExpiry[key] > now;
                if (isValid) {
                    stats.validCached++;
                } else {
                    stats.expiredCached++;
                }

                stats.entries.push({
                    key,
                    hasValue: !!value,
                    expires: this._cacheExpiry[key] ? new Date(this._cacheExpiry[key]).toISOString() : 'N/A',
                    isValid
                });
            }

            return stats;
        },

        // Clean expired entries from cache
        cleanExpiredCache: function() {
            const now = Date.now();
            let cleanedCount = 0;

            for (const [key, expiry] of Object.entries(this._cacheExpiry)) {
                if (expiry <= now) {
                    delete this._cache[key];
                    delete this._cacheExpiry[key];
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                console.log(`Cleaned ${cleanedCount} expired cache entries`);
            }

            return cleanedCount;
        },

        // Initialize the API keys system
        initialize: async function() {
            console.log('Initializing API keys system...');

            // Clean any expired cache entries
            this.cleanExpiredCache();

            // Preload common keys
            await this.preloadKeys();

            // Set up periodic cache cleanup (every 10 minutes)
            if (!this._cleanupInterval) {
                this._cleanupInterval = setInterval(() => {
                    this.cleanExpiredCache();
                }, 10 * 60 * 1000);
            }

            console.log('API keys system initialized');
            return true;
        },

        // Load Google Maps API with the stored key or a default key
        loadGoogleMapsApi: async function(callback, libraries = ['places']) {
            try {
                // Remove any existing Google Maps script tags
                const existingScripts = document.querySelectorAll('script[src*="maps.googleapis.com"]');
                existingScripts.forEach(script => script.remove());

                // Reset Google Maps API-related objects
                window.google = undefined;

                // Get API key from storage (async)
                const apiKey = await this.getKey('google_maps');

                if (!apiKey) {
                    console.error('Google Maps API key not configured');
                    throw new Error('Google Maps API key not configured');
                }

                // Create new script element
                const script = document.createElement('script');
                const librariesParam = libraries.join(',');

                script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=${librariesParam}&callback=${callback}&loading=async`;
                script.async = true;
                script.defer = true;

                // Add error handling
                script.onerror = function() {
                    console.error('Failed to load Google Maps API - check API key configuration');
                };

                // Add script to document
                document.head.appendChild(script);

                console.log(`Loading Google Maps API with ${libraries.length} libraries...`);

                // Return the API key that was used
                return apiKey;
            } catch (error) {
                console.error('Error loading Google Maps API:', error);
                throw error;
            }
        },

        // Update Google Maps API key dynamically (useful when key changes in admin panel)
        updateGoogleMapsApiKey: function(newKey) {
            // Clear cache to force reload with new key
            if (this._cache['google_maps']) {
                delete this._cache['google_maps'];
                delete this._cacheExpiry['google_maps'];
                console.log('Google Maps API key cache cleared for update');
            }

            // Store the new key in cache
            this._cache['google_maps'] = newKey;
            this._cacheExpiry['google_maps'] = Date.now() + (5 * 60 * 1000); // 5 minutes

            // Get all callbacks that might be registered
            const callbacks = [];
            if (window.initAutocomplete) callbacks.push('initAutocomplete');
            if (window.initMap) callbacks.push('initMap');

            // If no callbacks found, don't reload
            if (callbacks.length === 0) return false;

            // Reload the first callback (typically the main one)
            this.loadGoogleMapsApi(callbacks[0]);
            return true;
        }
    };

    // Make updateGoogleMapsApiKey globally available (needed by the admin panel)
    window.updateGoogleMapsApiKey = window.apiKeys.updateGoogleMapsApiKey.bind(window.apiKeys);

    console.log('API Keys utility initialized');
})();