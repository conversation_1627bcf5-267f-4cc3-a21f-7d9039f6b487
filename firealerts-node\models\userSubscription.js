module.exports = (sequelize, DataTypes) => {
  const UserSubscription = sequelize.define('userSubscription', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      primaryKey: true
    },
    countyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'county_id',
      primaryKey: true
    }
  }, {
    tableName: 'user_subscriptions',
    underscored: true,
    timestamps: true,
    updatedAt: false, // Only track creation
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'county_id']
      }
    ]
  });

  return UserSubscription;
};
