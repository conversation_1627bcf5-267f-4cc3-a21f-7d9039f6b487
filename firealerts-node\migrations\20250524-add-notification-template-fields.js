'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add notification_type column
    await queryInterface.addColumn('notifications', 'notification_type', {
      type: Sequelize.ENUM('new', 'update'),
      defaultValue: 'new',
      allowNull: false
    });

    // Add template_type column
    await queryInterface.addColumn('notifications', 'template_type', {
      type: Sequelize.STRING,
      defaultValue: 'incident',
      allowNull: false
    });

    // Add indexes for the new columns
    await queryInterface.addIndex('notifications', ['notification_type']);
    await queryInterface.addIndex('notifications', ['template_type']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes
    await queryInterface.removeIndex('notifications', ['template_type']);
    await queryInterface.removeIndex('notifications', ['notification_type']);
    
    // Remove columns
    await queryInterface.removeColumn('notifications', 'template_type');
    await queryInterface.removeColumn('notifications', 'notification_type');
  }
};
