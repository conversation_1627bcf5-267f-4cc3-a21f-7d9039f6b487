/**
 * Security Schema Verification Script
 * 
 * Verifies that the enhanced security schema has been properly
 * implemented in the Railway database.
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function verifySecuritySchema() {
  console.log('🔍 ENHANCED SECURITY SCHEMA VERIFICATION');
  console.log('========================================');
  
  const config = {
    host: process.env.DB_HOST || 'crossover.proxy.rlwy.net',
    port: parseInt(process.env.DB_PORT) || 54883,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'railway',
    ssl: false
  };

  console.log('📋 Connection Configuration:');
  console.log(`   Host: ${config.host}`);
  console.log(`   Port: ${config.port}`);
  console.log(`   Database: ${config.database}`);
  console.log('');

  let connection;
  
  try {
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(config);
    console.log('✅ Connected successfully!');
    console.log('');

    // Test 1: Check system_settings table structure
    console.log('🔍 CHECKING SYSTEM_SETTINGS TABLE STRUCTURE');
    console.log('===========================================');
    
    const [columns] = await connection.execute('DESCRIBE system_settings');
    console.log('📋 Table structure:');
    columns.forEach(col => {
      console.log(`   - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? col.Key : ''}`);
    });
    console.log('');

    // Test 2: Verify security fields
    console.log('🔒 SECURITY FIELDS VERIFICATION');
    console.log('===============================');
    
    const securityFields = ['expires_at', 'last_accessed_at', 'access_count'];
    const existingFields = columns.map(col => col.Field);
    
    let securityFieldsPresent = 0;
    securityFields.forEach(field => {
      const exists = existingFields.includes(field);
      console.log(`   ${exists ? '✅' : '❌'} ${field}: ${exists ? 'Present' : 'Missing'}`);
      if (exists) securityFieldsPresent++;
    });
    
    const securityFieldsComplete = securityFieldsPresent === securityFields.length;
    console.log('');
    console.log(`📊 Security Fields Status: ${securityFieldsPresent}/${securityFields.length} present`);
    console.log(`🔒 Enhanced Security Schema: ${securityFieldsComplete ? '✅ COMPLETE' : '❌ INCOMPLETE'}`);
    console.log('');

    // Test 3: Check existing system settings
    console.log('📊 SYSTEM SETTINGS DATA VERIFICATION');
    console.log('====================================');
    
    const [settings] = await connection.execute('SELECT `key`, category, is_secret, expires_at, last_accessed_at, access_count FROM system_settings');
    console.log(`📋 Found ${settings.length} system settings:`);
    
    settings.forEach(setting => {
      const isSecret = setting.is_secret ? '🔒' : '🔓';
      const hasExpiration = setting.expires_at ? '⏰' : '♾️';
      const accessCount = setting.access_count || 0;
      console.log(`   ${isSecret} ${setting.key} (${setting.category}) - Access: ${accessCount} ${hasExpiration}`);
    });
    console.log('');

    // Test 4: Test encryption functionality (if we can access the model)
    console.log('🔐 ENCRYPTION FUNCTIONALITY TEST');
    console.log('================================');
    
    try {
      // Try to use the SystemSetting model
      const db = require('../models');
      
      // Test encryption/decryption
      const testValue = 'test-api-key-12345';
      const encrypted = db.systemSetting.encrypt(testValue);
      const decrypted = db.systemSetting.decrypt(encrypted);
      
      const encryptionWorks = decrypted === testValue;
      console.log(`🔐 Encryption Test: ${encryptionWorks ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`   Original: ${testValue}`);
      console.log(`   Encrypted: ${encrypted.substring(0, 50)}...`);
      console.log(`   Decrypted: ${decrypted}`);
      console.log(`   Match: ${encryptionWorks ? 'Yes' : 'No'}`);
      
    } catch (error) {
      console.log('⚠️ Could not test encryption (model not available)');
      console.log(`   Error: ${error.message}`);
    }
    console.log('');

    // Test 5: Verify table indexes
    console.log('📈 DATABASE INDEXES VERIFICATION');
    console.log('================================');
    
    const [indexes] = await connection.execute('SHOW INDEX FROM system_settings');
    console.log('📋 Table indexes:');
    indexes.forEach(index => {
      console.log(`   - ${index.Key_name} on ${index.Column_name} (${index.Index_type})`);
    });
    console.log('');

    // Summary
    console.log('📋 VERIFICATION SUMMARY');
    console.log('======================');
    console.log(`✅ Database Connection: Working`);
    console.log(`${securityFieldsComplete ? '✅' : '❌'} Security Schema: ${securityFieldsComplete ? 'Complete' : 'Incomplete'}`);
    console.log(`✅ System Settings: ${settings.length} records found`);
    console.log(`✅ Table Structure: Verified`);
    console.log('');
    
    if (securityFieldsComplete) {
      console.log('🎉 ENHANCED SECURITY SCHEMA VERIFICATION SUCCESSFUL!');
      console.log('');
      console.log('🚀 Ready for:');
      console.log('   1. End-to-end integration testing');
      console.log('   2. API endpoint testing');
      console.log('   3. Performance testing');
      console.log('   4. Production deployment');
    } else {
      console.log('⚠️ Security schema incomplete. Please check the seeding process.');
    }

  } catch (error) {
    console.error('❌ VERIFICATION FAILED');
    console.error('======================');
    console.error(`Error: ${error.message}`);
    console.error(`Code: ${error.code || 'Unknown'}`);
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the verification
if (require.main === module) {
  verifySecuritySchema().catch(err => {
    console.error('❌ Unhandled error:', err);
    process.exit(1);
  });
}

module.exports = verifySecuritySchema;
