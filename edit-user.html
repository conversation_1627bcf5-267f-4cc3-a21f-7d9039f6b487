<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Edit User</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Standardized profile picture using Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Admin User</span>
                    </div>
                    <a href="login.html" class="btn-icon" data-tooltip="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Edit User Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-user-edit" style="margin-right: 10px;"></i>
                        Edit User: <span id="userFullName">User Name</span>
                    </div>
                    <div class="card-actions">
                        <a href="admin-panel.html" class="btn btn-outline btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Admin Panel
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Tabs for different sections -->
                    <div class="tabs">
                        <div class="tab-list">
                            <button class="tab-button active" data-tab="profile">Profile</button>
                            <button class="tab-button" data-tab="permissions">Permissions</button>
                            <button class="tab-button" data-tab="security">Security</button>
                            <button class="tab-button" data-tab="activity">Activity Log</button>
                        </div>

                        <!-- Profile Tab -->
                        <div class="tab-content active" id="profile">
                            <form id="profileForm" action="#" method="post">
                                <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 20px;">
                                    <div style="position: relative; margin-bottom: 15px;">
                                        <img src="https://via.placeholder.com/150" alt="User Avatar" id="avatarPreview" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover;">
                                        <button type="button" class="btn btn-primary btn-sm" style="position: absolute; bottom: 0; right: 0; border-radius: 50%; width: 36px; height: 36px; padding: 0; display: flex; justify-content: center; align-items: center;" id="changeAvatarBtn">
                                            <i class="fas fa-camera"></i>
                                        </button>
                                        <input type="file" id="avatarInput" style="display: none;" accept="image/*">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">First Name *</label>
                                            <input type="text" class="form-control" name="first_name" id="firstName" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" name="last_name" id="lastName" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" name="email" id="email" required>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="phone" id="phone">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Role *</label>
                                            <select class="form-control" name="role" id="role" required>
                                                <option value="admin">Administrator</option>
                                                <option value="super_admin">Super Administrator</option>
                                                <option value="editor">Editor</option>
                                                <option value="viewer">Viewer</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">Status</label>
                                            <select class="form-control" name="status" id="status">
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                                <option value="suspended">Suspended</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" name="notes" id="notes" rows="3"></textarea>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="submit" class="btn btn-primary" id="saveProfileBtn">
                                        <i class="fas fa-save"></i> Save Profile
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Permissions Tab -->
                        <div class="tab-content" id="permissions">
                            <form id="permissionsForm" action="#" method="post">
                                <div class="permission-section">
                                    <h4>Incident Management</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_view_incidents" checked>
                                            <span style="margin-left: 10px;">View Incidents</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_create_incidents" checked>
                                            <span style="margin-left: 10px;">Create Incidents</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_edit_incidents" checked>
                                            <span style="margin-left: 10px;">Edit Incidents</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_delete_incidents">
                                            <span style="margin-left: 10px;">Delete Incidents</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_approve_incidents">
                                            <span style="margin-left: 10px;">Approve Incidents</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_assign_incidents" checked>
                                            <span style="margin-left: 10px;">Assign Incidents</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="permission-section">
                                    <h4>Subscriber Management</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_view_subscribers" checked>
                                            <span style="margin-left: 10px;">View Subscribers</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_create_subscribers" checked>
                                            <span style="margin-left: 10px;">Add Subscribers</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_edit_subscribers" checked>
                                            <span style="margin-left: 10px;">Edit Subscribers</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_delete_subscribers">
                                            <span style="margin-left: 10px;">Remove Subscribers</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="permission-section">
                                    <h4>User Management</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_view_users" checked>
                                            <span style="margin-left: 10px;">View Users</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_create_users">
                                            <span style="margin-left: 10px;">Add Users</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_edit_users">
                                            <span style="margin-left: 10px;">Edit Users</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_delete_users">
                                            <span style="margin-left: 10px;">Remove Users</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="permission-section">
                                    <h4>System Administration</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_view_settings">
                                            <span style="margin-left: 10px;">View Settings</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_edit_settings">
                                            <span style="margin-left: 10px;">Edit Settings</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_manage_api_keys">
                                            <span style="margin-left: 10px;">Manage API Keys</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_view_logs">
                                            <span style="margin-left: 10px;">View System Logs</span>
                                        </label>
                                        <label class="form-check">
                                            <input type="checkbox" name="perm_manage_backups">
                                            <span style="margin-left: 10px;">Manage Backups</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-check">
                                        <input type="checkbox" name="perm_super_admin" id="superAdminCheck">
                                        <span style="margin-left: 10px;"><strong>Super Administrator</strong> (Grants all permissions)</span>
                                    </label>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="submit" class="btn btn-primary" id="savePermissionsBtn">
                                        <i class="fas fa-save"></i> Save Permissions
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-content" id="security">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <h3 class="section-title">Password Management</h3>
                                        <div class="btn-group" style="display: flex; gap: 10px;">
                                            <button type="button" class="btn btn-outline" id="resetPasswordBtn">
                                                <i class="fas fa-key"></i> Reset Password
                                            </button>
                                            <button type="button" class="btn btn-outline" id="forcePasswordChangeBtn">
                                                <i class="fas fa-lock"></i> Force Password Change
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <h3 class="section-title">Two-Factor Authentication</h3>
                                        <div class="form-check">
                                            <input type="checkbox" id="require2fa" name="require_2fa">
                                            <span style="margin-left: 10px;">Require Two-Factor Authentication</span>
                                        </div>
                                        <div id="2faStatus" style="margin-top: 10px;">
                                            <span class="status-badge status-pending">Not Configured</span>
                                        </div>
                                        <div style="margin-top: 10px;">
                                            <button type="button" class="btn btn-outline btn-sm" id="reset2faBtn" disabled>
                                                <i class="fas fa-redo"></i> Reset 2FA
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h3 class="section-title">Access Control</h3>
                            <form id="accessControlForm">
                                <div class="form-group">
                                    <label class="form-label">Account Expiry Date</label>
                                    <input type="date" class="form-control" name="expiry_date" id="expiryDate">
                                    <small style="color: var(--text-secondary);">Leave empty for no expiration</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">IP Restrictions</label>
                                    <textarea class="form-control" name="ip_restrictions" id="ipRestrictions" rows="3" placeholder="Enter allowed IP addresses, one per line"></textarea>
                                    <small style="color: var(--text-secondary);">Leave empty to allow access from any IP</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-check">
                                        <input type="checkbox" name="force_password_expiry" id="forcePasswordExpiry">
                                        <span style="margin-left: 10px;">Apply password expiration policy</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="form-check">
                                        <input type="checkbox" name="account_locked" id="accountLocked">
                                        <span style="margin-left: 10px;">Lock Account</span>
                                    </label>
                                    <small style="color: var(--text-secondary); margin-left: 28px; display: block;">Prevent user from logging in until unlocked</small>
                                </div>

                                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                                    <button type="submit" class="btn btn-primary" id="saveSecurityBtn">
                                        <i class="fas fa-save"></i> Save Security Settings
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Activity Log Tab -->
                        <div class="tab-content" id="activity">
                            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
                                <div style="flex: 1; min-width: 200px;">
                                    <input type="text" class="form-control" placeholder="Search activity..." id="searchActivity">
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <select class="form-control" id="activityFilter">
                                        <option value="all">All Activity Types</option>
                                        <option value="login">Logins</option>
                                        <option value="edit">Edit Actions</option>
                                        <option value="create">Create Actions</option>
                                        <option value="delete">Delete Actions</option>
                                    </select>
                                    <button class="btn btn-primary" id="filterActivityBtn">Filter</button>
                                </div>
                            </div>

                            <div class="table-wrapper">
                                <table class="data-table" id="activityTable">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>Activity</th>
                                            <th>IP Address</th>
                                            <th>Browser/Device</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="4" style="text-align: center;">Loading activity...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div id="activityPagination" class="pagination" style="margin-top: 20px; display: flex; justify-content: center;">
                                <!-- Pagination will be inserted here via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Modal -->
    <div class="modal" id="actionsModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">User Actions</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="action-buttons">
                        <button type="button" class="btn btn-outline" id="modalResetPasswordBtn">
                            <i class="fas fa-key"></i> Reset Password
                        </button>
                        <button type="button" class="btn btn-outline" id="modalForce2faBtn">
                            <i class="fas fa-shield-alt"></i> Reset 2FA
                        </button>
                        <button type="button" class="btn btn-outline btn-danger" id="modalLockAccountBtn">
                            <i class="fas fa-lock"></i> Lock Account
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline close-modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Backdrop -->
    <div class="modal-backdrop" id="modalBackdrop" style="display: none;"></div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Use shared sidebar rendering utility only
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('admin-panel');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }
        });
    </script>
    <style>
        /* Tabs styling */
        .tabs {
            margin-bottom: 20px;
        }

        .tab-list {
            display: flex;
            overflow-x: auto;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            position: relative;
        }

        .tab-button:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: var(--text-light);
        }

        .tab-button.active:after {
            background-color: var(--accent-blue);
        }

        .tab-button:hover {
            color: var(--text-light);
        }

        .tab-button:hover:after {
            background-color: var(--accent-blue);
            opacity: 0.5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .permission-section {
            background-color: rgba(30, 136, 229, 0.1);
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .permission-section h4 {
            margin-top: 0;
            color: var(--text-light);
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1050;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-dialog {
            width: 100%;
            max-width: 500px;
            margin: 20px;
            position: relative;
            z-index: 1051;
        }

        .modal-content {
            background-color: var(--secondary-dark);
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
        }

        .modal-body {
            padding: 20px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .action-buttons button {
            text-align: left;
            padding: 10px 15px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close-modal {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 0;
            font-size: 16px;
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1049;
        }
    </style>
</body>
</html>