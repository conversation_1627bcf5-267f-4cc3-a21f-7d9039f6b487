/**
 * Gmail-Compatible Enhanced Email Template for FireAlerts911
 *
 * This template uses table-based layouts and inline styles to ensure
 * proper rendering across all email clients, especially Gmail.
 */

function generateGmailCompatibleIncidentUpdate(user, incident, notification) {
    const incidentType = incident?.incidentType?.name || 'Incident';
    const location = `${incident.address}, ${incident.city}, ${incident.county}, ${incident.state}`;
    const userName = user.firstName || user.email;
    const frontendUrl = process.env.FRONTEND_URL || 'https://firealerts911.com';

    // Dynamic styling based on incident type
    const isFireIncident = incidentType.toLowerCase().includes('fire');
    const isWaterIncident = incidentType.toLowerCase().includes('water');

    let primaryColor = '#e53935'; // Fire red
    let secondaryColor = '#d32f2f';
    let emoji = '🚨';
    let gradientStart = '#e53935';
    let gradientEnd = '#d32f2f';

    if (isWaterIncident) {
        primaryColor = '#1e88e5'; // Water blue
        secondaryColor = '#1976d2';
        emoji = '💧';
        gradientStart = '#1e88e5';
        gradientEnd = '#1976d2';
    } else if (isFireIncident) {
        emoji = '🔥';
    }

    return {
        subject: `${emoji} FireAlerts911: ${incidentType} Update - ${incident.address}`,
        text: generateTextContent(user, incident, notification),
        html: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - ${incidentType} Update</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style type="text/css">
        /* Reset styles for email clients */
        * { margin: 0 !important; padding: 0 !important; }
        body, table, td, p, a, li, blockquote { -webkit-text-size-adjust: 100% !important; -ms-text-size-adjust: 100% !important; }
        table, td { mso-table-lspace: 0pt !important; mso-table-rspace: 0pt !important; border-collapse: collapse !important; }
        img { -ms-interpolation-mode: bicubic !important; border: 0 !important; outline: none !important; text-decoration: none !important; }
        #outlook a { padding: 0 !important; }
        .ReadMsgBody { width: 100% !important; }
        .ExternalClass { width: 100% !important; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div { line-height: 100% !important; }

        /* Mobile responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container { width: 100% !important; }
            .mobile-padding { padding: 15px !important; }
            .mobile-text { font-size: 14px !important; }
            .mobile-title { font-size: 18px !important; }
            .info-cell { display: block !important; width: 100% !important; padding: 8px 0 !important; }
        }
    </style>
</head>
<body style="margin: 0 !important; padding: 0 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
    <!-- Outer wrapper table for Gmail background -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #1a2035 !important; min-height: 100vh !important;">
        <tr>
            <td style="background-color: #1a2035 !important; padding: 20px 10px !important;">

                <!-- Main email container -->
                <table role="presentation" class="email-container" cellspacing="0" cellpadding="0" border="0" width="600" align="center" style="max-width: 600px !important; margin: 0 auto !important; background-color: #272e48 !important; border-radius: 8px !important; border: 1px solid #464d63 !important; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;">

                    <!-- Header -->
                    <tr>
                        <td style="background-color: ${gradientStart} !important; background: linear-gradient(135deg, ${gradientStart} 0%, ${gradientEnd} 100%) !important; padding: 30px 25px !important; text-align: center !important; border-radius: 8px 8px 0 0 !important;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="text-align: center !important;">
                                        <!-- Logo -->
                                        <div style="font-size: 32px !important; font-weight: bold !important; color: white !important; margin-bottom: 8px !important; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            ${emoji} FireAlerts911
                                        </div>
                                        <!-- Subtitle -->
                                        <div style="font-size: 14px !important; color: rgba(255, 255, 255, 0.95) !important; text-transform: uppercase !important; letter-spacing: 1.2px !important; margin-bottom: 15px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            Emergency Notification System
                                        </div>
                                        <!-- Update badge -->
                                        <div style="display: inline-block !important; background-color: rgba(255, 255, 255, 0.2) !important; color: white !important; padding: 8px 16px !important; border-radius: 20px !important; font-weight: bold !important; font-size: 14px !important; border: 1px solid rgba(255, 255, 255, 0.3) !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            INCIDENT UPDATE
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="background-color: #272e48 !important; padding: 30px 25px !important;" class="mobile-padding">

                            <!-- Greeting -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="font-size: 18px !important; color: #f5f5f5 !important; margin-bottom: 20px !important; padding-bottom: 20px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                        Hello ${userName},
                                    </td>
                                </tr>
                            </table>

                            <!-- Incident Summary -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: rgba(${isFireIncident ? '229, 57, 53' : isWaterIncident ? '30, 136, 229' : '229, 57, 53'}, 0.1) !important; border: 1px solid ${primaryColor} !important; border-radius: 8px !important; margin-bottom: 25px !important;">
                                <tr>
                                    <td style="padding: 20px !important;">

                                        <!-- Incident Title -->
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="font-size: 20px !important; font-weight: bold !important; color: ${primaryColor} !important; margin-bottom: 15px !important; padding-bottom: 15px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                                    <span style="margin-right: 10px !important; font-size: 24px !important;">${emoji}</span>
                                                    ${incidentType} Update
                                                </td>
                                            </tr>
                                        </table>

                                        <!-- Description -->
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="color: #f5f5f5 !important; margin-bottom: 20px !important; padding-bottom: 20px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important; line-height: 1.6 !important;">
                                                    This is an update for the ${incidentType} at ${location}.
                                                </td>
                                            </tr>
                                        </table>

                                        <!-- Info Grid -->
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <!-- Type -->
                                                <td class="info-cell" style="width: 50% !important; padding: 12px !important; background-color: rgba(255, 255, 255, 0.05) !important; border-radius: 6px !important; border-left: 3px solid ${primaryColor} !important; margin-bottom: 10px !important; vertical-align: top !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 5px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">Type</div>
                                                    <div style="font-size: 14px !important; color: #f5f5f5 !important; font-weight: 500 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">${incidentType}</div>
                                                </td>
                                                <!-- Status -->
                                                <td class="info-cell" style="width: 50% !important; padding: 12px !important; background-color: rgba(255, 255, 255, 0.05) !important; border-radius: 6px !important; border-left: 3px solid ${primaryColor} !important; margin-bottom: 10px !important; vertical-align: top !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 5px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">Status</div>
                                                    <div style="font-size: 14px !important; color: #f5f5f5 !important; font-weight: 500 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">${incident.status?.name || 'Active'}</div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <!-- Location -->
                                                <td class="info-cell" style="width: 50% !important; padding: 12px !important; background-color: rgba(255, 255, 255, 0.05) !important; border-radius: 6px !important; border-left: 3px solid ${primaryColor} !important; margin-bottom: 10px !important; vertical-align: top !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 5px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">Location</div>
                                                    <div style="font-size: 14px !important; color: #f5f5f5 !important; font-weight: 500 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">${location}</div>
                                                </td>
                                                <!-- Severity -->
                                                <td class="info-cell" style="width: 50% !important; padding: 12px !important; background-color: rgba(255, 255, 255, 0.05) !important; border-radius: 6px !important; border-left: 3px solid ${primaryColor} !important; margin-bottom: 10px !important; vertical-align: top !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 5px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">Severity</div>
                                                    <div style="font-size: 14px !important; color: #f5f5f5 !important; font-weight: 500 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">${incident.severity || 'Moderate'}</div>
                                                </td>
                                            </tr>
                                        </table>

                                        ${incident.description ? `
                                        <!-- Description -->
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin-top: 15px !important;">
                                            <tr>
                                                <td style="background-color: rgba(255, 255, 255, 0.05) !important; padding: 15px !important; border-radius: 6px !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 5px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">Description</div>
                                                    <div style="color: #f5f5f5 !important; line-height: 1.6 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">${incident.description.replace(/\n/g, '<br>')}</div>
                                                </td>
                                            </tr>
                                        </table>
                                        ` : ''}

                                        ${incident.incidentDetail && (incident.incidentDetail.propertyOwner || incident.incidentDetail.dwellingType || incident.incidentDetail.yearBuilt || incident.incidentDetail.propertyValue) ? `
                                        <!-- Property Information -->
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin-top: 15px !important;">
                                            <tr>
                                                <td style="background-color: rgba(255, 255, 255, 0.05) !important; padding: 15px !important; border-radius: 6px !important;">
                                                    <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 10px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">🏠 Property Information</div>
                                                    ${incident.incidentDetail.propertyOwner ? `
                                                    <div style="margin-bottom: 8px !important; color: #f5f5f5 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                                        <strong style="color: #ffffff !important;">Owner:</strong> ${incident.incidentDetail.propertyOwner}
                                                    </div>
                                                    ` : ''}
                                                    ${incident.incidentDetail.dwellingType ? `
                                                    <div style="margin-bottom: 8px !important; color: #f5f5f5 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                                        <strong style="color: #ffffff !important;">Type:</strong> ${incident.incidentDetail.dwellingType}
                                                    </div>
                                                    ` : ''}
                                                    ${incident.incidentDetail.yearBuilt ? `
                                                    <div style="margin-bottom: 8px !important; color: #f5f5f5 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                                        <strong style="color: #ffffff !important;">Year Built:</strong> ${incident.incidentDetail.yearBuilt}
                                                    </div>
                                                    ` : ''}
                                                    ${incident.incidentDetail.propertyValue ? `
                                                    <div style="margin-bottom: 8px !important; color: #f5f5f5 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                                        <strong style="color: #ffffff !important;">Property Value:</strong> ${new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(incident.incidentDetail.propertyValue)}
                                                    </div>
                                                    ` : ''}
                                                </td>
                                            </tr>
                                        </table>
                                        ` : ''}

                                    </td>
                                </tr>
                            </table>

                            <!-- Action Button -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 30px 0 !important;">
                                <tr>
                                    <td style="text-align: center !important;">
                                        <!-- View Details Button -->
                                        <a href="${frontendUrl}/view-incident.html?id=${incident.id}" style="display: inline-block !important; padding: 12px 24px !important; background-color: ${primaryColor} !important; color: white !important; text-decoration: none !important; border-radius: 6px !important; font-weight: 500 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            View Full Details
                                        </a>
                                    </td>
                                </tr>
                            </table>

                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #1a2035 !important; padding: 20px 25px !important; text-align: center !important; color: #b8c2cc !important; font-size: 14px !important; border-radius: 0 0 8px 8px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="text-align: center !important; padding-bottom: 15px !important;">
                                        <strong style="color: #f5f5f5 !important;">FireAlerts911</strong> - Keeping Communities Safe
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: center !important; padding-bottom: 15px !important;">
                                        <a href="${frontendUrl}" style="color: ${primaryColor} !important; text-decoration: none !important; margin: 0 10px !important;">Dashboard</a>
                                        <a href="${frontendUrl}/alerts" style="color: ${primaryColor} !important; text-decoration: none !important; margin: 0 10px !important;">View Alerts</a>
                                        <a href="${frontendUrl}/settings" style="color: ${primaryColor} !important; text-decoration: none !important; margin: 0 10px !important;">Settings</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: center !important;">
                                        © ${new Date().getFullYear()} FireAlerts911. All rights reserved.
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                </table>

            </td>
        </tr>
    </table>
</body>
</html>
        `
    };
}

function generateTextContent(user, incident, notification) {
    const incidentType = incident?.incidentType?.name || 'Incident';
    const location = `${incident.address}, ${incident.city}, ${incident.county}, ${incident.state}`;

    return `
INCIDENT UPDATE - ${incidentType.toUpperCase()}

Hello ${user.firstName || user.email},

This is an update for the ${incidentType} at ${location}.

INCIDENT DETAILS:
- Type: ${incidentType}
- Location: ${location}
- Status: ${incident.status?.name || 'Active'}
- Date/Time: ${incident.incidentDate ? new Date(incident.incidentDate).toLocaleString() : 'Not specified'}
- Severity: ${incident.severity || 'Moderate'}

${incident.description ? `DESCRIPTION:\n${incident.description}\n` : ''}

${incident.incidentDetail && (incident.incidentDetail.propertyOwner || incident.incidentDetail.dwellingType || incident.incidentDetail.yearBuilt || incident.incidentDetail.propertyValue) ? `PROPERTY INFORMATION:
${incident.incidentDetail.propertyOwner ? `- Owner: ${incident.incidentDetail.propertyOwner}\n` : ''}${incident.incidentDetail.dwellingType ? `- Type: ${incident.incidentDetail.dwellingType}\n` : ''}${incident.incidentDetail.yearBuilt ? `- Year Built: ${incident.incidentDetail.yearBuilt}\n` : ''}${incident.incidentDetail.propertyValue ? `- Property Value: ${new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(incident.incidentDetail.propertyValue)}\n` : ''}
` : ''}

This incident information has been updated. Please stay alert and follow local emergency guidance.

For more information, visit: ${process.env.FRONTEND_URL || 'https://firealerts911.com'}/view-incident.html?id=${incident.id}

Stay safe,
FireAlerts911 Team
    `.trim();
}

/**
 * Gmail-Compatible New Incident Template
 * For new incident notifications (not updates)
 */
function generateGmailCompatibleNewIncident(user, incident, notification) {
    // Use the same template as updates but modify the content
    const template = generateGmailCompatibleIncidentUpdate(user, incident, notification);

    const incidentType = incident?.incidentType?.name || 'Incident';
    const isFireIncident = incidentType.toLowerCase().includes('fire');
    const isWaterIncident = incidentType.toLowerCase().includes('water');

    let emoji = '🚨';
    if (isFireIncident) emoji = '🔥';
    else if (isWaterIncident) emoji = '💧';

    return {
        subject: `${emoji} FireAlerts911: ${incidentType} Alert - ${incident.address}`,
        text: template.text
            .replace('INCIDENT UPDATE', 'NEW INCIDENT ALERT')
            .replace('This is an update for', 'A new incident has been reported:')
            .replace('This incident information has been updated', 'This is a new incident in your area'),
        html: template.html
            .replace('INCIDENT UPDATE', 'NEW INCIDENT ALERT')
            .replace('This is an update for', 'A new incident has been reported:')
            .replace('This incident information has been updated', 'This is a new incident in your area')
    };
}

/**
 * Gmail-Compatible Test Email Template
 */
function generateGmailCompatibleTestEmail() {
    return {
        subject: '🧪 FireAlerts911 Test Email',
        text: `
FireAlerts911 Test Email

This is a test email from your FireAlerts911 system.

If you're receiving this email, your email delivery configuration is working correctly.

Time sent: ${new Date().toLocaleString()}

FireAlerts911 Team
        `.trim(),
        html: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 Test Email</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style type="text/css">
        /* Gmail-Compatible Email Styles */
        * { margin: 0 !important; padding: 0 !important; }
        body, table, td, p, a, li, blockquote { -webkit-text-size-adjust: 100% !important; -ms-text-size-adjust: 100% !important; }
        table, td { mso-table-lspace: 0pt !important; mso-table-rspace: 0pt !important; border-collapse: collapse !important; }
        img { -ms-interpolation-mode: bicubic !important; border: 0 !important; outline: none !important; text-decoration: none !important; }
        #outlook a { padding: 0 !important; }
        .ReadMsgBody { width: 100% !important; }
        .ExternalClass { width: 100% !important; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div { line-height: 100% !important; }

        /* Mobile responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container { width: 100% !important; }
            .mobile-padding { padding: 15px !important; }
        }
    </style>
</head>
<body style="margin: 0 !important; padding: 0 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
    <!-- Outer wrapper table for Gmail background -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #1a2035 !important; min-height: 100vh !important;">
        <tr>
            <td style="background-color: #1a2035 !important; padding: 20px 10px !important;">

                <!-- Main email container -->
                <table role="presentation" class="email-container" cellspacing="0" cellpadding="0" border="0" width="600" align="center" style="max-width: 600px !important; margin: 0 auto !important; background-color: #272e48 !important; border-radius: 8px !important; border: 1px solid #464d63 !important; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;">

                    <!-- Header -->
                    <tr>
                        <td style="background-color: #28a745 !important; background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important; padding: 30px 25px !important; text-align: center !important; border-radius: 8px 8px 0 0 !important;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="text-align: center !important;">
                                        <!-- Logo -->
                                        <div style="font-size: 32px !important; font-weight: bold !important; color: white !important; margin-bottom: 8px !important; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            🧪 FireAlerts911
                                        </div>
                                        <!-- Subtitle -->
                                        <div style="font-size: 14px !important; color: rgba(255, 255, 255, 0.95) !important; text-transform: uppercase !important; letter-spacing: 1.2px !important; margin-bottom: 15px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            Emergency Notification System
                                        </div>
                                        <!-- Test badge -->
                                        <div style="display: inline-block !important; background-color: rgba(255, 255, 255, 0.2) !important; color: white !important; padding: 8px 16px !important; border-radius: 20px !important; font-weight: bold !important; font-size: 14px !important; border: 1px solid rgba(255, 255, 255, 0.3) !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            EMAIL TEST
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="background-color: #272e48 !important; padding: 30px 25px !important;" class="mobile-padding">

                            <!-- Success Section -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: rgba(40, 167, 69, 0.1) !important; border: 1px solid #28a745 !important; border-radius: 8px !important; margin-bottom: 25px !important;">
                                <tr>
                                    <td style="padding: 20px !important;">
                                        <div style="font-size: 20px !important; font-weight: bold !important; color: #28a745 !important; margin-bottom: 15px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            ✅ Test Successful
                                        </div>
                                        <div style="color: #f5f5f5 !important; line-height: 1.6 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            This is a test email from your FireAlerts911 system. If you're receiving this email, your email delivery configuration is working correctly.
                                        </div>
                                    </td>
                                </tr>
                            </table>

                            <!-- Info Section -->
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: rgba(255, 255, 255, 0.05) !important; border-radius: 8px !important;">
                                <tr>
                                    <td style="padding: 20px !important;">
                                        <div style="font-size: 12px !important; color: #b8c2cc !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; margin-bottom: 10px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">System Information</div>
                                        <div style="color: #f5f5f5 !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                                            <strong>Time sent:</strong> ${new Date().toLocaleString()}<br>
                                            <strong>System:</strong> FireAlerts911 Email Service<br>
                                            <strong>Template:</strong> Gmail-Compatible Design
                                        </div>
                                    </td>
                                </tr>
                            </table>

                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #1a2035 !important; padding: 20px 25px !important; text-align: center !important; color: #b8c2cc !important; font-size: 14px !important; border-radius: 0 0 8px 8px !important; font-family: 'Roboto', 'Segoe UI', Arial, sans-serif !important;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="text-align: center !important; padding-bottom: 15px !important;">
                                        <strong style="color: #f5f5f5 !important;">FireAlerts911</strong> - Keeping Communities Safe
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: center !important;">
                                        © ${new Date().getFullYear()} FireAlerts911. All rights reserved.
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                </table>

            </td>
        </tr>
    </table>
</body>
</html>
        `
    };
}

module.exports = {
    generateGmailCompatibleIncidentUpdate,
    generateGmailCompatibleNewIncident,
    generateGmailCompatibleTestEmail
};
