/**
 * Schema Consistency Validation Script
 * 
 * This script validates that the Docker SQL schema matches the Sequelize models
 * and ensures all migrated fields are present in both.
 */

const mysql = require('mysql2/promise');
const { sequelize } = require('../models');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'dispatchuser',
  password: process.env.DB_PASSWORD || 'dispatchpassword',
  database: process.env.DB_NAME || 'firealerts911'
};

/**
 * Get table structure from database
 */
async function getDatabaseSchema(connection) {
  const [tables] = await connection.execute(`
    SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY, EXTRA, COLUMN_DEFAULT, COLUMN_COMMENT
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = ?
    ORDER BY TABLE_NAME, ORDINAL_POSITION
  `, [dbConfig.database]);
  
  const schema = {};
  tables.forEach(col => {
    if (!schema[col.TABLE_NAME]) {
      schema[col.TABLE_NAME] = [];
    }
    schema[col.TABLE_NAME].push({
      name: col.COLUMN_NAME,
      type: col.DATA_TYPE,
      nullable: col.IS_NULLABLE === 'YES',
      key: col.COLUMN_KEY,
      extra: col.EXTRA,
      default: col.COLUMN_DEFAULT,
      comment: col.COLUMN_COMMENT
    });
  });
  
  return schema;
}

/**
 * Validate incident_details table has all required fields
 */
function validateIncidentDetailsFields(schema) {
  const requiredFields = [
    'secondary_owner', 'owner_address', 'owner_phone', 'owner_email',
    'property_value', 'dwelling_type', 'year_built', 'square_footage',
    'bedrooms', 'bathrooms', 'response_details'
  ];
  
  const incidentDetails = schema.incident_details;
  if (!incidentDetails) {
    return { valid: false, error: 'incident_details table not found' };
  }
  
  const existingFields = incidentDetails.map(col => col.name);
  const missingFields = requiredFields.filter(field => !existingFields.includes(field));
  
  if (missingFields.length > 0) {
    return { 
      valid: false, 
      error: `Missing required fields in incident_details: ${missingFields.join(', ')}` 
    };
  }
  
  return { valid: true };
}

/**
 * Validate core tables exist
 */
function validateCoreTables(schema) {
  const requiredTables = [
    'users', 'companies', 'incidents', 'incident_details', 'incident_types',
    'statuses', 'states', 'counties', 'company_types', 'settings'
  ];
  
  const existingTables = Object.keys(schema);
  const missingTables = requiredTables.filter(table => !existingTables.includes(table));
  
  if (missingTables.length > 0) {
    return { 
      valid: false, 
      error: `Missing required tables: ${missingTables.join(', ')}` 
    };
  }
  
  return { valid: true };
}

/**
 * Main validation function
 */
async function validateSchemaConsistency() {
  let connection;
  
  try {
    console.log('🔍 Starting schema consistency validation...');
    
    // Connect to database
    console.log('🔌 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Test Sequelize connection
    console.log('🔌 Testing Sequelize connection...');
    await sequelize.authenticate();
    console.log('✅ Database connections established');
    
    // Get database schema
    console.log('📋 Analyzing database schema...');
    const schema = await getDatabaseSchema(connection);
    
    console.log(`📊 Found ${Object.keys(schema).length} tables in database`);
    
    // Validate core tables
    console.log('🔍 Validating core tables...');
    const coreTablesResult = validateCoreTables(schema);
    if (!coreTablesResult.valid) {
      console.error('❌ Core tables validation failed:', coreTablesResult.error);
      return false;
    }
    console.log('✅ All core tables present');
    
    // Validate incident_details fields
    console.log('🔍 Validating incident_details migration fields...');
    const incidentDetailsResult = validateIncidentDetailsFields(schema);
    if (!incidentDetailsResult.valid) {
      console.error('❌ Incident details validation failed:', incidentDetailsResult.error);
      console.log('💡 Run the migration script: npm run migrate-property-fields');
      return false;
    }
    console.log('✅ All incident_details migration fields present');
    
    // Display schema summary
    console.log('\n📊 Schema Summary:');
    Object.keys(schema).sort().forEach(tableName => {
      const columnCount = schema[tableName].length;
      console.log(`   ${tableName}: ${columnCount} columns`);
    });
    
    console.log('\n🎉 Schema consistency validation passed!');
    console.log('✅ Docker SQL schema matches current application requirements');
    console.log('✅ All migrated fields are present');
    console.log('✅ Database is ready for use');
    
    return true;
    
  } catch (error) {
    console.error('❌ Schema validation failed:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
    if (sequelize) {
      await sequelize.close();
    }
  }
}

// Run validation if executed directly
if (require.main === module) {
  validateSchemaConsistency()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unhandled error:', error);
      process.exit(1);
    });
}

module.exports = validateSchemaConsistency;
