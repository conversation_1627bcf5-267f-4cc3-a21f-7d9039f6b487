const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const permissionCheck = require('../middleware/permissionCheck');
const db = require('../models');
const { PERMISSIONS } = require('../models/permissions');
const notificationWorker = require('../workers/notificationWorker');

// @route   GET api/notifications
// @desc    Get all notifications
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Get query parameters for filtering
    const {
      status,
      type,
      userId,
      incidentId,
      page = 1,
      limit = 20
    } = req.query;

    // Build where clause
    const where = {};

    // Filter by status
    if (status) {
      where.status = status;
    }

    // Filter by type
    if (type) {
      where.type = type;
    }

    // Filter by user
    if (userId) {
      where.userId = userId;
    }

    // Filter by incident
    if (incidentId) {
      where.incidentId = incidentId;
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get notifications
    const { count, rows } = await db.notification.findAndCountAll({
      where,
      include: [
        { model: db.incident, include: [db.incidentType] },
        { model: db.user, attributes: ['id', 'email', 'firstName', 'lastName', 'username', 'phone', 'cellPhone'] }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    });
    res.json({
      notifications: rows,
      total: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page)
    });
  } catch (err) {
    console.error('Error retrieving notifications:', err);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// @route   GET api/notifications/:id
// @desc    Get notification by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const notification = await db.notification.findByPk(req.params.id, {
      include: [
        { model: db.incident, include: [db.incidentType] },
        { model: db.user, attributes: ['id', 'email', 'firstName', 'lastName', 'username', 'phone', 'cellPhone'] }
      ]
    });

    if (!notification) {
      return res.status(404).json({ success: false, error: 'Notification not found' });
    }

    res.json(notification);
  } catch (err) {
    console.error('Error retrieving notification:', err);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// @route   POST api/notifications/process
// @desc    Manually process pending notifications
// @access  Private (Admin only)
router.post('/process', [auth, permissionCheck(PERMISSIONS.ADMIN)], async (req, res) => {
  try {
    const batchSize = req.body.batchSize || 50;

    // Use the notification worker to process notifications
    console.log(`Manual notification processing triggered by user ${req.user.id}, batch size: ${batchSize}`);

    // Process notifications asynchronously, don't wait for completion
    notificationWorker.processNotifications(batchSize)
      .then(result => {
        console.log('Manual notification processing completed');
      })
      .catch(error => {
        console.error('Error in manual notification processing:', error);
      });

    // Return immediately
    res.json({
      success: true,
      message: 'Notification processing started',
    });
  } catch (err) {
    console.error('Error triggering notification processing:', err);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// @route   PUT api/notifications/:id/retry
// @desc    Retry a failed notification
// @access  Private (Admin only)
router.put('/:id/retry', [auth, permissionCheck(PERMISSIONS.ADMIN)], async (req, res) => {
  try {
    const notification = await db.notification.findByPk(req.params.id, {
      include: [
        { model: db.incident },
        { model: db.user, attributes: ['id', 'email', 'firstName', 'lastName', 'username', 'phone', 'cellPhone'] }
      ]
    });

    if (!notification) {
      return res.status(404).json({ success: false, error: 'Notification not found' });
    }

    // Reset notification status
    await notification.update({
      status: 'pending',
      errorMessage: null
    });

    // Process this specific notification
    notificationWorker.processNotifications(1)
      .then(() => {
        console.log(`Notification ${notification.id} retry processing completed`);
      })
      .catch(error => {
        console.error(`Error retrying notification ${notification.id}:`, error);
      });

    res.json({ success: true, message: 'Notification retry initiated' });
  } catch (err) {
    console.error('Error retrying notification:', err);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// @route   GET api/notifications/stats/count
// @desc    Get notification statistics
// @access  Private
router.get('/stats/count', auth, async (req, res) => {
  try {
    // Get counts by status
    const statusCounts = await db.notification.findAll({
      attributes: [
        'status',
        [db.sequelize.fn('count', db.sequelize.col('status')), 'count']
      ],
      group: ['status']
    });

    // Get counts by type
    const typeCounts = await db.notification.findAll({
      attributes: [
        'type',
        [db.sequelize.fn('count', db.sequelize.col('type')), 'count']
      ],
      group: ['type']
    });

    // Format results
    const stats = {
      total: await db.notification.count(),
      byStatus: statusCounts.reduce((acc, item) => {
        acc[item.status] = parseInt(item.dataValues.count);
        return acc;
      }, {}),
      byType: typeCounts.reduce((acc, item) => {
        acc[item.type] = parseInt(item.dataValues.count);
        return acc;
      }, {})
    };

    res.json(stats);
  } catch (err) {
    console.error('Error retrieving notification stats:', err);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// @route   POST api/notifications/bulk
// @desc    Send bulk notification to all subscribers
// @access  Private (Admin only)
router.post('/bulk', [auth, permissionCheck(PERMISSIONS.ADMIN)], async (req, res) => {
  try {
    const { notificationType, subject, message, includeMap } = req.body;

    if (!subject || !message) {
      return res.status(400).json({ msg: 'Subject and message are required' });
    }

    // Get all active company admin users (subscribers)
    const subscribers = await db.user.findAll({
      where: {
        role: 'company_admin',
        status: true
      },
      include: [
        {
          model: db.company,
          where: { status: true },
          required: true
        },
        {
          model: db.userPreference,
          required: false
        }
      ]
    });

    if (subscribers.length === 0) {
      return res.json({
        success: true,
        msg: 'No active subscribers found',
        recipientCount: 0
      });
    }

    // Create notifications for each subscriber
    const notifications = [];
    let recipientCount = 0;

    for (const subscriber of subscribers) {
      const userPref = subscriber.userPreference;

      // Check if user wants email notifications
      if (userPref && userPref.notify_by_email) {
        notifications.push({
          userId: subscriber.id,
          type: 'email',
          content: message,
          subject: subject,
          status: 'pending',
          notificationType: notificationType || 'bulk'
        });
        recipientCount++;
      }

      // Check if user wants SMS notifications
      if (userPref && userPref.notify_by_sms && subscriber.phone) {
        notifications.push({
          userId: subscriber.id,
          type: 'sms',
          content: `${subject}: ${message}`,
          status: 'pending',
          notificationType: notificationType || 'bulk'
        });
      }
    }

    // Save notifications to database
    if (notifications.length > 0) {
      await db.notification.bulkCreate(notifications);
    }

    // Log action
    await db.activity.create({
      user_id: req.user.id,
      action: `Sent bulk notification to ${recipientCount} subscribers`,
      entity_type: 'notification',
      details: `Subject: ${subject}`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      msg: 'Bulk notification sent successfully',
      recipientCount: recipientCount,
      notificationsCreated: notifications.length
    });

  } catch (err) {
    console.error('Error sending bulk notification:', err);
    res.status(500).json({ success: false, message: 'Server error sending bulk notification' });
  }
});

module.exports = router;
