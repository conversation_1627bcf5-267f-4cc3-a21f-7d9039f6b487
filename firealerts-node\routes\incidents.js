const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const permissionCheck = require('../middleware/permissionCheck');
const db = require('../models');
const { PERMISSIONS } = require('../models/permissions');

// @route   GET api/incidents
// @desc    Get all incidents
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Get query parameters for filtering
    const {
      startDate,
      endDate,
      typeId,
      type,
      status,
      search,
      state,
      county,
      page = 1,
      limit = 20,
      sortBy = 'incidentDate',
      sortOrder = 'DESC'
    } = req.query;

    // Build where clause
    const where = {};

    // Filter by date range
    if (startDate && endDate) {
      where.incidentDate = {
        [db.Sequelize.Op.between]: [
          new Date(startDate),
          new Date(endDate)
        ]
      };
    } else if (startDate) {
      where.incidentDate = {
        [db.Sequelize.Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.incidentDate = {
        [db.Sequelize.Op.lte]: new Date(endDate)
      };
    }

    // Filter by type
    if (typeId) {
      where.incidentTypeId = typeId;
    }

    // Filter by state
    if (state) {
      where.state = state;
    }

    // Filter by county
    if (county) {
      where.county = county;
    }

    // Text search across title, address, city
    if (search) {
      where[db.Sequelize.Op.or] = [
        { title: { [db.Sequelize.Op.iLike]: `%${search}%` } },
        { address: { [db.Sequelize.Op.iLike]: `%${search}%` } },
        { city: { [db.Sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // If user is a subscriber, only show incidents from their subscribed counties
    if (req.user.role === 'subscriber' || req.user.role === 'company_admin') {
      const userSubscriptions = await db.userSubscription.findAll({
        where: { userId: req.user.id },
        include: [{ model: db.county }]
      });

      if (userSubscriptions && userSubscriptions.length > 0) {
        where.county = {
          [db.Sequelize.Op.in]: userSubscriptions.map(sub => sub.county.name)
        };
      }
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Check if we need to include location data for map display
    const includeLocation = req.query.includeLocation === 'true';

    // Build include array based on requirements
    const includeArray = [
      { model: db.user, attributes: ['id', 'firstName', 'lastName', 'username'] }
    ];

    // Add incident type with optional filtering
    const incidentTypeInclude = { model: db.incidentType };
    if (type) {
      incidentTypeInclude.where = { category: type };
    }
    includeArray.push(incidentTypeInclude);

    // Add status with optional filtering
    const statusInclude = { model: db.status };
    if (status) {
      statusInclude.where = { name: status };
    }
    includeArray.push(statusInclude);

    // Include incident details if requested or for map display
    if (includeLocation || req.query.includeDetails === 'true') {
      includeArray.push({ model: db.incidentDetail });
    }

    // Build order clause
    let orderClause;
    const validSortColumns = {
      'id': 'id',
      'title': 'title',
      'date': 'incidentDate',
      'incidentDate': 'incidentDate',
      'type': [db.incidentType, 'name'],
      'status': [db.status, 'name'],
      'location': 'address'
    };

    if (validSortColumns[sortBy]) {
      const column = validSortColumns[sortBy];
      const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

      if (Array.isArray(column)) {
        // For associated model sorting
        orderClause = [[...column, order]];
      } else {
        // For direct column sorting
        orderClause = [[column, order]];
      }
    } else {
      // Default sorting
      orderClause = [['incidentDate', 'DESC']];
    }

    // Get incidents with associated data
    const { count, rows } = await db.incident.findAndCountAll({
      where,
      include: includeArray,
      order: orderClause,
      limit: parseInt(limit),
      offset
    });

    // For map requests, filter out incidents without coordinates
    let filteredRows = rows;
    if (includeLocation) {
      filteredRows = rows.filter(incident =>
        incident.latitude && incident.longitude &&
        !isNaN(parseFloat(incident.latitude)) &&
        !isNaN(parseFloat(incident.longitude))
      );
    }

    res.json({
      success: true,
      data: filteredRows,
      incidents: filteredRows, // Keep for backward compatibility
      totalCount: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      hasLocationData: includeLocation
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/incidents
// @desc    Create a new incident
// @access  Private (Admin and Dispatcher only)
router.post(
  '/',
  [
    auth,
    permissionCheck(PERMISSIONS.CREATE_INCIDENT),
    [
      check('title', 'Title is required').not().isEmpty(),
      check('address', 'Address is required').not().isEmpty(),
      check('city', 'City is required').not().isEmpty(),
      check('county', 'County is required').not().isEmpty(),
      check('state', 'State is required').not().isEmpty(),
      check('incidentTypeId', 'Incident type is required').not().isEmpty()
    ]
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Extract incident data from request body
      const {
        title, address, city, county, state, zip,
        latitude, longitude, description, incidentTypeId,
        statusId, severity, incidentDetail, notifySubscribers
      } = req.body;

      // Create incident with only the fields that match the model
      const incident = await db.incident.create({
        title,
        address,
        city,
        county,
        state,
        zip,
        latitude: latitude || null,
        longitude: longitude || null,
        description,
        incidentTypeId,
        statusId: statusId || 1, // Default to first status (active)
        userId: req.user.id,
        severity: severity || 'moderate',
        incidentDate: new Date() // Set current date/time
      });

      // If incidentDetail is provided, create it
      if (incidentDetail) {
        // Sanitize incident detail data - convert empty strings to null for decimal fields
        const sanitizedIncidentDetail = {
          ...incidentDetail,
          estimatedDamage: incidentDetail.estimatedDamage === '' ? null : incidentDetail.estimatedDamage,
          propertyValue: incidentDetail.propertyValue === '' ? null : incidentDetail.propertyValue,
          bathrooms: incidentDetail.bathrooms === '' ? null : incidentDetail.bathrooms
        };

        await db.incidentDetail.create({
          ...sanitizedIncidentDetail,
          incidentId: incident.id
        });
      }

      // Initialize notifications array outside the conditional block
      const notifications = [];

      // Find users in the affected county if notifications requested
      if (notifySubscribers) {
        // First, find the county by name and state to get its ID
        const county = await db.county.findOne({
          where: {
            name: incident.county,
            state: incident.state,
            is_active: true
          }
        });

        if (!county) {
          console.log(`County not found: ${incident.county}`);
        } else {
          // Find users who are subscribed to this county and have notification preferences
          const usersToNotify = await db.user.findAll({
            include: [
              {
                model: db.userSubscription,
                as: 'subscriptions',
                where: { countyId: county.id },
                required: true
              },
              {
                model: db.userPreference,
                required: true,
                where: {
                  [db.Sequelize.Op.or]: [
                    { notifyByEmail: true },
                    { notifyBySms: true },
                    { notifyByPush: true }
                  ]
                }
              },
              {
                model: db.company,
                required: false // Include company info if available
              }
            ],
            where: {
              status: true // Only active users
            }
          });

          console.log(`Found ${usersToNotify.length} users to notify about incident in ${incident.county}`);

          // Get incident type details
          const incidentTypeDetails = await db.incidentType.findByPk(incident.incidentTypeId);
          const incidentTypeName = incidentTypeDetails ? incidentTypeDetails.name : 'incident';

          for (const user of usersToNotify) {
            const userPref = user.userPreference;

            // Check if user wants to be notified about this type of incident
            let shouldNotify = false;
            if (incidentTypeName.toLowerCase().includes('fire') && userPref.fireIncidentAlert) {
              shouldNotify = true;
            } else if (incidentTypeName.toLowerCase().includes('water') && userPref.waterIncidentAlert) {
              shouldNotify = true;
            } else if (userPref.otherIncidentAlert) {
              shouldNotify = true;
            }

            if (!shouldNotify) {
              continue;
            }

            // Create notifications based on user preferences
            if (userPref.notifyByEmail && user.email) {
              notifications.push({
                incidentId: incident.id,
                userId: user.id,
                type: 'email',
                content: `New ${incidentTypeName} at ${incident.address}, ${incident.city}`
              });
            }

            if (userPref.notifyBySms && user.cellPhone) {
              notifications.push({
                incidentId: incident.id,
                userId: user.id,
                type: 'sms',
                content: `FireAlerts911: New ${incidentTypeName} reported at ${incident.address}, ${incident.city}`
              });
            }

            if (userPref.notifyByPush) {
              notifications.push({
                incidentId: incident.id,
                userId: user.id,
                type: 'push',
                content: `New ${incidentTypeName} reported`
              });
            }
          }
        }
      }

      // Save notifications to database (moved outside the if block)
      if (notifications.length > 0) {
        try {
          await db.notification.bulkCreate(notifications);

          // Update incident with notification count
          await incident.update({
            notificationSent: true,
            notificationCount: notifications.length
          });

          console.log(`Created ${notifications.length} notification records for incident ${incident.id}`);
        } catch (notificationError) {
          console.error('Error creating notifications:', notificationError);
          // Don't fail the entire incident creation if notifications fail
          // Just log the error and continue
        }
      }

      // Return the created incident with its details
      const createdIncident = await db.incident.findByPk(incident.id, {
        include: [
          { model: db.incidentType },
          { model: db.status },
          { model: db.user, attributes: ['id', 'firstName', 'lastName', 'username'] },
          { model: db.incidentDetail }
        ]
      });

      res.status(201).json(createdIncident);
    } catch (err) {
      console.error('Error creating incident:', err);
      res.status(500).json({ error: err.message || 'Server Error' });
    }
  }
);

// @route   GET api/incidents/:id
// @desc    Get incident by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const incident = await db.incident.findByPk(req.params.id, {
      include: [
        { model: db.incidentType },
        { model: db.status },
        { model: db.user, attributes: ['id', 'firstName', 'lastName', 'username'] },
        { model: db.incidentDetail } // Include incident details for complete data
      ]
    });

    if (!incident) {
      return res.status(404).json({
        success: false,
        msg: 'Incident not found'
      });
    }

    // If user is a subscriber, verify they have access to this county
    if (req.user.role === 'subscriber') {
      // Check if user has access to this county through userLocation
      const userWithLocations = await db.user.findByPk(req.user.id, {
        include: [{
          model: db.userLocation,
          as: 'locations',
          where: { county: incident.county },
          required: false
        }]
      });

      const hasAccess = userWithLocations && userWithLocations.locations && userWithLocations.locations.length > 0;

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          msg: 'Not authorized to view this incident'
        });
      }
    }

    // Return standardized response format
    res.json({
      success: true,
      data: incident
    });
  } catch (err) {
    console.error('Error fetching incident by ID:', err.message);
    res.status(500).json({
      success: false,
      msg: 'Server Error',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

// @route   PUT api/incidents/:id
// @desc    Update an incident
// @access  Private (Admin and Dispatcher only)
router.put('/:id', [auth, permissionCheck(PERMISSIONS.UPDATE_INCIDENT)], async (req, res) => {
  try {
    const incident = await db.incident.findByPk(req.params.id);

    if (!incident) {
      return res.status(404).json({
        success: false,
        msg: 'Incident not found'
      });
    }

    // Extract incidentDetail from request body if present
    const { incidentDetail, ...rawIncidentData } = req.body;

    // Sanitize incident data - convert empty strings to null for decimal fields
    const incidentData = {
      ...rawIncidentData,
      latitude: rawIncidentData.latitude === '' ? null : rawIncidentData.latitude,
      longitude: rawIncidentData.longitude === '' ? null : rawIncidentData.longitude
    };

    // Update the main incident record
    await incident.update(incidentData);

    // Update or create incident details if provided
    if (incidentDetail) {
      // Sanitize incident detail data - convert empty strings to null for decimal fields
      const sanitizedIncidentDetail = {
        ...incidentDetail,
        estimatedDamage: incidentDetail.estimatedDamage === '' ? null : incidentDetail.estimatedDamage,
        propertyValue: incidentDetail.propertyValue === '' ? null : incidentDetail.propertyValue,
        bathrooms: incidentDetail.bathrooms === '' ? null : incidentDetail.bathrooms
      };

      const existingDetail = await db.incidentDetail.findOne({
        where: { incidentId: req.params.id }
      });

      if (existingDetail) {
        // Update existing incident detail
        await existingDetail.update(sanitizedIncidentDetail);
      } else {
        // Create new incident detail
        await db.incidentDetail.create({
          ...sanitizedIncidentDetail,
          incidentId: req.params.id
        });
      }
    }

    // Return the updated incident with all associations
    const updatedIncident = await db.incident.findByPk(req.params.id, {
      include: [
        { model: db.incidentType },
        { model: db.status },
        { model: db.user, attributes: ['id', 'firstName', 'lastName', 'username'] },
        { model: db.incidentDetail }
      ]
    });

    res.json({
      success: true,
      data: updatedIncident,
      msg: 'Incident updated successfully'
    });
  } catch (err) {
    console.error('Error updating incident:', err.message);
    res.status(500).json({
      success: false,
      msg: 'Server Error',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

// @route   DELETE api/incidents/:id
// @desc    Delete an incident
// @access  Private (Admin only)
router.delete('/:id', [auth, permissionCheck(PERMISSIONS.DELETE_INCIDENT)], async (req, res) => {
  try {
    const incident = await db.incident.findByPk(req.params.id);

    if (!incident) {
      return res.status(404).json({ msg: 'Incident not found' });
    }

    await incident.destroy();

    res.json({ msg: 'Incident removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/incidents/:id/notify
// @desc    Send notifications for an incident (new or update)
// @access  Private (Admin and Dispatcher only)
router.post('/:id/notify', [auth, permissionCheck(PERMISSIONS.SEND_NOTIFICATIONS)], async (req, res) => {
  try {
    // Get the incident with all related data
    const incident = await db.incident.findByPk(req.params.id, {
      include: [
        { model: db.incidentType },
        { model: db.status },
        { model: db.user, attributes: ['id', 'firstName', 'lastName', 'username'] },
        { model: db.incidentDetail }
      ]
    });

    if (!incident) {
      return res.status(404).json({
        success: false,
        msg: 'Incident not found'
      });
    }

    // Determine if this is an update notification or new incident notification
    const isUpdate = req.body.isUpdate || incident.notificationSent || false;
    const notificationType = isUpdate ? 'update' : 'new';

    // First, find the county by name and state to get its ID
    // Handle both formats: "Alameda County" and "Alameda"
    let countyName = incident.county;
    let county = await db.county.findOne({
      where: {
        name: countyName,
        state: incident.state,
        is_active: true
      }
    });

    // If not found and the incident county includes "County", try without it
    if (!county && countyName && countyName.includes('County')) {
      const countyNameWithoutSuffix = countyName.replace(/\s+County$/i, '').trim();
      console.log(`County not found with full name '${countyName}', trying '${countyNameWithoutSuffix}'`);

      county = await db.county.findOne({
        where: {
          name: countyNameWithoutSuffix,
          state: incident.state,
          is_active: true
        }
      });
    }

    // If still not found and the incident county doesn't include "County", try with it
    if (!county && countyName && !countyName.includes('County')) {
      const countyNameWithSuffix = `${countyName} County`;
      console.log(`County not found with name '${countyName}', trying '${countyNameWithSuffix}'`);

      county = await db.county.findOne({
        where: {
          name: countyNameWithSuffix,
          state: incident.state,
          is_active: true
        }
      });
    }

    if (!county) {
      console.log(`County not found with any variation: ${incident.county}`);
      return res.status(400).json({
        success: false,
        msg: `County '${incident.county}' not found in system`
      });
    }

    console.log(`Found county: ${county.name} (ID: ${county.id}) for incident county: ${incident.county}`);

    // Find users who are subscribed to this county and have notification preferences
    const usersToNotify = await db.user.findAll({
      include: [
        {
          model: db.userSubscription,
          as: 'subscriptions',
          where: { countyId: county.id },
          required: true
        },
        {
          model: db.userPreference,
          required: true,
          where: {
            [db.Sequelize.Op.or]: [
              { notifyByEmail: true },
              { notifyBySms: true },
              { notifyByPush: true }
            ]
          }
        },
        {
          model: db.company,
          required: false // Include company info if available
        }
      ],
      where: {
        status: true // Only active users
      }
    });

    console.log(`Found ${usersToNotify.length} users to notify for county: ${incident.county}`);

    // Create notification records
    const notifications = [];
    const incidentTypeName = incident.incidentType?.name || 'incident';

    for (const user of usersToNotify) {
      const userPref = user.userPreference;

      // Check if user wants to be notified about this type of incident
      let shouldNotify = false;
      if (incidentTypeName.toLowerCase().includes('fire') && userPref.fireIncidentAlert) {
        shouldNotify = true;
      } else if (incidentTypeName.toLowerCase().includes('water') && userPref.waterIncidentAlert) {
        shouldNotify = true;
      } else if (userPref.otherIncidentAlert) {
        shouldNotify = true;
      }

      if (!shouldNotify) {
        console.log(`User ${user.email} does not want notifications for ${incidentTypeName}`);
        continue;
      }

      // Create notifications based on user preferences with appropriate content
      const actionText = isUpdate ? 'Updated' : 'New';
      const actionTextLower = isUpdate ? 'update for' : 'reported at';

      if (userPref.notifyByEmail && user.email) {
        notifications.push({
          incidentId: incident.id,
          userId: user.id,
          type: 'email',
          content: `${actionText} ${incidentTypeName} ${actionTextLower} ${incident.address}, ${incident.city}`,
          status: 'pending',
          notificationType: notificationType,
          templateType: isUpdate ? 'incidentUpdate' : 'incident' // Use new template for updates
        });
      }

      if (userPref.notifyBySms && (user.phone || user.cellPhone)) {
        notifications.push({
          incidentId: incident.id,
          userId: user.id,
          type: 'sms',
          content: `FireAlerts911: ${actionText} ${incidentTypeName} ${actionTextLower} ${incident.address}, ${incident.city}`,
          status: 'pending',
          notificationType: notificationType
        });
      }

      if (userPref.notifyByPush) {
        notifications.push({
          incidentId: incident.id,
          userId: user.id,
          type: 'push',
          content: `${actionText} ${incidentTypeName} ${isUpdate ? 'update' : 'reported'}`,
          status: 'pending',
          notificationType: notificationType
        });
      }
    }

    console.log(`Creating ${notifications.length} notifications`);

    // Save notifications to database
    if (notifications.length > 0) {
      await db.notification.bulkCreate(notifications);
    }

    // Update incident
    await incident.update({
      notificationSent: true,
      notificationCount: (incident.notificationCount || 0) + notifications.length
    });

    // Log activity
    await db.activity.create({
      action: isUpdate ? 'send_update_notifications' : 'send_notifications',
      details: JSON.stringify({
        incidentId: incident.id,
        notificationCount: notifications.length,
        county: incident.county,
        usersFound: usersToNotify.length,
        notificationType: notificationType
      }),
      userId: req.user.id,
      module: 'incidents',
      severity: 'info'
    });

    res.json({
      success: true,
      msg: `${isUpdate ? 'Update' : ''} Notifications queued successfully`,
      notificationsSent: notifications.length,
      usersFound: usersToNotify.length,
      county: incident.county,
      notificationType: notificationType
    });
  } catch (err) {
    console.error('Error sending notifications:', err);
    res.status(500).json({
      success: false,
      msg: 'Server Error while sending notifications',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

// @route   GET api/incidents/stats/counts
// @desc    Get count of incidents by type
// @access  Private
router.get('/stats/counts', auth, async (req, res) => {
  try {
    // Special case for admin role - bypass permission check
    if (req.user.role === 'admin' || (req.query.role && req.query.role === 'admin')) {
      // Admin user - proceed without permission check
    }
    // Check for dispatcher permission if not admin
    else if (req.user.role !== 'dispatcher' && (!req.query.role || req.query.role !== 'dispatcher')) {
      return res.status(403).json({
        msg: 'Permission denied. Required permission: admin,dispatcher',
        requiredPermission: 'VIEW_INCIDENT_STATS',
        yourRole: req.user.role
      });
    }

    // Get query parameters for filtering
    const { startDate, endDate } = req.query;

    // Build where clause for date filtering
    const where = {};

    // Filter by date range if specified
    if (startDate && endDate) {
      where.incidentDate = {
        [db.Sequelize.Op.between]: [
          new Date(startDate),
          new Date(endDate)
        ]
      };
    } else if (startDate) {
      where.incidentDate = {
        [db.Sequelize.Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.incidentDate = {
        [db.Sequelize.Op.lte]: new Date(endDate)
      };
    }

    // If user is a subscriber, only count incidents from their subscribed counties
    if (req.user.role === 'subscriber' || req.user.role === 'company_admin') {
      const userSubscriptions = await db.userSubscription.findAll({
        where: { userId: req.user.id },
        include: [{ model: db.county }]
      });

      if (userSubscriptions && userSubscriptions.length > 0) {
        where.county = {
          [db.Sequelize.Op.in]: userSubscriptions.map(sub => sub.county.name)
        };
      }
    }

    // Get all incident types first
    const incidentTypes = await db.incidentType.findAll();

    // Create a map to store counts by category
    let fireCount = 0;
    let waterCount = 0;
    let otherCount = 0;

    // For each incident type, get the count
    for (const type of incidentTypes) {
      const count = await db.incident.count({
        where: {
          ...where,
          incidentTypeId: type.id
        }
      });

      // Add to the appropriate category count
      if (type.category === 'fire') {
        fireCount += count;
      } else if (type.category === 'water') {
        waterCount += count;
      } else {
        otherCount += count;
      }
    }

    // Get total count of all incidents matching the criteria
    const totalCount = await db.incident.count({ where });

    // Return the counts
    res.json({
      fireCount,
      waterCount,
      otherCount,
      totalCount,
      categories: {
        fire: fireCount,
        water: waterCount,
        other: otherCount
      }
    });
  } catch (err) {
    console.error('Error getting incident counts:', err);
    res.status(500).json({
      success: false,
      message: 'Server error while getting incident counts',
      error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

module.exports = router;
