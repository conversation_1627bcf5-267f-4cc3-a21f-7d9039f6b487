'use strict';

module.exports = (sequelize, DataTypes) => {
  const ZipCode = sequelize.define('zip_code', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    zip: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    state_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'states',
        key: 'id'
      }
    },
    county_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'counties',
        key: 'id'
      }
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 7),
      allowNull: true
    },
    longitude: {
      type: DataTypes.DECIMAL(10, 7),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'zip_codes',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['zip']
      },
      {
        fields: ['county_id']
      },
      {
        fields: ['state_id']
      }
    ]
  });

  return ZipCode;
};