/**
 * Email Service
 * 
 * Centralized email handling service that manages different email types
 * and templates for the FireAlerts911 system.
 * 
 * Features:
 * - Categorized email types (Authentication, Emergency, System, Administrative)
 * - Priority-based sending
 * - Multiple provider support with fallback
 * - Template management
 * - Delivery tracking and logging
 */

const emailTemplates = require('./emailTemplates');
const emailProviders = require('./emailProviders');
const { EMAIL_TYPES, EMAIL_CATEGORIES, EMAIL_PRIORITIES, TYPE_CATEGORY_MAP, TYPE_PRIORITY_MAP } = require('./emailConstants');

class EmailService {
    constructor() {
        this.providers = null;
        this.initialized = false;
        this.deliveryStats = {
            sent: 0,
            failed: 0,
            byCategory: {},
            byType: {}
        };
    }

    async initialize() {
        if (!this.initialized) {
            this.providers = await emailProviders.initialize();
            this.initialized = true;
        }
    }

    /**
     * Get email category and priority for a given type
     */
    getEmailMetadata(type) {
        return {
            category: TYPE_CATEGORY_MAP[type] || EMAIL_CATEGORIES.SYSTEM,
            priority: TYPE_PRIORITY_MAP[type] || EMAIL_PRIORITIES.NORMAL
        };
    }

    /**
     * Update delivery statistics
     */
    updateStats(type, success) {
        const metadata = this.getEmailMetadata(type);
        
        if (success) {
            this.deliveryStats.sent++;
        } else {
            this.deliveryStats.failed++;
        }
        
        // Update by category
        if (!this.deliveryStats.byCategory[metadata.category]) {
            this.deliveryStats.byCategory[metadata.category] = { sent: 0, failed: 0 };
        }
        this.deliveryStats.byCategory[metadata.category][success ? 'sent' : 'failed']++;
        
        // Update by type
        if (!this.deliveryStats.byType[type]) {
            this.deliveryStats.byType[type] = { sent: 0, failed: 0 };
        }
        this.deliveryStats.byType[type][success ? 'sent' : 'failed']++;
    }

    /**
     * Get delivery statistics
     */
    getStats() {
        return this.deliveryStats;
    }    // ==========================================
    // AUTHENTICATION EMAILS
    // ==========================================

    /**
     * Send a welcome email to a new user
     */
    async sendWelcomeEmail(user, temporaryPassword = null) {
        await this.initialize();
        
        const template = emailTemplates.welcome.generate(user, temporaryPassword);
        
        try {
            const result = await this.sendEmail({
                to: user.email,
                subject: template.subject,
                html: template.html,
                text: template.text,
                type: EMAIL_TYPES.WELCOME
            });
            this.updateStats(EMAIL_TYPES.WELCOME, true);
            return result;
        } catch (error) {
            this.updateStats(EMAIL_TYPES.WELCOME, false);
            throw error;
        }
    }

    /**
     * Send a password reset email
     */
    async sendPasswordResetEmail(user, resetToken) {
        await this.initialize();
        
        const template = emailTemplates.passwordReset.generate(user, resetToken);
        
        try {
            const result = await this.sendEmail({
                to: user.email,
                subject: template.subject,
                html: template.html,
                text: template.text,
                type: EMAIL_TYPES.PASSWORD_RESET
            });
            this.updateStats(EMAIL_TYPES.PASSWORD_RESET, true);
            return result;
        } catch (error) {
            this.updateStats(EMAIL_TYPES.PASSWORD_RESET, false);
            throw error;
        }
    }

    // ==========================================
    // EMERGENCY & INCIDENT NOTIFICATIONS
    // ==========================================

    /**
     * Send an incident notification email
     */
    async sendIncidentNotification(user, incident, notification) {
        await this.initialize();
        
        const template = emailTemplates.incident.generate(user, incident, notification);
        
        try {
            const result = await this.sendEmail({
                to: user.email,
                subject: template.subject,
                html: template.html,
                text: template.text,
                type: EMAIL_TYPES.INCIDENT_ALERT
            });
            this.updateStats(EMAIL_TYPES.INCIDENT_ALERT, true);
            return result;
        } catch (error) {
            this.updateStats(EMAIL_TYPES.INCIDENT_ALERT, false);
            throw error;
        }
    }

    // ==========================================
    // SYSTEM & ADMINISTRATIVE EMAILS
    // ==========================================

    /**
     * Send a test email
     */
    async sendTestEmail(email, provider = 'auto') {
        await this.initialize();
        
        const template = emailTemplates.test.generate();
        
        try {
            const result = await this.sendEmail({
                to: email,
                subject: template.subject,
                html: template.html,
                text: template.text,
                type: EMAIL_TYPES.TEST
            }, provider);
            this.updateStats(EMAIL_TYPES.TEST, true);
            return result;
        } catch (error) {
            this.updateStats(EMAIL_TYPES.TEST, false);
            throw error;
        }
    }

    /**
     * Send a bulk notification email
     */
    async sendBulkNotification(recipients, subject, message, includeMap = false) {
        await this.initialize();
        
        const template = emailTemplates.bulk.generate(subject, message, includeMap);
        
        const results = [];
        for (const recipient of recipients) {
            try {
                const result = await this.sendEmail({
                    to: recipient.email,
                    subject: template.subject,
                    html: template.html,
                    text: template.text,
                    type: EMAIL_TYPES.BULK_NOTIFICATION
                });
                results.push({ recipient: recipient.email, success: true, result });
                this.updateStats(EMAIL_TYPES.BULK_NOTIFICATION, true);
            } catch (error) {
                results.push({ recipient: recipient.email, success: false, error: error.message });
                this.updateStats(EMAIL_TYPES.BULK_NOTIFICATION, false);
            }
        }
        
        return results;
    }

    /**
     * Core email sending method
     */
    async sendEmail(emailData, preferredProvider = 'auto') {
        if (!this.initialized) {
            throw new Error('Email service not initialized');
        }

        const db = require('../models');
        const fromEmail = await db.systemSetting.getValue('email_from', process.env.EMAIL_FROM || '<EMAIL>');
        
        const emailContent = {
            from: `"FireAlerts911" <${fromEmail}>`,
            to: emailData.to,
            subject: emailData.subject,
            text: emailData.text,
            html: emailData.html
        };

        // Try to send with preferred provider, fallback to alternatives
        return await this.providers.send(emailContent, preferredProvider);
    }
}

module.exports = new EmailService();
