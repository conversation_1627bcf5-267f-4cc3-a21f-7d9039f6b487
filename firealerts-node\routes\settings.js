const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const adminAccess = require('../middleware/adminAccess');
const db = require('../models');

// Apply both auth and admin middleware
const adminOnly = [auth, adminAccess];

// -- API Keys Management Routes --

// @route   GET api/settings/api-keys
// @desc    Get all API keys with enhanced security information
// @access  Private (Admin only)
router.get('/api-keys', adminOnly, async (req, res) => {
  try {
    const apiKeys = await db.systemSetting.findAll({
      where: {
        category: 'api_key'
      },
      attributes: ['key', 'description', 'isSecret', 'updatedAt', 'expiresAt', 'lastAccessedAt', 'accessCount']
    });

    // Format response with enhanced security information
    const formattedKeys = apiKeys.map(key => {
      const now = new Date();
      const isExpired = key.expiresAt && now > key.expiresAt;
      const isExpiringSoon = key.expiresAt && !isExpired &&
        (key.expiresAt - now) < (30 * 24 * 60 * 60 * 1000); // 30 days

      return {
        id: key.key,
        name: key.key,
        description: key.description,
        isSet: true,
        isSecret: key.isSecret,
        lastUpdated: key.updatedAt,
        expiresAt: key.expiresAt,
        lastAccessedAt: key.lastAccessedAt,
        accessCount: key.accessCount || 0,
        status: isExpired ? 'expired' : isExpiringSoon ? 'expiring' : 'active'
      };
    });

    // Log this access
    await db.activity.create({
      action: 'api_keys_list_accessed',
      details: JSON.stringify({
        keyCount: formattedKeys.length,
        expiredCount: formattedKeys.filter(k => k.status === 'expired').length,
        expiringCount: formattedKeys.filter(k => k.status === 'expiring').length
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json(formattedKeys);
  } catch (err) {
    console.error('Error retrieving API keys:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Specific routes must come before parameterized routes
// @route   GET api/settings/api-keys/session-state
// @desc    Get API key UI state from session (replaces localStorage)
// @access  Private (Admin only)
router.get('/api-keys/session-state', adminOnly, async (req, res) => {
  try {
    // For now, return empty session state since we're not using sessions
    // This can be enhanced later with database-backed session storage
    const sessionState = {};

    // Log session state access
    await db.activity.create({
      action: 'session_state_accessed',
      details: JSON.stringify({
        stateKeys: [],
        stateCount: 0,
        note: 'Session storage temporarily disabled'
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json(sessionState);
  } catch (err) {
    console.error('Error retrieving session state:', err);

    // Log the error
    await db.activity.create({
      action: 'session_state_access_error',
      details: JSON.stringify({
        error: err.message
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'error',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   GET api/settings/api-keys/expiring
// @desc    Get API keys that are expiring soon
// @access  Private (Admin only)
router.get('/api-keys/expiring', adminOnly, async (req, res) => {
  try {
    const daysAhead = parseInt(req.query.days) || 30;
    const expiringKeys = await db.systemSetting.getExpiringKeys(daysAhead);

    // Log this access
    await db.activity.create({
      action: 'expiring_keys_checked',
      details: JSON.stringify({
        daysAhead,
        expiringCount: expiringKeys.length
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json(expiringKeys);
  } catch (err) {
    console.error('Error retrieving expiring API keys:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   GET api/settings/api-keys/expired
// @desc    Get expired API keys
// @access  Private (Admin only)
router.get('/api-keys/expired', adminOnly, async (req, res) => {
  try {
    const expiredKeys = await db.systemSetting.getExpiredKeys();

    // Log this access
    await db.activity.create({
      action: 'expired_keys_checked',
      details: JSON.stringify({
        expiredCount: expiredKeys.length
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'warning',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json(expiredKeys);
  } catch (err) {
    console.error('Error retrieving expired API keys:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   GET api/settings/api-keys/:key
// @desc    Get a specific API key (metadata only, no secret values)
// @access  Private (Admin only)
router.get('/api-keys/:key', adminOnly, async (req, res) => {
  try {
    const setting = await db.systemSetting.findByPk(req.params.key);

    if (!setting) {
      // Log failed access attempt
      await db.activity.create({
        action: 'api_key_access_failed',
        details: JSON.stringify({
          key: req.params.key,
          reason: 'Key not found'
        }),
        userId: req.user.id,
        module: 'settings',
        severity: 'warning',
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      return res.status(404).json({ error: 'API key not found' });
    }

    // Log successful access
    await db.activity.create({
      action: 'api_key_individual_access',
      details: JSON.stringify({
        key: req.params.key,
        isSecret: setting.isSecret,
        hasExpiration: !!setting.expiresAt
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // Return the actual decrypted value for authenticated users
    // The value getter in the model will automatically decrypt if needed
    const response = {
      key: setting.key,
      value: setting.value, // This will be decrypted by the model getter
      description: setting.description,
      category: setting.category,
      isSecret: setting.isSecret,
      expiresAt: setting.expiresAt,
      lastAccessedAt: setting.lastAccessedAt,
      accessCount: setting.accessCount,
      createdAt: setting.createdAt,
      updatedAt: setting.updatedAt
    };

    res.json(response);
  } catch (err) {
    console.error('Error retrieving API key:', err);

    // Log the error
    await db.activity.create({
      action: 'api_key_access_error',
      details: JSON.stringify({
        key: req.params.key,
        error: err.message
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'error',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   GET api/settings/api-keys/:key/edit
// @desc    Get a specific API key with decrypted value for editing
// @access  Private (Admin only)
router.get('/api-keys/:key/edit', adminOnly, async (req, res) => {
  try {
    const setting = await db.systemSetting.findByPk(req.params.key);

    if (!setting) {
      // Log failed access attempt
      await db.activity.create({
        action: 'api_key_edit_access_failed',
        details: JSON.stringify({
          key: req.params.key,
          reason: 'Key not found'
        }),
        userId: req.user.id,
        module: 'settings',
        severity: 'warning',
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      return res.status(404).json({ error: 'API key not found' });
    }

    // Log successful edit access (more sensitive than regular access)
    await db.activity.create({
      action: 'api_key_edit_access',
      details: JSON.stringify({
        key: req.params.key,
        isSecret: setting.isSecret,
        hasExpiration: !!setting.expiresAt,
        purpose: 'admin_edit'
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // Special handling for Mailgun unified configuration
    if (req.params.key === 'mailgun_config') {
      try {
        const configValue = setting.value; // This will be decrypted by the model getter
        const mailgunConfig = JSON.parse(configValue);

        const response = {
          key: setting.key,
          value: mailgunConfig.api_key,
          mailgunDomain: mailgunConfig.domain,
          description: setting.description,
          category: setting.category,
          isSecret: setting.isSecret,
          expiresAt: setting.expiresAt,
          lastAccessedAt: setting.lastAccessedAt,
          accessCount: setting.accessCount,
          createdAt: setting.createdAt,
          updatedAt: setting.updatedAt,
          isMailgunConfig: true
        };

        return res.json(response);
      } catch (parseError) {
        console.error('Error parsing Mailgun configuration:', parseError);
        return res.status(500).json({ error: 'Invalid Mailgun configuration format' });
      }
    }

    // Return the actual decrypted value for editing (admin only)
    // The value getter in the model will automatically decrypt if needed
    const response = {
      key: setting.key,
      value: setting.value, // This will be decrypted by the model getter
      description: setting.description,
      category: setting.category,
      isSecret: setting.isSecret,
      expiresAt: setting.expiresAt,
      lastAccessedAt: setting.lastAccessedAt,
      accessCount: setting.accessCount,
      createdAt: setting.createdAt,
      updatedAt: setting.updatedAt
    };

    res.json(response);
  } catch (err) {
    console.error('Error retrieving API key for editing:', err);

    // Log the error
    await db.activity.create({
      action: 'api_key_edit_access_error',
      details: JSON.stringify({
        key: req.params.key,
        error: err.message
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'error',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   POST api/settings/api-keys
// @desc    Create or update an API key with enhanced security
// @access  Private (Admin only)
router.post('/api-keys', adminOnly, async (req, res) => {
  try {
    const { key, value, description, isSecret = true, expiresInDays, mailgunDomain } = req.body;

    if (!key) {
      return res.status(400).json({ error: 'Key name is required' });
    }

    if (!value || value.trim().length === 0) {
      return res.status(400).json({ error: 'API key value is required' });
    }

    // Calculate expiration date if provided
    let expiresAt = null;
    if (expiresInDays && expiresInDays > 0) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + parseInt(expiresInDays));
    }

    // Special handling for Mailgun - create unified configuration
    if (key === 'mailgun_api_key' || key === 'mailgun_config') {
      if (!mailgunDomain || mailgunDomain.trim().length === 0) {
        return res.status(400).json({ error: 'Mailgun domain is required' });
      }

      // Create unified Mailgun configuration
      const mailgunConfig = {
        api_key: value.trim(),
        domain: mailgunDomain.trim()
      };

      // Store as unified configuration
      await db.systemSetting.setValue('mailgun_config', JSON.stringify(mailgunConfig), 'api_key', 'Mailgun Email Service Configuration', true, expiresAt);

      // Enhanced logging for Mailgun
      await db.activity.create({
        action: 'mailgun_config_update',
        details: JSON.stringify({
          key: 'mailgun_config',
          hasExpiration: !!expiresAt,
          expiresAt: expiresAt,
          domain: mailgunDomain.trim(),
          description: 'Mailgun Email Service Configuration'
        }),
        userId: req.user.id,
        module: 'settings',
        severity: 'info',
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      // Store in session for UI state
      if (req.session) {
        if (!req.session.apiKeyStates) {
          req.session.apiKeyStates = {};
        }

        req.session.apiKeyStates['mailgun_config'] = {
          name: 'Mailgun Email Service Configuration',
          isActive: true,
          lastUpdated: new Date().toISOString(),
          hasExpiration: !!expiresAt,
          expiresAt: expiresAt
        };
      }

      return res.json({
        success: true,
        message: 'Mailgun configuration saved successfully',
        expiresAt: expiresAt,
        unified: true
      });
    }

    // Standard API key handling for non-Mailgun services
    await db.systemSetting.setValue(key, value, 'api_key', description, isSecret, expiresAt);

    // Enhanced logging with more details
    await db.activity.create({
      action: 'api_key_update',
      details: JSON.stringify({
        key,
        hasExpiration: !!expiresAt,
        expiresAt: expiresAt,
        isSecret,
        description: description || 'No description provided'
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // Store in session for UI state (instead of localStorage)
    if (req.session) {
      if (!req.session.apiKeyStates) {
        req.session.apiKeyStates = {};
      }

      req.session.apiKeyStates[key] = {
        name: description || key,
        isActive: true,
        lastUpdated: new Date().toISOString(),
        hasExpiration: !!expiresAt,
        expiresAt: expiresAt
      };
    } else {
      console.warn('Session not available for API key state storage');
    }

    res.json({
      success: true,
      message: `API key '${key}' has been saved`,
      expiresAt: expiresAt
    });
  } catch (err) {
    console.error('Error saving API key:', err);

    // Log the error
    await db.activity.create({
      action: 'api_key_update_failed',
      details: JSON.stringify({
        key: req.body.key,
        error: err.message
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'error',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(500).json({ error: 'Server Error' });
  }
});



// @route   DELETE api/settings/api-keys/:key
// @desc    Delete an API key
// @access  Private (Admin only)
router.delete('/api-keys/:key', adminOnly, async (req, res) => {
  try {
    const result = await db.systemSetting.destroy({
      where: { key: req.params.key }
    });

    if (result === 0) {
      return res.status(404).json({ error: 'API key not found' });
    }

    // Log this action
    await db.activity.create({
      action: 'api_key_delete',
      details: JSON.stringify({ key: req.params.key }),
      userId: req.user.id,
      module: 'settings',
      severity: 'warning',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({ success: true, message: `API key '${req.params.key}' has been deleted` });
  } catch (err) {
    console.error('Error deleting API key:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});



// @route   POST api/settings/migrate-mailgun
// @desc    Migrate separate Mailgun entries to unified configuration
// @access  Private (Admin only)
router.post('/migrate-mailgun', adminOnly, async (req, res) => {
  try {
    // Check if separate entries exist
    const apiKeySetting = await db.systemSetting.findByPk('mailgun_api_key');
    const domainSetting = await db.systemSetting.findByPk('mailgun_domain');

    if (!apiKeySetting && !domainSetting) {
      return res.json({
        success: true,
        message: 'No separate Mailgun entries found to migrate',
        migrated: false
      });
    }

    if (!apiKeySetting || !domainSetting) {
      return res.status(400).json({
        error: 'Incomplete Mailgun configuration found. Both API key and domain are required for migration.',
        hasApiKey: !!apiKeySetting,
        hasDomain: !!domainSetting
      });
    }

    // Create unified configuration
    const mailgunConfig = {
      api_key: apiKeySetting.value, // This will be decrypted automatically
      domain: domainSetting.value
    };

    // Store unified configuration
    await db.systemSetting.setValue(
      'mailgun_config',
      JSON.stringify(mailgunConfig),
      'api_key',
      'Mailgun Email Service Configuration',
      true, // isSecret
      apiKeySetting.expiresAt // Use API key expiration if set
    );

    // Remove old separate entries
    await db.systemSetting.destroy({ where: { key: 'mailgun_api_key' } });
    await db.systemSetting.destroy({ where: { key: 'mailgun_domain' } });

    // Log migration
    await db.activity.create({
      action: 'mailgun_migration_completed',
      details: JSON.stringify({
        from: ['mailgun_api_key', 'mailgun_domain'],
        to: 'mailgun_config',
        domain: mailgunConfig.domain
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Mailgun configuration migrated successfully to unified format',
      migrated: true,
      domain: mailgunConfig.domain
    });
  } catch (err) {
    console.error('Error migrating Mailgun configuration:', err);

    // Log the error
    await db.activity.create({
      action: 'mailgun_migration_failed',
      details: JSON.stringify({
        error: err.message
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'error',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(500).json({ error: 'Migration failed: ' + err.message });
  }
});

// @route   POST api/settings/api-keys/:key/extend
// @desc    Extend expiration date of an API key
// @access  Private (Admin only)
router.post('/api-keys/:key/extend', adminOnly, async (req, res) => {
  try {
    const { key } = req.params;
    const { extensionDays = 365 } = req.body;

    const setting = await db.systemSetting.findByPk(key);
    if (!setting) {
      return res.status(404).json({ error: 'API key not found' });
    }

    // Calculate new expiration date
    const newExpirationDate = new Date();
    newExpirationDate.setDate(newExpirationDate.getDate() + parseInt(extensionDays));

    // Update the setting
    setting.expiresAt = newExpirationDate;
    await setting.save();

    // Log this action
    await db.activity.create({
      action: 'api_key_extended',
      details: JSON.stringify({
        key,
        extensionDays: parseInt(extensionDays),
        newExpirationDate
      }),
      userId: req.user.id,
      module: 'settings',
      severity: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: `API key '${key}' expiration extended by ${extensionDays} days`,
      newExpirationDate
    });
  } catch (err) {
    console.error('Error extending API key:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// -- General System Settings --

// @route   GET api/settings
// @desc    Get all public settings
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const settings = await db.systemSetting.findAll({
      where: {
        isSecret: false
      }
    });

    // Format settings as key-value pairs
    const formattedSettings = {};
    settings.forEach(setting => {
      formattedSettings[setting.key] = setting.value;
    });

    res.json(formattedSettings);
  } catch (err) {
    console.error('Error retrieving settings:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// @route   GET api/settings/os
// @desc    Get all OS types
// @access  Private (Admin only)
router.get('/os', adminOnly, async (req, res) => {
  try {
    const osTypes = await db.os.findAll({
      order: [['id', 'ASC']]
    });
    res.json(osTypes);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/settings/os
// @desc    Create a new OS type
// @access  Private (Admin only)
router.post('/os', adminOnly, async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ msg: 'Name is required' });
    }

    const newOs = await db.os.create({ name });

    // Log action
    await db.logger.create({
      action: `creation of new OS entry (${newOs.id})`,
      user_id: req.user.id,
      action_date: new Date()
    });

    res.status(201).json(newOs);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   DELETE api/settings/os/:id
// @desc    Delete an OS type
// @access  Private (Admin only)
router.delete('/os/:id', adminOnly, async (req, res) => {
  try {
    const result = await db.os.destroy({
      where: { id: req.params.id }
    });

    if (result === 0) {
      return res.status(404).json({ msg: 'OS type not found' });
    }

    // Log action
    await db.logger.create({
      action: `deletion of OS entry (${req.params.id})`,
      user_id: req.user.id,
      action_date: new Date()
    });

    res.json({ msg: 'OS type deleted' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// Similar routes for status, info_type, info_structure, info_smoke, info_area
// I'll implement one more set as an example

// Note: GET /statuses endpoint moved to line 307 with regular auth access

// @route   POST api/settings/statuses
// @desc    Create a new status
// @access  Private (Admin only)
router.post('/statuses', adminOnly, async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ msg: 'Name is required' });
    }

    const newStatus = await db.status.create({ name });

    // Log action
    await db.logger.create({
      action: `creation of new status entry (${newStatus.id})`,
      user_id: req.user.id,
      action_date: new Date()
    });

    res.status(201).json(newStatus);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   DELETE api/settings/statuses/:id
// @desc    Delete a status
// @access  Private (Admin only)
router.delete('/statuses/:id', adminOnly, async (req, res) => {
  try {
    const result = await db.status.destroy({
      where: { id: req.params.id }
    });

    if (result === 0) {
      return res.status(404).json({ msg: 'Status not found' });
    }

    // Log action
    await db.logger.create({
      action: `deletion of status entry (${req.params.id})`,
      user_id: req.user.id,
      action_date: new Date()
    });

    res.json({ msg: 'Status deleted' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/settings/types
// @desc    Get all incident types
// @access  Private
router.get('/types', auth, async (req, res) => {
  try {
    const types = await db.type.findAll({
      order: [['name', 'ASC']]
    });
    res.json(types);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/settings/statuses
// @desc    Get all incident statuses
// @access  Private
router.get('/statuses', auth, async (req, res) => {
  try {
    const statuses = await db.status.findAll({
      order: [['name', 'ASC']]
    });
    res.json(statuses);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/settings/os
// @desc    Get all OS (operating system) entries
// @access  Private
router.get('/os', auth, async (req, res) => {
  try {
    const os = await db.sequelize.query(
      'SELECT * FROM os ORDER BY name ASC',
      { type: db.sequelize.QueryTypes.SELECT }
    );
    res.json(os);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/settings/infotypes
// @desc    Get all info types
// @access  Private
router.get('/infotypes', auth, async (req, res) => {
  try {
    // Get info_smoke, info_structure, info_type, info_area
    const infoSmoke = await db.sequelize.query(
      'SELECT * FROM info_smoke ORDER BY info ASC',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    const infoStructure = await db.sequelize.query(
      'SELECT * FROM info_structure ORDER BY info ASC',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    const infoType = await db.sequelize.query(
      'SELECT * FROM info_type ORDER BY info ASC',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    const infoArea = await db.sequelize.query(
      'SELECT * FROM info_area ORDER BY info ASC',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    res.json({
      smoke: infoSmoke,
      structure: infoStructure,
      type: infoType,
      area: infoArea
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/settings/stats/dashboard
// @desc    Get stats for dashboard
// @access  Private
router.get('/stats/dashboard', auth, async (req, res) => {
  try {
    // Get total active incidents
    const [activeIncidents] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM incident WHERE status_id = 1',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // Get fire incidents count
    const [fireIncidents] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM incident WHERE status_id = 1 AND os_id IN (SELECT id FROM os WHERE name LIKE "%Fire%")',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // Get water incidents count
    const [waterIncidents] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM incident WHERE status_id = 1 AND os_id IN (SELECT id FROM os WHERE name LIKE "%Water%" OR name LIKE "%Flood%")',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // Get total subscribers
    const [subscribers] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM subscribers',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // Get active subscribers
    const [activeSubscribers] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM subscribers WHERE status = 1',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // Get pending subscribers
    const [pendingSubscribers] = await db.sequelize.query(
      'SELECT COUNT(*) as total FROM subscribers WHERE status = 0',
      { type: db.sequelize.QueryTypes.SELECT }
    );

    res.json({
      totalActive: activeIncidents.total || 0,
      fireIncidents: fireIncidents.total || 0,
      waterIncidents: waterIncidents.total || 0,
      totalSubscribers: subscribers.total || 0,
      activeSubscribers: activeSubscribers.total || 0,
      pendingSubscribers: pendingSubscribers.total || 0
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/settings/test-email
// @desc    Test email delivery using configured providers
// @access  Private (Admin only)
router.post('/test-email', adminOnly, async (req, res) => {
  try {
    const { email, provider = 'auto' } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email address is required' });
    }

    // Load the necessary libraries
    const formData = require('form-data');
    const Mailgun = require('mailgun.js');
    const nodemailer = require('nodemailer');

    // Create test email content
    const emailContent = {
      from: `"FireAlerts911" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: email,
      subject: 'FireAlerts911 Test Email',
      text: 'This is a test email from your FireAlerts911 system.',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px;">
          <h2 style="color: #e53935;">FireAlerts911 Test Email</h2>
          <p style="font-size: 16px;"><strong>This is a test email from your FireAlerts911 system.</strong></p>
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          <p style="font-size: 14px; color: #777;">If you're receiving this email, your email delivery configuration is working correctly.</p>
          <p style="font-size: 12px; color: #999;">Time sent: ${new Date().toLocaleString()}</p>
        </div>
      `
    };

    let result = { success: false, error: 'No email provider configured' };

    // Try Mailgun if selected or auto mode
    if (provider === 'mailgun' || provider === 'auto') {
      try {
        // Try unified Mailgun configuration first
        let mailgunApiKey = null;
        let mailgunDomain = null;

        const mailgunConfig = await db.systemSetting.getValue('mailgun_config');
        if (mailgunConfig) {
          try {
            const config = JSON.parse(mailgunConfig);
            mailgunApiKey = config.api_key;
            mailgunDomain = config.domain;
          } catch (parseError) {
            console.error('Error parsing unified Mailgun configuration:', parseError);
          }
        }

        // Fallback to separate entries for backward compatibility
        if (!mailgunApiKey || !mailgunDomain) {
          mailgunApiKey = mailgunApiKey || await db.systemSetting.getValue('mailgun_api_key');
          mailgunDomain = mailgunDomain || await db.systemSetting.getValue('mailgun_domain');
        }

        if (mailgunApiKey && mailgunDomain) {
          // Initialize Mailgun client
          const mailgun = new Mailgun(formData);
          const mailgunClient = mailgun.client({ username: 'api', key: mailgunApiKey });

          // Send email
          const mgResult = await mailgunClient.messages.create(mailgunDomain, {
            from: emailContent.from,
            to: emailContent.to,
            subject: emailContent.subject,
            text: emailContent.text,
            html: emailContent.html
          });

          result = {
            success: true,
            provider: 'mailgun',
            messageId: mgResult.id,
            message: `Email sent successfully via Mailgun to ${email}`
          };

          // Log this action
          await db.activity.create({
            action: 'test_email_sent',
            details: JSON.stringify({ provider: 'mailgun', email }),
            userId: req.user.id,
            module: 'settings',
            severity: 'info'
          });

          return res.json(result);
        } else if (provider === 'mailgun') {
          result.error = 'Mailgun is not configured. Please set up your Mailgun API key and domain.';
        }
      } catch (error) {
        if (provider === 'mailgun') {
          result.error = `Mailgun error: ${error.message}`;
          return res.status(500).json(result);
        }
        // Otherwise continue to try SMTP in auto mode
      }
    }

    // Try SMTP if selected or as fallback in auto mode
    if (provider === 'smtp' || (provider === 'auto' && !result.success)) {
      try {
        const smtpHost = process.env.SMTP_HOST || await db.systemSetting.getValue('smtp_host');
        const smtpPort = process.env.SMTP_PORT || await db.systemSetting.getValue('smtp_port');
        const smtpUser = process.env.SMTP_USER || await db.systemSetting.getValue('smtp_user');
        const smtpPassword = process.env.SMTP_PASSWORD || await db.systemSetting.getValue('smtp_password');
        const smtpSecure = process.env.SMTP_SECURE === 'true' || await db.systemSetting.getValue('smtp_secure') === 'true';

        if (!smtpHost || !smtpPort || !smtpUser || !smtpPassword) {
          result.error = 'SMTP is not fully configured. Please check your SMTP settings.';
          return res.status(500).json(result);
        }

        // Initialize SMTP transporter
        const transporter = nodemailer.createTransport({
          host: smtpHost,
          port: smtpPort,
          secure: smtpSecure,
          auth: {
            user: smtpUser,
            pass: smtpPassword
          }
        });

        // Send email
        const smtpResult = await transporter.sendMail(emailContent);

        result = {
          success: true,
          provider: 'smtp',
          messageId: smtpResult.messageId,
          message: `Email sent successfully via SMTP to ${email}`
        };

        // Log this action
        await db.activity.create({
          action: 'test_email_sent',
          details: JSON.stringify({ provider: 'smtp', email }),
          userId: req.user.id,
          module: 'settings',
          severity: 'info'
        });

        return res.json(result);
      } catch (error) {
        result.error = `SMTP error: ${error.message}`;
        return res.status(500).json(result);
      }
    }

    // If we get here, no provider succeeded
    return res.status(500).json(result);
  } catch (err) {
    console.error('Error testing email delivery:', err);
    res.status(500).json({ error: 'Server Error', details: err.message });
  }
});

// @route   POST api/settings/test-sms
// @desc    Test SMS delivery using Twilio
// @access  Private (Admin only)
router.post('/test-sms', adminOnly, async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ error: 'Phone number is required' });
    }

    // Import SMS service
    const smsService = require('../services/smsService');

    // Test SMS delivery
    const result = await smsService.sendTestSms(phoneNumber);

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        messageId: result.details.messageId,
        provider: result.details.provider,
        cost: result.details.cost,
        status: result.details.status
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        message: result.message
      });
    }

  } catch (err) {
    console.error('Error testing SMS delivery:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error',
      details: err.message
    });
  }
});

// @route   GET api/settings/email-providers/status
// @desc    Get email provider configuration status
// @access  Private (Admin only)
router.get('/email-providers/status', adminOnly, async (req, res) => {
  try {
    // Try unified Mailgun configuration first
    let mailgunApiKey = null;
    let mailgunDomain = null;

    const mailgunConfig = await db.systemSetting.getValue('mailgun_config');
    if (mailgunConfig) {
      try {
        const config = JSON.parse(mailgunConfig);
        mailgunApiKey = config.api_key;
        mailgunDomain = config.domain;
      } catch (parseError) {
        console.error('Error parsing unified Mailgun configuration:', parseError);
      }
    }

    // Fallback to separate entries for backward compatibility
    if (!mailgunApiKey || !mailgunDomain) {
      mailgunApiKey = mailgunApiKey || await db.systemSetting.getValue('mailgun_api_key');
      mailgunDomain = mailgunDomain || await db.systemSetting.getValue('mailgun_domain');
    }

    // Check SMTP configuration
    const smtpHost = process.env.SMTP_HOST || await db.systemSetting.getValue('smtp_host');
    const smtpPort = process.env.SMTP_PORT || await db.systemSetting.getValue('smtp_port');
    const smtpUser = process.env.SMTP_USER || await db.systemSetting.getValue('smtp_user');
    const smtpPassword = process.env.SMTP_PASSWORD || await db.systemSetting.getValue('smtp_password');

    res.json({
      success: true,
      mailgun: {
        configured: !!(mailgunApiKey && mailgunDomain),
        available: !!(mailgunApiKey && mailgunDomain),
        domain: mailgunDomain || null
      },
      smtp: {
        configured: !!(smtpHost && smtpPort && smtpUser && smtpPassword),
        available: !!(smtpHost && smtpPort && smtpUser && smtpPassword),
        host: smtpHost || null,
        port: smtpPort || null
      }
    });

  } catch (err) {
    console.error('Error getting email provider status:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error',
      details: err.message
    });
  }
});

// @route   GET api/settings/sms-status
// @desc    Get SMS service configuration status
// @access  Private (Admin only)
router.get('/sms-status', adminOnly, async (req, res) => {
  try {
    const smsService = require('../services/smsService');
    const stats = smsService.getStats();

    res.json({
      success: true,
      data: {
        isConfigured: stats.isConfigured,
        provider: stats.provider,
        fromNumber: stats.fromNumber,
        deliveryStats: {
          sent: stats.sent,
          failed: stats.failed,
          lastSuccess: stats.lastSuccess,
          lastError: stats.lastError
        }
      }
    });

  } catch (err) {
    console.error('Error getting SMS status:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error',
      details: err.message
    });
  }
});

// @route   GET api/settings/health
// @desc    Get system health status
// @access  Public (basic health check)
router.get('/health', async (req, res) => {
  try {
    const healthStatus = {
      timestamp: new Date().toISOString(),
      status: 'operational',
      services: {}
    };

    // Check database connection
    try {
      await db.sequelize.authenticate();
      healthStatus.services.database = {
        status: 'operational',
        message: 'Database connection successful'
      };
    } catch (dbError) {
      console.error('Database health check failed:', dbError);
      healthStatus.services.database = {
        status: 'error',
        message: 'Database connection failed'
      };
      healthStatus.status = 'degraded';
    }

    // Check if we can query basic data
    try {
      const incidentCount = await db.incident.count();
      healthStatus.services.api = {
        status: 'operational',
        message: 'API functioning normally',
        data: { incidentCount }
      };
    } catch (apiError) {
      console.error('API health check failed:', apiError);
      healthStatus.services.api = {
        status: 'error',
        message: 'API queries failing'
      };
      healthStatus.status = 'error';
    }

    // Check notification system configuration
    try {
      const smsService = require('../services/smsService');
      const smsStats = smsService.getStats();

      healthStatus.services.notifications = {
        status: smsStats.isConfigured ? 'operational' : 'warning',
        message: smsStats.isConfigured ? 'SMS service configured' : 'SMS service not configured',
        data: {
          provider: smsStats.provider,
          configured: smsStats.isConfigured
        }
      };
    } catch (notificationError) {
      console.error('Notification health check failed:', notificationError);
      healthStatus.services.notifications = {
        status: 'unknown',
        message: 'Unable to check notification service'
      };
    }

    res.json({
      success: true,
      data: healthStatus
    });

  } catch (err) {
    console.error('Health check error:', err);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      details: err.message
    });
  }
});

module.exports = router;
