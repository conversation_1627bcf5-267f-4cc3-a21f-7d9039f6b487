/**
 * Role-based access control middleware
 * This middleware checks if the user has one of the allowed roles
 */
const { ROLES } = require('../models/permissions');

/**
 * Middleware that checks if the user has one of the specified roles
 * @param {Array|String} allowedRoles - Single role or array of roles that are permitted
 * @returns {Function} Express middleware function
 */
module.exports = function(allowedRoles) {
  // Convert single role to array for consistent handling
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  
  return function(req, res, next) {
    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({ msg: 'No user authentication found' });
    }
    
    // Check if user's role is in the allowed roles
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        msg: `Access denied. Required role: ${roles.join(' or ')}`,
        requiredRoles: roles,
        yourRole: req.user.role
      });
    }
    
    // User has the required role, proceed
    next();
  };
};

// Export convenient pre-configured middleware for common roles
module.exports.adminOnly = module.exports(ROLES.ADMIN);
module.exports.dispatcherOrAdmin = module.exports([ROLES.DISPATCHER, ROLES.ADMIN]);
module.exports.allAuthenticated = module.exports([ROLES.SUBSCRIBER, ROLES.DISPATCHER, ROLES.ADMIN]);