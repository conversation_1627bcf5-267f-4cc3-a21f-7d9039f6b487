/**
 * Notification Scheduler
 * 
 * This module sets up scheduled jobs to process notifications and other
 * recurring tasks for the FireAlerts911 system.
 */

const cron = require('node-cron');
const notificationWorker = require('./notificationWorker');

// Log initialization
console.log('Initializing FireAlerts911 task scheduler...');

// Process notifications every minute
// In a production environment, you might want to adjust this frequency
const notificationSchedule = process.env.NOTIFICATION_CRON || '* * * * *';

// Schedule notification processing job
cron.schedule(notificationSchedule, async () => {
    console.log(`[${new Date().toISOString()}] Running scheduled notification processing task`);
    try {
        await notificationWorker.processNotifications();
    } catch (error) {
        console.error('Error in notification scheduler:', error);
    }
});

// Add additional scheduled tasks as needed
// Examples:
// - Daily cleanup of old notifications
// - Weekly summary reports
// - System health checks

console.log(`FireAlerts911 scheduler initialized with notification processing schedule: ${notificationSchedule}`);

module.exports = {
    // Export this object to ensure the scheduler can be required from other modules
    isInitialized: true
};
