/**
 * Migration: Enhance System Settings Security
 * 
 * This migration adds security enhancements to the system_settings table:
 * - expires_at: For API key expiration tracking
 * - last_accessed_at: For audit trail of key access
 * - access_count: For usage analytics
 * 
 * Also includes data migration for existing API keys to encrypted format
 */

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🔧 Adding security enhancement fields to system_settings table...');
      
      // Add new columns for security tracking
      await queryInterface.addColumn('system_settings', 'expires_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Expiration date for API keys and sensitive settings'
      }, { transaction });

      await queryInterface.addColumn('system_settings', 'last_accessed_at', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Last time this setting was accessed'
      }, { transaction });

      await queryInterface.addColumn('system_settings', 'access_count', {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false,
        comment: 'Number of times this setting has been accessed'
      }, { transaction });

      // Add indexes for performance
      await queryInterface.addIndex('system_settings', ['expires_at'], {
        name: 'idx_system_settings_expires_at',
        transaction
      });

      await queryInterface.addIndex('system_settings', ['last_accessed_at'], {
        name: 'idx_system_settings_last_accessed',
        transaction
      });

      await queryInterface.addIndex('system_settings', ['category', 'is_secret'], {
        name: 'idx_system_settings_category_secret',
        transaction
      });

      console.log('✅ Security enhancement fields added successfully');
      
      // Set default expiration for existing API keys (1 year from now)
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      await queryInterface.bulkUpdate('system_settings', {
        expires_at: oneYearFromNow,
        access_count: 0
      }, {
        category: 'api_key',
        is_secret: true
      }, { transaction });

      console.log('✅ Set default expiration dates for existing API keys');

      await transaction.commit();
      console.log('🎉 Migration completed successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🔄 Reverting system_settings security enhancements...');
      
      // Remove indexes
      await queryInterface.removeIndex('system_settings', 'idx_system_settings_expires_at', { transaction });
      await queryInterface.removeIndex('system_settings', 'idx_system_settings_last_accessed', { transaction });
      await queryInterface.removeIndex('system_settings', 'idx_system_settings_category_secret', { transaction });
      
      // Remove columns
      await queryInterface.removeColumn('system_settings', 'expires_at', { transaction });
      await queryInterface.removeColumn('system_settings', 'last_accessed_at', { transaction });
      await queryInterface.removeColumn('system_settings', 'access_count', { transaction });
      
      await transaction.commit();
      console.log('✅ Migration reverted successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Migration rollback failed:', error);
      throw error;
    }
  }
};
