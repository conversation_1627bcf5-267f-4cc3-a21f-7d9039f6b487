# FireAlerts911 Deployment Guide

This comprehensive guide covers all deployment options for the FireAlerts911 application, including Docker development setup and production deployment to Render.com.

## 📋 **Table of Contents**

1. [Deployment Options Overview](#deployment-options-overview)
2. [Docker Development Setup](#docker-development-setup)
3. [Render.com Production Deployment](#rendercom-production-deployment)
4. [Database Setup](#database-setup)
5. [Environment Configuration](#environment-configuration)
6. [Production Optimization](#production-optimization)
7. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)

---

## 🚀 **Deployment Options Overview**

### **1. Render.com Deployment (Recommended for Production)**
- **Advantages**: No Docker required, automatic SSL, easy scaling, built-in monitoring
- **Best For**: Production deployments, teams without Docker expertise
- **Preparation Script**: `npm run prepare-render`

### **2. Docker Deployment (Development/Self-hosted)**
- **Quick Start**: `docker-compose up -d`
- **Advantages**: Consistent environment, easy local development, full control
- **Best For**: Development, self-hosted deployments, testing

---

## 🐳 **Docker Development Setup**

### **Prerequisites**
- [Docker](https://www.docker.com/products/docker-desktop/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

### **Quick Start**

#### **On Linux/macOS:**
```bash
# Start the containers
./start.sh

# View logs
docker-compose logs -f

# Stop the containers
./stop.sh

# Rebuild containers (if you make code changes)
./rebuild.sh

# Reset the database (caution: deletes all data)
./reset-db.sh
```

#### **On Windows:**
```bash
# Start the containers
start.bat

# View logs
docker-compose logs -f

# Stop the containers
docker-compose down

# Rebuild containers (if you make code changes)
docker-compose build --no-cache
docker-compose up -d

# Reset the database (caution: deletes all data)
docker-compose down
docker volume rm docker_mysql_data
docker-compose up -d
```

### **System Components**
The Docker setup includes:
1. **Node.js Application Container** - Runs the FireAlerts911 application
2. **MySQL Database Container** - Stores all system data
3. **Docker Network** - Provides communication between containers
4. **Persistent Volume** - Keeps the database data between container restarts

### **Default Credentials**
- **Admin User**: username=`admin`, password=`admin123`
- **Dispatcher User**: username=`dispatcher`, password=`dispatch123`

### **Configuration**
You can modify environment variables in the `.env` file or directly in `docker-compose.yml` to customize the application.

### **Docker Troubleshooting**
If you encounter issues:

1. **Check logs**: `docker-compose logs -f`
2. **Restart containers**: `docker-compose restart`
3. **Rebuild from scratch**:
   ```bash
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```
4. **Database connection issues**:
   ```bash
   docker exec -it firealerts911-db mysql -ufireuser -pfirepass -e "SHOW DATABASES;"
   ```

---

## ☁️ **Render.com Production Deployment**

### **Pre-deployment Preparation**

#### **Repository Structure Overview**
FireAlerts911 consists of:
- **Backend**: `firealerts-node/` - Node.js/Express API server
- **Frontend**: Root directory - Static HTML/CSS/JS files
- **Database**: MySQL with production-ready seeding

#### **Required Environment Variables**
Create a `.env` file with these essential variables:

```env
# Database Configuration
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=firealerts911
DB_PORT=3306

# Application Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=your-secure-jwt-secret-64-chars-minimum

# API Keys (configure after deployment)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_FROM_NUMBER=
GOOGLE_MAPS_API_KEY=
MAILGUN_API_KEY=
MAILGUN_DOMAIN=
ESTATED_API_KEY=

# Optional Configuration
DB_LOGGING=false
DISABLE_NOTIFICATION_WORKER=false
```

#### **Security Considerations**
1. **Generate Secure JWT Secret**: `node scripts/generate-jwt-secret.js`
2. **Environment Validation**: The production seeding script validates environment settings
3. **API Key Management**: All API keys start as empty placeholders and must be configured post-deployment

### **Render.com Service Configuration**

#### **Backend Service Setup (Node.js)**
1. **Create Web Service**:
   - Go to [render.com](https://render.com) dashboard
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Service Configuration**:
   ```yaml
   Name: firealerts911-api
   Environment: Node
   Region: Choose closest to your users
   Branch: main (or your production branch)
   Root Directory: firealerts-node
   ```

3. **Build & Start Commands**:
   ```bash
   # Build Command
   npm install

   # Start Command
   npm start
   ```

4. **Advanced Settings**:
   ```yaml
   Auto-Deploy: Yes
   Health Check Path: /api/health (if implemented)
   ```

#### **Frontend Service Setup (Static Site)**
1. **Create Static Site**:
   - Click "New +" → "Static Site"
   - Connect same GitHub repository

2. **Static Site Configuration**:
   ```yaml
   Name: firealerts911-frontend
   Branch: main
   Root Directory: . (root)
   Build Command: # Leave empty for static files
   Publish Directory: . (root)
   ```

#### **Alternative: Single Service Deployment**
You can serve both frontend and backend from the Node.js service by modifying `server.js` to serve static files:

```javascript
// Add this to firealerts-node/server.js
app.use(express.static(path.join(__dirname, '../')));

// Catch-all handler for frontend routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../index.html'));
});
```

### **Environment Variables Configuration**
In Render Dashboard, go to your service → "Environment" and add each variable individually:

```env
# Required Database Variables
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=firealerts911
DB_PORT=3306

# Application Variables
NODE_ENV=production
PORT=5000
JWT_SECRET=your-64-character-secure-secret

# API Keys (add after deployment)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_FROM_NUMBER=
GOOGLE_MAPS_API_KEY=
MAILGUN_API_KEY=
MAILGUN_DOMAIN=
ESTATED_API_KEY=

# Optional Variables
DB_LOGGING=false
DISABLE_NOTIFICATION_WORKER=false
```

**Security Note**: Mark sensitive variables as "Secret" in Render and never commit API keys to repository.

---

## 🗄️ **Database Setup**

### **Recommended MySQL Hosting Solutions**

#### **Option 1: PlanetScale (Recommended)**
- **Pros**: Serverless, automatic scaling, built-in branching
- **Cons**: Limited free tier
- **Setup**:
  1. Create account at [planetscale.com](https://planetscale.com)
  2. Create new database: `firealerts911`
  3. Get connection string from dashboard
  4. Use format: `mysql://user:password@host:port/database?ssl={"rejectUnauthorized":true}`

#### **Option 2: Railway**
- **Pros**: Simple setup, good free tier
- **Cons**: Limited storage on free tier
- **Setup**:
  1. Create account at [railway.app](https://railway.app)
  2. Create MySQL service
  3. Note connection details from Variables tab

#### **Option 3: AWS RDS**
- **Pros**: Enterprise-grade, highly configurable
- **Cons**: More complex setup, costs
- **Setup**:
  1. Create RDS MySQL instance
  2. Configure security groups for Render IP ranges
  3. Use connection string format

#### **Option 4: Google Cloud SQL**
- **Pros**: Reliable, good integration options
- **Cons**: Requires Google Cloud account
- **Setup**:
  1. Create Cloud SQL MySQL instance
  2. Configure authorized networks
  3. Create database and user

### **Database Connection String Formats**

```env
# PlanetScale
DB_HOST=aws.connect.psdb.cloud
DB_USER=your-username
DB_PASSWORD=your-password
DB_NAME=firealerts911

# Railway
DB_HOST=containers-us-west-xxx.railway.app
DB_USER=root
DB_PASSWORD=generated-password
DB_NAME=railway

# Standard MySQL
DB_HOST=your-mysql-host.com
DB_USER=firealerts_user
DB_PASSWORD=secure-password
DB_NAME=firealerts911
```

---

## 🚀 **Production Deployment Workflow**

### **Step 1: Repository Preparation**
1. **Ensure Clean Repository**:
   ```bash
   # Remove Docker-specific files from deployment (optional)
   echo "docker-compose.yml" >> .gitignore
   echo "Dockerfile" >> .gitignore
   echo ".env" >> .gitignore
   ```

2. **Verify Package.json**:
   ```json
   {
     "scripts": {
       "start": "node server.js",
       "seed-production": "node scripts/seed-production.js"
     },
     "engines": {
       "node": ">=16.0.0"
     }
   }
   ```

### **Step 2: Database Initialization**
1. **Create Database Schema**:
   - Use your chosen MySQL provider's console
   - Run the schema from `docker/mysql-init/01-schema.sql`
   - Or let the application create tables automatically

2. **Production Seeding Strategy**:
   ```bash
   # After first deployment, run via Render shell:
   npm run seed-production
   ```

### **Step 3: Render Service Deployment**
1. **Deploy Backend**:
   - Configure Node.js service as described above
   - Set all environment variables
   - Deploy and verify startup logs

2. **Deploy Frontend** (if separate):
   - Configure static site service
   - Update API endpoints in frontend code if needed

### **Step 4: Domain Configuration**
1. **Custom Domain Setup**:
   - Go to service → "Settings" → "Custom Domains"
   - Add your domain (e.g., `firealerts911.com`)
   - Configure DNS CNAME record

2. **SSL Configuration**:
   - Render automatically provides SSL certificates
   - Verify HTTPS is working

---

## 📋 **Post-deployment Tasks**

### **Step 1: Database Seeding**
1. **Access Render Shell**:
   - Go to your service dashboard
   - Click "Shell" tab
   - Run production seeding:
   ```bash
   npm run seed-production
   ```

2. **Verify Seeding**:
   ```bash
   npm run validate-production-seeding
   ```

### **Step 2: Admin Account Security**
1. **Login to Application**:
   - Navigate to your deployed URL
   - Login with default credentials:
     - Username: `admin`
     - Password: `FireAdmin2025!`

2. **Change Admin Password**:
   - Go to Account settings
   - Update password immediately
   - Update email address

### **Step 3: API Key Configuration**
Configure these API keys in the admin panel:

1. **Twilio (SMS Notifications)**:
   - Account SID
   - Auth Token
   - From Phone Number

2. **Google Maps (Geocoding)**:
   - API Key with Geocoding API enabled

3. **Mailgun (Email Notifications)**:
   - API Key
   - Domain name

4. **Estated (Property Information)**:
   - API Key for property data

### **Step 4: System Validation**
1. **Test Core Functionality**:
   - Create test incident
   - Verify location dropdowns work
   - Test user management
   - Verify notifications (if configured)

2. **Performance Check**:
   - Monitor response times
   - Check database connection
   - Verify all pages load correctly

---

## 📊 **Production Optimization**

### **Production Seeding Configuration**
- **New Script**: `npm run seed-production`
- **Features**: Environment validation, no demo data, comprehensive logging
- **Validation**: `npm run validate-production-seeding`

### **What Gets Seeded in Production**
✅ **Included**:
- Admin user account (admin / FireAdmin2025!)
- Complete US states and counties (~3,100+ with proper naming)
- Incident types (fire, water, general)
- System statuses and company types
- Essential system settings

❌ **Excluded**:
- Demo incidents
- Test companies/subscribers
- Mock user accounts
- Sample data

### **Health Check Endpoints**
- `/health` - Basic application health
- `/api/health` - Database connectivity check
- Automatic monitoring and alerting

### **Build Optimization**
- Optimized package.json for Render
- Health check paths configured
- Static file serving for single-service deployment

---

## 🔧 **Monitoring and Troubleshooting**

### **Common Deployment Issues**

#### **Database Connection Errors**
```bash
# Error: connect ECONNREFUSED
# Solution: Check database host and credentials
```

**Fixes**:
1. Verify DB_HOST, DB_USER, DB_PASSWORD in environment
2. Check database server is running
3. Verify firewall/security group settings
4. Test connection from Render shell: `npm run wait-for-db`

#### **Build Failures**
```bash
# Error: npm install failed
# Solution: Check package.json and Node version
```

**Fixes**:
1. Verify Node.js version compatibility
2. Check for missing dependencies
3. Clear npm cache if needed
4. Review build logs in Render dashboard

#### **Environment Variable Issues**
```bash
# Error: JWT_SECRET is required
# Solution: Set all required environment variables
```

**Fixes**:
1. Verify all required variables are set
2. Check for typos in variable names
3. Ensure sensitive variables are marked as secrets
4. Restart service after adding variables

### **Performance Optimization**
1. **Database Optimization**:
   - Use connection pooling (already configured)
   - Monitor slow queries
   - Consider read replicas for high traffic

2. **Application Optimization**:
   - Enable gzip compression
   - Implement caching where appropriate
   - Monitor memory usage

3. **Render Optimization**:
   - Choose appropriate service plan
   - Monitor service metrics
   - Consider upgrading for better performance

### **Monitoring and Logs**
1. **Access Logs**:
   - Render Dashboard → Service → "Logs" tab
   - Real-time log streaming available

2. **Health Monitoring**:
   - Set up health check endpoints
   - Monitor service uptime
   - Configure alerts for downtime

3. **Database Monitoring**:
   - Monitor connection counts
   - Track query performance
   - Set up alerts for issues

---

## 🎯 **Production Checklist**

### **Pre-deployment**
- [ ] Database provider selected and configured
- [ ] Environment variables prepared
- [ ] Repository connected to deployment platform
- [ ] Build and start commands configured

### **Post-deployment**
- [ ] Production seeding completed
- [ ] Admin password changed
- [ ] API keys configured
- [ ] Core functionality tested
- [ ] Monitoring set up

### **Available Scripts**

#### **Production Scripts**
- `npm run seed-production` - Production-safe database seeding
- `npm run validate-production-seeding` - Verify production seeding
- `npm run prepare-render` - Prepare for Render.com deployment

#### **Development Scripts**
- `npm run seed` - Complete development seeding
- `npm run seed-core` - Essential data only
- `npm run seed-complete-locations` - Full US location data

#### **Utility Scripts**
- `npm run generate-jwt-secret` - Generate secure JWT secret
- `npm run validate-security` - Production security validation
- `npm run wait-for-db` - Database connectivity test

---

## 🔗 **Useful Links**

- **Render.com**: https://render.com
- **PlanetScale**: https://planetscale.com (Recommended database)
- **Railway**: https://railway.app (Alternative database)
- **Twilio**: https://twilio.com (SMS notifications)
- **Google Maps API**: https://developers.google.com/maps
- **Mailgun**: https://mailgun.com (Email notifications)

---

**The FireAlerts911 application is now optimized for production deployment with comprehensive documentation, automated seeding, and platform-specific configurations for both Docker and cloud hosting platforms.**
