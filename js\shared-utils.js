/**
 * Shared Utilities for FireAlerts911
 * Common functions used across multiple pages to reduce code duplication
 */

(function() {
    'use strict';

    // Create global namespace
    window.FireAlertsUtils = window.FireAlertsUtils || {};

    /**
     * Common notification function used across multiple pages
     * @param {string} message - The message to display
     * @param {string} type - Type of notification (success, error, warning, info)
     * @param {number} duration - How long to show the notification (default: 4000ms)
     */
    window.FireAlertsUtils.showNotification = function(message, type = 'info', duration = 4000) {
        console.log(`${type.toUpperCase()}: ${message}`);

        // Remove any existing notifications to prevent duplicates
        const existingNotifications = document.querySelectorAll('.notification, .view-incident-notification');
        existingNotifications.forEach(notification => {
            if (notification.textContent === message) {
                return; // Don't show duplicate
            }
        });

        // Create notification element
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.className = 'notification';
            document.body.appendChild(notification);
        }

        // Set notification content and type
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;

        // Show notification
        notification.style.display = 'flex';
        notification.style.opacity = '1';

        // Auto-hide after duration
        setTimeout(() => {
            if (notification) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification) {
                        notification.style.display = 'none';
                    }
                }, 300);
            }
        }, duration);
    };

    /**
     * Get appropriate icon for notification type
     * @param {string} type - Notification type
     * @returns {string} Font Awesome icon class
     */
    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Common sidebar rendering function
     * @param {string} activePage - The currently active page
     */
    window.FireAlertsUtils.renderRoleBasedSidebar = function(activePage = '') {
        const sidebar = document.querySelector('.sidebar .nav-menu');
        if (!sidebar) {
            console.warn('Sidebar menu container not found.');
            return;
        }

        // Get user role from storage
        let userRole = 'user'; // default
        try {
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || '{}');
            userRole = (userInfo.role || 'user').toLowerCase();
        } catch (e) {
            console.error('Error reading user role:', e);
        }

        // Base navigation items
        const baseNavItems = [
            { href: 'dashboard.html', icon: 'tachometer-alt', text: 'Dashboard', page: 'dashboard' },
            { href: 'incidents.html', icon: 'bell', text: 'Incidents', page: 'incidents' },
            { href: 'map.html', icon: 'map-marked-alt', text: 'Map View', page: 'map' },
            { href: 'subscribers.html', icon: 'users', text: 'Subscribers', page: 'subscribers' },
            { href: 'account.html', icon: 'user-circle', text: 'My Account', page: 'account' }
        ];

        // Role-specific items
        const roleSpecificItems = {
            admin: [
                { href: 'admin-panel.html', icon: 'cog', text: 'Admin Panel', page: 'admin-panel' }
            ],
            company_admin: [
                // Company admins have no additional sidebar items
            ]
        };

        // Combine navigation items based on role
        let navItems = [...baseNavItems];
        if (roleSpecificItems[userRole]) {
            // Insert role-specific items before "My Account" (last item)
            navItems.splice(-1, 0, ...roleSpecificItems[userRole]);
        }

        // Generate HTML
        const navHTML = navItems.map(item => {
            // Enhanced active page detection
            const currentPath = window.location.pathname;
            const currentPage = currentPath.split('/').pop() || 'index.html';

            const isActive = activePage === item.page ||
                           currentPage === item.href ||
                           (currentPage === 'dashboard.html' && item.page === 'dashboard') ||
                           (currentPage === 'index.html' && item.page === 'dashboard') ||
                           (currentPage === '' && item.page === 'dashboard') ||
                           currentPath.includes(item.href);

            return `
                <a href="${item.href}" class="nav-item ${isActive ? 'active' : ''}">
                    <span class="nav-indicator"></span>
                    <i class="fas fa-${item.icon}"></i>
                    <span>${item.text}</span>
                </a>
            `;
        }).join('');

        sidebar.innerHTML = navHTML;
        console.log(`Sidebar rendered for role: ${userRole}, activePage: ${activePage}`);
        console.log('Navigation items:', navItems.map(item => `${item.text} (${item.page})`).join(', '));

        // Initialize avatars after sidebar is rendered
        setTimeout(() => {
            this.initializeAvatars();
        }, 100);
    };

    /**
     * Initialize avatars on page load
     */
    window.FireAlertsUtils.initializeAvatars = function() {
        try {
            // Get user info from storage
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || '{}');

            if (userInfo && (userInfo.firstName || userInfo.lastName)) {
                // Use avatar utilities to update user avatar
                if (this.avatar && this.avatar.updateUserAvatar) {
                    this.avatar.updateUserAvatar(userInfo);
                }
            } else {
                // Check if there's a stored avatar URL and apply it
                const storedAvatarUrl = this.avatar ? this.avatar.getStoredAvatarUrl() : null;
                if (storedAvatarUrl) {
                    // Update user-avatar-icon elements with stored avatar
                    const userAvatarIcons = document.querySelectorAll('.user-avatar-icon');
                    userAvatarIcons.forEach(icon => {
                        icon.innerHTML = `<img src="${storedAvatarUrl}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                    });

                    // Update other avatar elements
                    const topNavAvatar = document.getElementById('topNavAvatar');
                    if (topNavAvatar) {
                        topNavAvatar.src = storedAvatarUrl;
                    }

                    const avatarPreview = document.getElementById('avatarPreview');
                    if (avatarPreview) {
                        if (avatarPreview.tagName === 'IMG') {
                            avatarPreview.src = storedAvatarUrl;
                        } else if (avatarPreview.classList.contains('user-avatar-icon')) {
                            // Handle div-based avatar preview (account page)
                            avatarPreview.innerHTML = `<img src="${storedAvatarUrl}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                        }
                    }
                }
            }
        } catch (e) {
            console.warn('Error initializing avatars:', e);
        }
    };

    /**
     * Common form validation utilities
     */
    window.FireAlertsUtils.validation = {
        /**
         * Validate email format
         * @param {string} email - Email to validate
         * @returns {boolean} True if valid
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        /**
         * Validate phone number format
         * @param {string} phone - Phone number to validate
         * @returns {boolean} True if valid
         */
        isValidPhone: function(phone) {
            const phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
            return phoneRegex.test(phone);
        },

        /**
         * Validate required fields in a form
         * @param {HTMLFormElement} form - Form to validate
         * @returns {Object} Validation result with isValid and errors
         */
        validateRequiredFields: function(form) {
            const errors = [];
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    errors.push(`${field.name || field.id} is required`);
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            });

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }
    };

    /**
     * Common API error handling
     * @param {Error} error - The error object
     * @param {string} context - Context where the error occurred
     */
    window.FireAlertsUtils.handleApiError = function(error, context = 'API call') {
        console.error(`${context} error:`, error);

        let message = 'An unexpected error occurred';

        if (error.response) {
            // Server responded with error status
            message = error.response.data?.message || `Server error: ${error.response.status}`;
        } else if (error.request) {
            // Network error
            message = 'Network error - please check your connection';
        } else if (error.message) {
            // Other error
            message = error.message;
        }

        this.showNotification(message, 'error');
        return message;
    };

    /**
     * Common loading state management
     */
    window.FireAlertsUtils.loading = {
        /**
         * Show loading overlay
         * @param {string} message - Loading message
         */
        show: function(message = 'Loading...') {
            let overlay = document.getElementById('loadingOverlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <div id="loadingMessage">${message}</div>
                    </div>
                `;
                document.body.appendChild(overlay);
            }

            const messageEl = overlay.querySelector('#loadingMessage');
            if (messageEl) {
                messageEl.textContent = message;
            }

            overlay.style.display = 'flex';
        },

        /**
         * Hide loading overlay
         */
        hide: function() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }
    };

    /**
     * Avatar management utilities
     */
    window.FireAlertsUtils.avatar = {
        /**
         * Get default avatar SVG for a user
         * @param {string} initials - User initials (optional)
         * @param {string} color - Background color (optional)
         * @returns {string} SVG data URL
         */
        getDefaultAvatar: function(initials = '', color = '#6366F1') {
            const initial = initials.charAt(0).toUpperCase() || 'U';
            const svg = `
                <svg width="150" height="150" viewBox="0 0 150 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="75" cy="75" r="75" fill="${color}"/>
                    <text x="75" y="85" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="white" text-anchor="middle">${initial}</text>
                </svg>
            `;
            return `data:image/svg+xml;base64,${btoa(svg)}`;
        },

        /**
         * Get small avatar for top navigation
         * @param {string} initials - User initials (optional)
         * @param {string} color - Background color (optional)
         * @returns {string} SVG data URL
         */
        getSmallAvatar: function(initials = '', color = '#6366F1') {
            const initial = initials.charAt(0).toUpperCase() || 'U';
            const svg = `
                <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="18" cy="18" r="18" fill="${color}"/>
                    <text x="18" y="24" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">${initial}</text>
                </svg>
            `;
            return `data:image/svg+xml;base64,${btoa(svg)}`;
        },

        /**
         * Get stored avatar URL from localStorage
         * @returns {string|null} Stored avatar URL or null
         */
        getStoredAvatarUrl: function() {
            try {
                return localStorage.getItem('userAvatarUrl');
            } catch (e) {
                console.warn('Could not access localStorage for avatar URL');
                return null;
            }
        },

        /**
         * Store avatar URL in localStorage
         * @param {string} avatarUrl - Avatar URL to store
         */
        setStoredAvatarUrl: function(avatarUrl) {
            try {
                if (avatarUrl) {
                    localStorage.setItem('userAvatarUrl', avatarUrl);
                } else {
                    localStorage.removeItem('userAvatarUrl');
                }
            } catch (e) {
                console.warn('Could not store avatar URL in localStorage');
            }
        },

        /**
         * Update user avatar throughout the application
         * @param {Object} user - User object with firstName, lastName, avatarUrl
         */
        updateUserAvatar: function(user) {
            if (!user) return;

            const initials = `${user.firstName || ''}${user.lastName || ''}`.trim();
            const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#06B6D4'];
            const colorIndex = (user.id || 0) % colors.length;
            const color = colors[colorIndex];

            // Check for custom avatar URL (from user data or localStorage)
            const customAvatarUrl = user.avatarUrl || this.getStoredAvatarUrl();

            // Determine which avatar to use
            const smallAvatarSrc = customAvatarUrl || this.getSmallAvatar(initials, color);
            const largeAvatarSrc = customAvatarUrl || this.getDefaultAvatar(initials, color);

            // Update top navigation avatar
            const topNavAvatar = document.getElementById('topNavAvatar');
            if (topNavAvatar) {
                topNavAvatar.src = smallAvatarSrc;
                topNavAvatar.alt = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User Avatar';
            }

            // Update profile page avatar (handle both img and div elements)
            const avatarPreview = document.getElementById('avatarPreview');
            if (avatarPreview) {
                if (avatarPreview.tagName === 'IMG') {
                    avatarPreview.src = largeAvatarSrc;
                    avatarPreview.alt = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User Avatar';
                } else if (avatarPreview.classList.contains('user-avatar-icon')) {
                    // Handle div-based avatar preview (account page)
                    if (customAvatarUrl) {
                        avatarPreview.innerHTML = `<img src="${customAvatarUrl}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                    } else {
                        avatarPreview.innerHTML = '<i class="fas fa-user"></i>';
                    }
                }
            }

            // Update user name display
            const userNameDisplay = document.querySelector('.user-info span');
            if (userNameDisplay) {
                userNameDisplay.textContent = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User';
            }

            // Store avatar URL if provided
            if (user.avatarUrl) {
                this.setStoredAvatarUrl(user.avatarUrl);
            }
        },

        /**
         * Update avatar with uploaded image
         * @param {string} imageDataUrl - Data URL of uploaded image
         */
        updateAvatarWithUpload: function(imageDataUrl) {
            if (!imageDataUrl) return;

            // Store the uploaded avatar
            this.setStoredAvatarUrl(imageDataUrl);

            // Update all avatar displays
            const topNavAvatar = document.getElementById('topNavAvatar');
            if (topNavAvatar) {
                topNavAvatar.src = imageDataUrl;
            }

            const avatarPreview = document.getElementById('avatarPreview');
            if (avatarPreview) {
                if (avatarPreview.tagName === 'IMG') {
                    avatarPreview.src = imageDataUrl;
                } else if (avatarPreview.classList.contains('user-avatar-icon')) {
                    // Handle div-based avatar preview (account page)
                    avatarPreview.innerHTML = `<img src="${imageDataUrl}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
                }
            }

            // Update any user-avatar-icon elements to show the uploaded image
            const userAvatarIcons = document.querySelectorAll('.user-avatar-icon');
            userAvatarIcons.forEach(icon => {
                // Replace the icon with an image
                icon.innerHTML = `<img src="${imageDataUrl}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
            });
        }
    };

    /**
     * Common date/time formatting utilities
     */
    window.FireAlertsUtils.formatters = {
        /**
         * Format date for display
         * @param {string|Date} date - Date to format
         * @returns {string} Formatted date string
         */
        formatDate: function(date) {
            if (!date) return 'N/A';

            const d = new Date(date);
            if (isNaN(d.getTime())) return 'Invalid Date';

            return d.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        /**
         * Format relative time (e.g., "2 hours ago")
         * @param {string|Date} date - Date to format
         * @returns {string} Relative time string
         */
        formatRelativeTime: function(date) {
            if (!date) return 'N/A';

            const d = new Date(date);
            if (isNaN(d.getTime())) return 'Invalid Date';

            const now = new Date();
            const diffMs = now - d;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
            if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
            if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;

            return this.formatDate(date);
        }
    };

    console.log('FireAlerts Shared Utilities loaded');
})();
