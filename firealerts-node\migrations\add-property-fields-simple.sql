-- Simple migration to add property owner and Estated API fields to incident_details table
USE firealerts911;

-- Add secondary_owner column
ALTER TABLE incident_details ADD COLUMN secondary_owner VARCHAR(255) COMMENT 'Secondary property owner name';

-- Add owner_address column  
ALTER TABLE incident_details ADD COLUMN owner_address TEXT COMMENT 'Property owner mailing address';

-- Add owner_phone column
ALTER TABLE incident_details ADD COLUMN owner_phone VARCHAR(20) COMMENT 'Property owner phone number';

-- Add owner_email column
ALTER TABLE incident_details ADD COLUMN owner_email VARCHAR(255) COMMENT 'Property owner email address';

-- Add property_value column
ALTER TABLE incident_details ADD COLUMN property_value DECIMAL(12,2) COMMENT 'Property valuation from Estated API';

-- Add dwelling_type column (if it doesn't exist)
ALTER TABLE incident_details ADD COLUMN dwelling_type VARCHAR(100) COMMENT 'Type of dwelling/structure';

-- Add year_built column
ALTER TABLE incident_details ADD COLUMN year_built INT COMMENT 'Year the property was built';

-- Add square_footage column
ALTER TABLE incident_details ADD COLUMN square_footage INT COMMENT 'Total square footage of property';

-- Add bedrooms column
ALTER TABLE incident_details ADD COLUMN bedrooms INT COMMENT 'Number of bedrooms';

-- Add bathrooms column
ALTER TABLE incident_details ADD COLUMN bathrooms DECIMAL(3,1) COMMENT 'Number of bathrooms';

-- Add response_details column
ALTER TABLE incident_details ADD COLUMN response_details TEXT COMMENT 'Details about emergency response';

-- Show the updated table structure
DESCRIBE incident_details;
