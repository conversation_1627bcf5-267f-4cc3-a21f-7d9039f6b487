<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FireAlerts911 - Dashboard</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔥</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/modern-dispatch.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <!-- Leaflet MarkerCluster CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.css" />
    <!-- Leaflet Fullscreen CSS -->
    <link rel="stylesheet" href="https://api.mapbox.com/mapbox.js/plugins/leaflet-fullscreen/v1.0.1/leaflet.fullscreen.css" />
    <style>

        /* Incident table row styling for consistent height */
        .data-table tr {
            height: 62px; /* Fixed height for all table rows */
            line-height: 1.5;
            transition: background-color 0.2s;
        }

        .data-table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px; /* Maximum width for cells to prevent extreme stretching */
            vertical-align: middle; /* Center content vertically */
            padding: 12px 15px; /* Consistent padding */
        }

        /* For the title column that might have long text */
        .data-table td:nth-child(3) {
            max-width: 300px; /* Allow more space for titles */
        }

        /* For the location column that might have long text */
        .data-table td:nth-child(4) {
            max-width: 300px; /* Allow more space for addresses */
        }

        /* Empty row styling for consistent table height */
        .data-table tr.empty-row {
            height: 62px; /* Match the height of rows with content */
            background-color: transparent;
        }

        .data-table tr.empty-row td {
            border-top: 1px solid rgba(230, 230, 230, 0.3);
            border-bottom: none;
        }

        /* Sort icon styling */
        .sort-icon {
            margin-left: 5px;
            cursor: pointer;
            display: inline-block;
            transition: color 0.2s;
        }

        .sort-icon:hover {
            color: var(--accent-primary);
        }

        /* Notification styles now use utility classes from utility-classes.css */
        .notification {
            /* Inherits from .notification-base in utility-classes.css */
        }

        /* Map styles */
        .map-container {
            height: 400px;
            width: 100%;
            position: relative;
        }

        #map {
            height: 100%;
            width: 100%;
        }

        .map-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #1e2a42;
            color: #b8c2cc;
            text-align: center;
            z-index: 5;
        }

        .map-legend {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 4px;
            z-index: 10;
            box-shadow: 0 1px 4px rgba(0,0,0,0.2);
        }

        .map-legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .map-legend-item .icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .map-legend-item.fire .icon {
            background-color: #e53935;
        }

        .map-legend-item.water .icon {
            background-color: #2196f3;
        }

        /* Custom Leaflet Marker Styling */
        .custom-map-marker {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .marker-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .marker-icon.fire {
            background-color: #e53935;
        }

        .marker-icon.water {
            background-color: #2196f3;
        }

        .marker-info-content h3 {
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .marker-info-content p {
            margin: 5px 0;
            font-size: 14px;
        }

        .marker-actions {
            margin-top: 10px;
        }

        .info-btn {
            display: inline-block;
            padding: 5px 10px;
            background-color: #3f51b5;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 12px;
        }

        /* Anti-flicker table styling */
        .table-wrapper {
            background-color: var(--secondary-dark) !important;
        }

        .data-table {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody tr {
            background-color: var(--secondary-dark) !important;
        }

        .data-table tbody td {
            background-color: transparent !important;
        }

        /* Preserve hover effects */
        .data-table tbody tr:hover {
            background-color: var(--hover-bg) !important;
        }
    </style>
    <!-- Load authentication check script -->
    <script src="js/auth-check.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <!-- Leaflet MarkerCluster JS -->
    <script src="https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js"></script>
    <!-- Leaflet Fullscreen plugin -->
    <script src="https://api.mapbox.com/mapbox.js/plugins/leaflet-fullscreen/v1.0.1/Leaflet.fullscreen.min.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire-alt fa-lg" style="color: #e53935;"></i>
                <h1>FireAlerts911</h1>
            </div>
            <!-- The nav-menu div will be populated by renderRoleBasedSidebar() -->
            <div class="nav-menu">
                <!-- Sidebar items will be dynamically inserted here -->
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <button class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="user-actions">
                    <button class="btn-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="user-info">
                        <!-- Replaced placeholder image with Font Awesome icon -->
                        <div class="user-avatar-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span id="currentUserName">Admin User</span>
                    </div>
                    <button class="btn-icon" data-tooltip="Logout" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>

            <!-- Dashboard Header -->
            <div class="dashboard-grid">
                <!-- Active Incidents Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Active Incidents</div>
                        <div id="totalIncidentCount">0 Total</div>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; text-align: center;">
                            <div>
                                <div id="fireIncidentCount" style="font-size: 48px; font-weight: 700; margin-bottom: 10px;">0</div>
                                <div style="color: var(--text-secondary);">Fire Incidents</div>
                            </div>
                            <div style="margin: 0 20px; font-size: 24px; color: var(--border-color);">|</div>
                            <div>
                                <div id="waterIncidentCount" style="font-size: 48px; font-weight: 700; margin-bottom: 10px;">0</div>
                                <div style="color: var(--text-secondary);">Water Incidents</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscriber Stats Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Subscribers</div>
                        <div id="totalSubscriberCountHeader">0 Total</div>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; text-align: center;">
                            <div>
                                <div id="totalSubscriberCountMain" style="font-size: 48px; font-weight: 700; margin-bottom: 10px;">0</div>
                                <div style="color: var(--text-secondary);">Total Subscribers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Incidents -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">Recent Incidents</div>
                    <div class="card-actions">
                        <a href="add-incident.html" class="btn btn-danger btn-sm" style="margin-right: 8px;">
                            <i class="fas fa-plus"></i> Add Incident
                        </a>
                        <a href="incidents.html" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Desktop/Tablet Table View -->
                    <div class="table-wrapper table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="id" style="width: 80px">#ID <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="type" style="width: 100px">Type <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="title">Title <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="location">Location <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="date">Date <i class="fas fa-sort sort-icon"></i></th>
                                    <th data-sort="status" style="width: 120px">Status <i class="fas fa-sort sort-icon"></i></th>
                                    <th style="width: 100px">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="recent-incidents-body" style="background-color: var(--secondary-dark) !important;">
                                <tr style="background-color: var(--secondary-dark) !important;">
                                    <td colspan="7" style="text-align: center; background-color: transparent !important; color: #f5f5f5;"><i class="fas fa-spinner fa-spin" style="color: #1e88e5;"></i> Loading recent incidents...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="table-mobile-cards" id="recent-incidents-mobile-cards">
                        <!-- Mobile cards will be populated by JavaScript -->
                    </div>

                    <!-- Add pagination controls -->
                    <div id="incidents-pagination" class="pagination-container" style="margin-top: 15px; display: flex; justify-content: center;">
                        <!-- Pagination will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Map Overview -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">Incident Map Overview</div>
                    <div class="card-actions">
                        <a href="map.html" class="btn btn-primary btn-sm">
                            <i class="fas fa-expand"></i> Full Map
                        </a>
                    </div>
                </div>
                <div class="map-container" id="dispatch-map">
                    <div id="map"></div>
                    <div class="map-placeholder" id="map-placeholder">
                        <div style="text-align:center;">
                            <i class="fas fa-map-marked-alt" style="font-size:48px; margin-bottom:10px;"></i>
                            <p>Loading map data...</p>
                        </div>
                    </div>
                    <div class="map-legend">
                        <div class="map-legend-item fire">
                            <div class="icon"><i class="fas fa-fire fa-xs"></i></div>
                            <span>Fire Incidents</span>
                        </div>
                        <div class="map-legend-item water">
                            <div class="icon"><i class="fas fa-water fa-xs"></i></div>
                            <span>Water Incidents</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-grid" style="margin-top: 20px;">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Quick Actions</div>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions-grid">
                            <a href="add-incident.html" class="quick-action-btn btn-danger">
                                <i class="fas fa-plus-circle fa-lg"></i>
                                <span>Add Incident</span>
                            </a>
                            <a href="incidents.html" class="quick-action-btn btn-primary">
                                <i class="fas fa-list fa-lg"></i>
                                <span>View Incidents</span>
                            </a>
                            <a href="subscribers.html" class="quick-action-btn btn-outline">
                                <i class="fas fa-users fa-lg"></i>
                                <span>Manage Subscribers</span>
                            </a>
                            <a href="notifications.html" class="quick-action-btn btn-outline">
                                <i class="fas fa-paper-plane fa-lg"></i>
                                <span>Send Notification</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">System Status</div>
                        <button class="btn-icon" id="refreshStatusBtn" data-tooltip="Refresh Status">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="system-status-loading" id="systemStatusLoading" style="display: none; text-align: center; padding: 20px;">
                            <i class="fas fa-spinner fa-spin"></i> Checking system status...
                        </div>
                        <ul class="system-status-list" id="systemStatusList">
                            <li class="status-item" id="apiStatus">
                                <span class="status-label">
                                    <i class="fas fa-server status-icon"></i> API Status
                                </span>
                                <span class="status-value">
                                    <i class="fas fa-spinner fa-spin"></i> Checking...
                                </span>
                            </li>
                            <li class="status-item" id="databaseStatus">
                                <span class="status-label">
                                    <i class="fas fa-database status-icon"></i> Database
                                </span>
                                <span class="status-value">
                                    <i class="fas fa-spinner fa-spin"></i> Checking...
                                </span>
                            </li>
                            <li class="status-item" id="notificationStatus">
                                <span class="status-label">
                                    <i class="fas fa-bell status-icon"></i> Notification System
                                </span>
                                <span class="status-value">
                                    <i class="fas fa-spinner fa-spin"></i> Checking...
                                </span>
                            </li>
                            <li class="status-item last-item" id="lastUpdated">
                                <span class="status-label">
                                    <i class="fas fa-clock status-icon"></i> Last Updated
                                </span>
                                <span class="status-value" id="lastUpdatedTime">
                                    Never
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/modern-dispatch.js"></script>
    <script src="js/api.js"></script>
    <script src="js/leaflet-maps-integration.js?v=20250524"></script>
    <script>
        // Local renderSidebar function removed - now using shared-utils.js

        // Logout function
        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                if (window.AuthCheck && typeof window.AuthCheck.logout === 'function') {
                    window.AuthCheck.logout();
                } else {
                    // Fallback logout
                    window.location.href = 'login.html';
                }
            }
        }

        // Sidebar rendering will be handled in the main DOMContentLoaded listener below

        // Variables for pagination and sorting
        let currentPage = 1;
        let incidentsPerPage = 5;
        let allIncidents = [];
        let currentSortColumn = 'id'; // Default sort column is ID
        let currentSortOrder = 'ASC'; // Default sort order is ascending

        // Function to load recent incidents into the table with pagination
        function loadRecentIncidents(page = 1) {
            const tbody = document.getElementById('recent-incidents-body');
            const paginationContainer = document.getElementById('incidents-pagination');

            if (!tbody) {
                console.error("Element with ID 'recent-incidents-body' not found.");
                return;
            }

            // Show loading state with dark background to prevent flicker
            tbody.innerHTML = '<tr style="background-color: var(--secondary-dark) !important;"><td colspan="7" style="text-align: center; background-color: transparent !important; color: #f5f5f5;"><i class="fas fa-spinner fa-spin" style="color: #1e88e5;"></i> Loading recent incidents...</td></tr>';

            // If we're loading a different page of already loaded incidents
            if (allIncidents.length > 0 && page !== currentPage) {
                currentPage = page;
                displayIncidents(allIncidents);
                return;
            }

            // Only fetch from API if we don't have the incidents yet
            if (allIncidents.length === 0) {
                // Fetch more incidents than we need per page to allow for pagination
                API.incidents.getAll({ limit: 20, sortBy: 'incidentDate', order: 'DESC' })
                    .then(response => {
                        console.log('Incidents API response:', response);

                        // Handle different response formats
                        if (response && response.data && Array.isArray(response.data)) {
                            // Standard API format: { data: [...] }
                            allIncidents = response.data;
                        } else if (Array.isArray(response)) {
                            // Direct array format
                            allIncidents = response;
                        } else if (response && response.incidents && Array.isArray(response.incidents)) {
                            // Alternative format: { incidents: [...] }
                            allIncidents = response.incidents;
                        } else {
                            console.error('Unexpected API response format:', response);
                            allIncidents = [];
                        }

                        // Apply default sorting (ID ascending) before displaying
                        sortTableInternal('id', 'ASC');

                        // Display the current page of incidents
                        displayIncidents(allIncidents);
                    })
                    .catch(error => {
                        console.error("Error fetching recent incidents:", error);
                        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: var(--accent-red);">Error loading recent incidents.</td></tr>';
                        showNotification("Error loading recent incidents.", "error");
                    });
            } else {
                // If we already have incidents, just display them
                displayIncidents(allIncidents);
            }
        }

        // Internal sorting function (doesn't update UI indicators)
        function sortTableInternal(column, order) {
            // Skip sorting if we don't have incidents loaded
            if (allIncidents.length === 0) return;

            // Set the sort column and order
            currentSortColumn = column;
            currentSortOrder = order;

            // Sort the incidents array
            allIncidents.sort((a, b) => {
                let valA, valB;

                // Extract the values to compare based on the column
                switch(column) {
                    case 'id':
                        valA = parseInt(a.id) || 0;
                        valB = parseInt(b.id) || 0;
                        break;
                    case 'type':
                        valA = a.incidentType?.name?.toLowerCase() || '';
                        valB = b.incidentType?.name?.toLowerCase() || '';
                        break;
                    case 'title':
                        valA = a.title?.toLowerCase() || '';
                        valB = b.title?.toLowerCase() || '';
                        break;
                    case 'location':
                        valA = `${a.address || ''} ${a.city || ''}`.toLowerCase();
                        valB = `${b.address || ''} ${b.city || ''}`.toLowerCase();
                        break;
                    case 'date':
                        valA = new Date(a.incidentDate || a.createdAt || 0).getTime();
                        valB = new Date(b.incidentDate || b.createdAt || 0).getTime();
                        break;
                    case 'status':
                        valA = a.status?.name?.toLowerCase() || '';
                        valB = b.status?.name?.toLowerCase() || '';
                        break;
                    default:
                        valA = 0;
                        valB = 0;
                }

                // Perform the comparison based on the sort order
                if (order === 'ASC') {
                    return valA > valB ? 1 : (valA < valB ? -1 : 0);
                } else {
                    return valA < valB ? 1 : (valA > valB ? -1 : 0);
                }
            });
        }

        // Function to handle column sorting (updates UI indicators)
        function sortTable(column) {
            // Skip sorting if we don't have incidents loaded
            if (allIncidents.length === 0) return;

            // If clicking the same column, toggle the sort order
            if (column === currentSortColumn) {
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                // New column, default to ascending
                currentSortColumn = column;
                currentSortOrder = 'ASC';
            }

            // Sort using the internal function
            sortTableInternal(currentSortColumn, currentSortOrder);

            // Update sort indicators in the UI
            updateSortIndicators();

            // Reset to first page and display the sorted incidents
            currentPage = 1;
            displayIncidents(allIncidents);
        }

        // Function to display a specific page of incidents
        function displayIncidents(incidents) {
            const tbody = document.getElementById('recent-incidents-body');
            const mobileCards = document.getElementById('recent-incidents-mobile-cards');
            const paginationContainer = document.getElementById('incidents-pagination');

            // Clear previous content
            tbody.innerHTML = '';
            if (mobileCards) {
                mobileCards.innerHTML = '';
            }

            if (incidents.length === 0) {
                // Handle empty state for table
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">No incidents found.</td></tr>';

                // Handle empty state for mobile cards
                if (mobileCards) {
                    mobileCards.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <p>No recent incidents found.</p>
                        </div>
                    `;
                }

                // Add additional empty rows to maintain consistent height
                for (let i = 0; i < 4; i++) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.innerHTML = '<td colspan="7">&nbsp;</td>';
                    tbody.appendChild(emptyRow);
                }
                paginationContainer.innerHTML = '';
                return;
            }

            // Calculate pagination values
            const totalPages = Math.ceil(incidents.length / incidentsPerPage);
            const startIndex = (currentPage - 1) * incidentsPerPage;
            const endIndex = Math.min(startIndex + incidentsPerPage, incidents.length);

            // Get current page of incidents
            const pageIncidents = incidents.slice(startIndex, endIndex);

            // Display current page of incidents
            pageIncidents.forEach(incident => {
                // Determine incident type icon and class
                let typeIcon = '<i class="fas fa-question-circle"></i>';
                let typeClass = 'default';
                let typeName = 'Unknown';

                // Check if incident has an incidentType property
                if (incident.incidentType) {
                    if (incident.incidentType.category === 'fire') {
                        typeIcon = '<i class="fas fa-fire"></i>';
                        typeClass = 'fire';
                        typeName = 'Fire';
                    } else if (incident.incidentType.category === 'water') {
                        typeIcon = '<i class="fas fa-water"></i>';
                        typeClass = 'water';
                        typeName = 'Water';
                    }

                    // Use the specific type name if available
                    typeName = incident.incidentType.name || typeName;
                }

                // Format status with appropriate badge
                let statusBadge = '<span class="status-badge status-unknown">Unknown</span>';
                if (incident.status) {
                    const statusName = incident.status.name;
                    if (statusName) {
                        const statusClass = statusName.toLowerCase();
                        statusBadge = `<span class="status-badge status-${statusClass}">${statusName}</span>`;
                    }
                }

                // Format date using shared utility
                const dateToFormat = incident.incidentDate || incident.createdAt;
                const formattedDate = window.FireAlertsUtils ?
                    window.FireAlertsUtils.formatters.formatDate(dateToFormat) :
                    (dateToFormat ? new Date(dateToFormat).toLocaleString() : 'N/A');

                // Create location string from address fields
                const location = incident.address ?
                    `${incident.address}, ${incident.city || ''}` :
                    (incident.city || 'Unknown Location');

                // Create table row for desktop/tablet
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>#${incident.id}</td>
                    <td>
                        <div class="incident-type-icon ${typeClass}">
                            ${typeIcon}
                            <span>${typeName}</span>
                        </div>
                    </td>
                    <td>${incident.title || 'Untitled Incident'}</td>
                    <td>${location}</td>
                    <td>${formattedDate}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="action-buttons">
                            <a href="view-incident.html?id=${incident.id}" class="btn-icon" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="edit-incident.html?id=${incident.id}" class="btn-icon" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);

                // Create mobile card for mobile view
                if (mobileCards) {
                    const card = document.createElement('div');
                    card.className = 'table-card';
                    card.innerHTML = `
                        <div class="table-card-header">
                            <div class="table-card-title">
                                <div class="incident-type-icon ${typeClass}" style="margin-right: 10px;">
                                    ${typeIcon}
                                </div>
                                <div>
                                    <div style="font-weight: 500;">${incident.title || 'Untitled Incident'}</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">#${incident.id} • ${typeName}</div>
                                </div>
                            </div>
                            <div class="table-card-status">
                                ${statusBadge}
                            </div>
                        </div>
                        <div class="table-card-content">
                            <div class="table-card-field">
                                <span class="table-card-label">Location:</span>
                                <span class="table-card-value">${location}</span>
                            </div>
                            <div class="table-card-field">
                                <span class="table-card-label">Date:</span>
                                <span class="table-card-value">${formattedDate}</span>
                            </div>
                        </div>
                        <div class="table-card-actions">
                            <a href="view-incident.html?id=${incident.id}" class="btn btn-sm btn-outline">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="edit-incident.html?id=${incident.id}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </div>
                    `;
                    mobileCards.appendChild(card);
                }
            });

            // Add empty rows if we have fewer than 5 incidents on this page
            const currentRowCount = pageIncidents.length;
            for (let i = currentRowCount; i < incidentsPerPage; i++) {
                const emptyRow = document.createElement('tr');
                emptyRow.className = 'empty-row';
                emptyRow.innerHTML = '<td colspan="7">&nbsp;</td>';
                tbody.appendChild(emptyRow);
            }

            // Create pagination controls
            createPaginationControls(totalPages);
        }

        // Function to create pagination controls
        function createPaginationControls(totalPages) {
            const paginationContainer = document.getElementById('incidents-pagination');

            if (!paginationContainer) return;

            // Clear previous pagination
            paginationContainer.innerHTML = '';

            // Don't show pagination if only one page
            if (totalPages <= 1) return;

            // Create pagination list
            const paginationList = document.createElement('ul');
            paginationList.className = 'pagination';

            // Previous button
            const prevLi = document.createElement('li');
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage > 1) {
                    loadRecentIncidents(currentPage - 1);
                }
            });
            prevLi.appendChild(prevLink);
            if (currentPage === 1) prevLi.className = 'disabled';
            paginationList.appendChild(prevLi);

            // Page numbers
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust if we're near the end
            if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page button if not visible
            if (startPage > 1) {
                const firstLi = document.createElement('li');
                const firstLink = document.createElement('a');
                firstLink.href = '#';
                firstLink.textContent = '1';
                firstLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadRecentIncidents(1);
                });
                firstLi.appendChild(firstLink);
                paginationList.appendChild(firstLi);

                // Ellipsis if needed
                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }
            }

            // Page buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                if (i === currentPage) pageLi.className = 'active';

                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadRecentIncidents(i);
                });
                pageLi.appendChild(pageLink);
                paginationList.appendChild(pageLi);
            }

            // Last page button if not visible
            if (endPage < totalPages) {
                // Ellipsis if needed
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'disabled';
                    ellipsisLi.innerHTML = '<span>...</span>';
                    paginationList.appendChild(ellipsisLi);
                }

                const lastLi = document.createElement('li');
                const lastLink = document.createElement('a');
                lastLink.href = '#';
                lastLink.textContent = totalPages;
                lastLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadRecentIncidents(totalPages);
                });
                lastLi.appendChild(lastLink);
                paginationList.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage < totalPages) {
                    loadRecentIncidents(currentPage + 1);
                }
            });
            nextLi.appendChild(nextLink);
            if (currentPage === totalPages) nextLi.className = 'disabled';
            paginationList.appendChild(nextLi);

            paginationContainer.appendChild(paginationList);
        }

        // Function to handle column sorting (updates UI indicators)
        function sortTable(column) {
            // Skip sorting if we don't have incidents loaded
            if (allIncidents.length === 0) return;

            // If clicking the same column, toggle the sort order
            if (column === currentSortColumn) {
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                // New column, default to ascending
                currentSortColumn = column;
                currentSortOrder = 'ASC';
            }

            // Sort using the internal function
            sortTableInternal(currentSortColumn, currentSortOrder);

            // Update sort indicators in the UI
            updateSortIndicators();

            // Reset to first page and display the sorted incidents
            currentPage = 1;
            displayIncidents(allIncidents);
        }

        // Function to update sort indicators in the table headers
        function updateSortIndicators() {
            const headers = document.querySelectorAll('.data-table th[data-sort]');

            // Remove all existing sort indicators
            headers.forEach(header => {
                const iconElement = header.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = 'fas fa-sort'; // Reset to default icon
                }
            });

            // Add the appropriate sort indicator to the current sort column
            const activeHeader = document.querySelector(`.data-table th[data-sort="${currentSortColumn}"]`);
            if (activeHeader) {
                const iconElement = activeHeader.querySelector('i.fas');
                if (iconElement) {
                    iconElement.className = currentSortOrder === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down';
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log("Dashboard loading data...");

            // Initialize sidebar first
            if (window.FireAlertsUtils && window.FireAlertsUtils.renderRoleBasedSidebar) {
                window.FireAlertsUtils.renderRoleBasedSidebar('index');
            } else {
                console.warn('Shared navigation utilities not available - navigation may not render properly');
            }

            if (typeof API === 'undefined') {
                console.error("API object not found. Make sure api.js is loaded correctly.");
                showNotification("Error loading dashboard data.", "error");
                // Clear loading states if API is missing
                 const incidentBody = document.getElementById('recent-incidents-body');
                 if(incidentBody) incidentBody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: var(--accent-red);">Error initializing page.</td></tr>';
                return;
            }

            // Add click event listeners to the sort icons only (not entire headers)
            const sortIcons = document.querySelectorAll('.sort-icon');
            sortIcons.forEach(icon => {
                icon.addEventListener('click', function(e) {
                    // Stop event from propagating to parent elements
                    e.stopPropagation();

                    // Get the column name from the parent header
                    const header = this.closest('th');
                    const column = header.getAttribute('data-sort');
                    sortTable(column);
                });
            });

            // Remove the cursor pointer from headers since only icons are clickable now
            const headers = document.querySelectorAll('.data-table th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'default';
            });

            // Load incident counts
            API.incidents.getCounts()
                .then(data => {
                    console.log("Incident count data received:", data);
                    // Ensure IDs exist before setting textContent
                    const fireCountEl = document.getElementById('fireIncidentCount');
                    const waterCountEl = document.getElementById('waterIncidentCount');
                    const totalCountEl = document.getElementById('totalIncidentCount');

                    if (fireCountEl && waterCountEl && totalCountEl) {
                        if (data && typeof data.fireCount !== 'undefined' && typeof data.waterCount !== 'undefined') {
                            const fireCount = data.fireCount || 0;
                            const waterCount = data.waterCount || 0;
                            const totalCount = fireCount + waterCount;
                            fireCountEl.textContent = fireCount;
                            waterCountEl.textContent = waterCount;
                            totalCountEl.textContent = `${totalCount} Total`;
                        } else if (Array.isArray(data)) {
                            // Fallback count logic
                            let fireCount = 0, waterCount = 0;
                            data.forEach(incident => {
                                if (incident.incidentType && incident.incidentType.category === 'fire') fireCount++;
                                if (incident.incidentType && incident.incidentType.category === 'water') waterCount++;
                            });
                            const totalCount = fireCount + waterCount;
                            fireCountEl.textContent = fireCount;
                            waterCountEl.textContent = waterCount;
                            totalCountEl.textContent = `${totalCount} Total`;
                        } else {
                            console.warn("Received unexpected incident count data structure:", data);
                        }
                    } else {
                        console.error("One or more incident count elements not found.");
                    }
                })
                .catch(error => {
                    console.error("Error fetching incident count data:", error);
                    showNotification("Error loading incident counts.", "error");
                });

            // Load subscriber stats (using companies API)
            if (API.companies && typeof API.companies.getStats === 'function') {
                API.companies.getStats()
                    .then(data => {
                        console.log("Company/Subscriber data received:", data);
                        const headerEl = document.getElementById('totalSubscriberCountHeader');
                        const mainEl = document.getElementById('totalSubscriberCountMain');

                        if (headerEl && mainEl) {
                            if (data && typeof data.totalCount !== 'undefined') {
                                const totalSubscribers = data.totalCount || 0;
                                headerEl.textContent = `${totalSubscribers} Total`;
                                mainEl.textContent = totalSubscribers;
                            } else if (Array.isArray(data)) {
                                const totalSubscribers = data.length;
                                headerEl.textContent = `${totalSubscribers} Total`;
                                mainEl.textContent = totalSubscribers;
                            } else if (data && data.success === false) {
                                // Always show "0" instead of "-" for consistency
                                console.warn("Permission error accessing subscriber data:", data.message);
                                headerEl.textContent = "0 Total";
                                mainEl.textContent = "0";

                                // Show notification for admins about the permission issue
                                showNotification("Cannot access subscriber data. Please check API permissions.", "warning");
                            } else {
                                console.warn("Received unexpected subscriber data structure:", data);
                                headerEl.textContent = "0 Total";
                                mainEl.textContent = "0";
                            }
                        } else {
                            console.error("One or more subscriber count elements not found.");
                        }
                    })
                    .catch(error => {
                        console.error("Error fetching subscriber data:", error);

                        // Show "0" instead of "-" when there's an error
                        const headerEl = document.getElementById('totalSubscriberCountHeader');
                        const mainEl = document.getElementById('totalSubscriberCountMain');

                        if (headerEl && mainEl) {
                            headerEl.textContent = "0 Total";
                            mainEl.textContent = "0";
                        }

                        // Show error notification
                        showNotification("Error loading subscriber data. Please try refreshing the page.", "error");
                    });
            } else {
                console.warn("Companies API not available, setting subscriber count to 0");
                const headerEl = document.getElementById('totalSubscriberCountHeader');
                const mainEl = document.getElementById('totalSubscriberCountMain');

                if (headerEl && mainEl) {
                    headerEl.textContent = "0 Total";
                    mainEl.textContent = "0";
                }
            }

            // Load recent incidents table data with pagination
            loadRecentIncidents(1);

            // Load system status
            loadSystemStatus();

            // Set up refresh button for system status
            const refreshBtn = document.getElementById('refreshStatusBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    this.querySelector('i').classList.add('fa-spin');
                    loadSystemStatus().finally(() => {
                        this.querySelector('i').classList.remove('fa-spin');
                    });
                });
            }

            // Auto-refresh system status every 5 minutes
            setInterval(loadSystemStatus, 5 * 60 * 1000);

            // Add window resize listener for responsive table/card switching
            window.addEventListener('resize', function() {
                // Debounce resize events
                clearTimeout(window.resizeTimeout);
                window.resizeTimeout = setTimeout(function() {
                    // Force re-render of table/cards based on current window size
                    if (allIncidents && allIncidents.length > 0) {
                        displayIncidents(allIncidents);
                    }
                }, 150);
            });
        });

        // Function to load and update system status
        async function loadSystemStatus() {
            const statusItems = {
                api: document.getElementById('apiStatus'),
                database: document.getElementById('databaseStatus'),
                notification: document.getElementById('notificationStatus')
            };

            const lastUpdatedEl = document.getElementById('lastUpdatedTime');

            // Function to update status display
            function updateStatusItem(element, status, message) {
                const valueEl = element.querySelector('.status-value');
                const iconEl = element.querySelector('.status-icon');

                // Remove all status classes
                element.classList.remove('status-operational', 'status-warning', 'status-error', 'status-unknown');

                // Add appropriate status class
                element.classList.add(`status-${status}`);

                // Update the status text
                valueEl.textContent = message;
            }

            try {
                // Check API status by making a simple API call
                const apiCheck = await checkApiStatus();
                updateStatusItem(statusItems.api, apiCheck.status, apiCheck.message);

                // Check database status
                const dbCheck = await checkDatabaseStatus();
                updateStatusItem(statusItems.database, dbCheck.status, dbCheck.message);

                // Check notification system status
                const notificationCheck = await checkNotificationStatus();
                updateStatusItem(statusItems.notification, notificationCheck.status, notificationCheck.message);

                // Update last updated time
                lastUpdatedEl.textContent = new Date().toLocaleTimeString();

            } catch (error) {
                console.error('Error checking system status:', error);

                // Set all systems to unknown status
                Object.values(statusItems).forEach(item => {
                    updateStatusItem(item, 'unknown', 'Unknown');
                });

                lastUpdatedEl.textContent = 'Error checking status';
            }
        }

        // Function to check API status
        async function checkApiStatus() {
            try {
                const response = await fetch('/api/settings/health', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.services.api) {
                        const apiService = data.data.services.api;
                        return {
                            status: apiService.status,
                            message: apiService.status === 'operational' ? 'Operational' : apiService.message
                        };
                    }
                }
                return { status: 'error', message: 'Health Check Failed' };
            } catch (error) {
                return { status: 'error', message: 'Offline' };
            }
        }

        // Function to check database status
        async function checkDatabaseStatus() {
            try {
                const response = await fetch('/api/settings/health', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.services.database) {
                        const dbService = data.data.services.database;
                        return {
                            status: dbService.status,
                            message: dbService.status === 'operational' ? 'Operational' : dbService.message
                        };
                    }
                }
                return { status: 'error', message: 'Health Check Failed' };
            } catch (error) {
                console.error('Database check failed:', error);
                return { status: 'error', message: 'Connection Error' };
            }
        }

        // Function to check notification system status
        async function checkNotificationStatus() {
            try {
                const response = await fetch('/api/settings/health', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.services.notifications) {
                        const notificationService = data.data.services.notifications;
                        return {
                            status: notificationService.status,
                            message: notificationService.message
                        };
                    }
                }
                return { status: 'unknown', message: 'Health Check Failed' };
            } catch (error) {
                console.error('Notification system check failed:', error);
                return { status: 'unknown', message: 'Unknown' };
            }
        }
    </script>
</body>
</html>
