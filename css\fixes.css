/* Fix for sidebar visibility and menu styles */
.sidebar {
    background-color: var(--primary-dark);
    z-index: 1000;
}

.sidebar .nav-menu .nav-item {
    color: var(--text-secondary);
}

.sidebar .nav-menu .nav-item.active {
    background-color: rgba(255, 255, 255, 0.05);
    border-left: 3px solid var(--accent-blue);
    color: var(--text-light);
}

/* Location dropdown styles - completely revised */
.select-wrapper {
    position: relative;
    display: block;
    width: 100%;
}

/* Hide the default browser dropdown arrow */
.select-wrapper select.form-control {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important; /* Remove any background images */
    padding-right: 30px;
    background-color: var(--secondary-dark);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    width: 100%;
    position: relative;
    z-index: 1;
}

/* Create custom dropdown arrow */
.select-wrapper::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0; 
    height: 0; 
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #aaa;
    z-index: 2;
    pointer-events: none; /* Allows clicks to pass through to the select */
}

/* Hide all extra dropdown arrows on IE/Edge */
.select-wrapper select::-ms-expand {
    display: none;
}

/* Hide the hidden input fields */
.select-wrapper input[type="hidden"] {
    display: none;
}

/* Set proper z-index */
.select-wrapper select.form-control {
    z-index: 1;
}

/* Dropdown focus styles */
.select-wrapper select.form-control:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

/* Ensure disabled dropdowns show properly */
.select-wrapper select.form-control:disabled {
    background-color: rgba(0, 0, 0, 0.1);
    cursor: not-allowed;
    opacity: 0.7;
}

/* Improve dropdown readability in dark mode */
.dark-mode .select-wrapper::after {
    border-top-color: #ccc;
}

/* Fix for subscriber statistics to ensure consistent display of zero values */
.dashboard-grid .card-content div[style*="display: flex"] > div:first-child {
    font-size: 48px !important; 
    font-weight: 700 !important;
    line-height: 1.2 !important;
    min-height: 48px !important;
    display: block !important;
    text-align: center !important;
    margin-bottom: 10px !important;
}

/* Force zero values to have the same appearance as other numbers */
.dashboard-grid .card-content div[style*="display: flex"] > div:first-child:contains('0') {
    font-size: 48px !important;
}

/* Class-based styling for consistent stats display */
.stat-number {
    font-size: 48px !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
    min-height: 48px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

/* Ensure consistent label sizing across all stats */
.stat-label {
    font-size: 16px !important;
    font-weight: normal !important;
    color: var(--text-secondary) !important;
    text-align: center !important;
    display: block !important;
}

/* Use CSS variables to enforce consistent font size */
.dashboard-grid .card-content {
    --stat-font-size: 48px;
    --stat-label-font-size: 16px;
}

/* Owner information card styling */
.owner-info-card {
    background-color: rgba(30, 136, 229, 0.1);
    border: 1px solid var(--accent-blue);
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.owner-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.owner-info-header i {
    color: var(--accent-blue);
    margin-right: 10px;
    font-size: 20px;
}
