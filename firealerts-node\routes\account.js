const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const db = require('../models');
const bcrypt = require('bcryptjs');

// @route   GET api/account/profile
// @desc    Get current user's profile
// @access  Private
router.get('/profile', auth, async (req, res) => {
  try {
    const user = await db.user.findByPk(req.user.id, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: db.company,
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ]
    });

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (err) {
    console.error('Error fetching user profile:', err);
    res.status(500).json({ success: false, message: 'Server error fetching profile' });
  }
});

// @route   PUT api/account/profile
// @desc    Update current user's profile
// @access  Private
router.put('/profile', [
  auth,
  [
    check('firstName', 'First name is required').notEmpty(),
    check('lastName', 'Last name is required').notEmpty(),
    check('email', 'Please include a valid email').isEmail()
  ]
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { firstName, lastName, email, phone } = req.body;

    // Check if email is already taken by another user
    const existingUser = await db.user.findOne({
      where: {
        email: email.toLowerCase(),
        id: { [db.Sequelize.Op.ne]: req.user.id }
      }
    });

    if (existingUser) {
      return res.status(400).json({ success: false, message: 'Email is already in use' });
    }

    // Update user profile
    const user = await db.user.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    await user.update({
      firstName,
      lastName,
      email: email.toLowerCase(),
      phone
    });

    // Log the activity
    await db.activity.create({
      action: 'profile_update',
      details: JSON.stringify({
        updatedFields: ['firstName', 'lastName', 'email', 'phone'],
        userId: user.id
      }),
      userId: req.user.id,
      ip: req.ip || req.connection.remoteAddress,
      module: 'account',
      severity: 'info'
    });

    // Return updated user data (excluding password)
    const updatedUser = await db.user.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: updatedUser
    });
  } catch (err) {
    console.error('Error updating user profile:', err);
    res.status(500).json({ success: false, message: 'Server error updating profile' });
  }
});

// @route   PUT api/account/password
// @desc    Change current user's password
// @access  Private
router.put('/password', [
  auth,
  [
    check('currentPassword', 'Current password is required').notEmpty(),
    check('newPassword', 'New password must be at least 6 characters').isLength({ min: 6 }),
    check('confirmPassword', 'Password confirmation is required').notEmpty()
  ]
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { currentPassword, newPassword, confirmPassword } = req.body;

    // Check if new password and confirmation match
    if (newPassword !== confirmPassword) {
      return res.status(400).json({ success: false, message: 'New password and confirmation do not match' });
    }

    // Get user with password
    const user = await db.user.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Verify current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ success: false, message: 'Current password is incorrect' });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    await user.update({ password: hashedPassword });

    // Log the activity
    await db.activity.create({
      action: 'password_change',
      details: JSON.stringify({ userId: user.id }),
      userId: req.user.id,
      ip: req.ip || req.connection.remoteAddress,
      module: 'account',
      severity: 'info'
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (err) {
    console.error('Error changing password:', err);
    res.status(500).json({ success: false, message: 'Server error changing password' });
  }
});

// @route   GET api/account/activity
// @desc    Get current user's activity log with pagination
// @access  Private
router.get('/activity', auth, async (req, res) => {
  try {
    const { limit = 20, offset = 0 } = req.query;

    // Get total count and paginated activities
    const { count, rows: activities } = await db.activity.findAndCountAll({
      where: { userId: req.user.id },
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id', 'action', 'details', 'createdAt', 'ipAddress', 'userAgent', 'module', 'severity'
      ]
    });

    // Format the response
    const formattedActivities = activities.map(activity => ({
      id: activity.id,
      action: activity.action,
      details: activity.details,
      created_at: activity.createdAt,
      ip_address: activity.ipAddress,
      user_agent: activity.userAgent,
      module: activity.module,
      severity: activity.severity
    }));

    res.json({
      success: true,
      data: formattedActivities,
      pagination: {
        total: count,
        limit: parseInt(limit),
        offset: parseInt(offset),
        totalPages: Math.ceil(count / parseInt(limit)),
        currentPage: Math.floor(parseInt(offset) / parseInt(limit)) + 1
      }
    });
  } catch (err) {
    console.error('Error fetching user activity:', err);
    res.status(500).json({ success: false, message: 'Server error fetching activity' });
  }
});

// @route   GET api/account/preferences
// @desc    Get current user's notification preferences
// @access  Private
router.get('/preferences', auth, async (req, res) => {
  try {
    let preferences = await db.userPreference.findOne({
      where: { user_id: req.user.id }
    });

    // If no preferences exist, create default ones
    if (!preferences) {
      preferences = await db.userPreference.create({
        user_id: req.user.id,
        notifyByEmail: true,
        notifyBySms: false,
        notifyByPush: false,
        fireIncidentAlert: true,
        waterIncidentAlert: true,
        otherIncidentAlert: true,
        dailyDigest: false,
        alertRadius: 10
      });
    }

    res.json({
      success: true,
      data: preferences
    });
  } catch (err) {
    console.error('Error fetching user preferences:', err);
    res.status(500).json({ success: false, message: 'Server error fetching preferences' });
  }
});

// @route   PUT api/account/preferences
// @desc    Update current user's notification preferences
// @access  Private
router.put('/preferences', auth, async (req, res) => {
  try {
    const {
      notifyByEmail,
      notifyBySms,
      notifyByPush,
      fireIncidentAlert,
      waterIncidentAlert,
      otherIncidentAlert,
      dailyDigest,
      alertRadius,
      quietHoursStart,
      quietHoursEnd
    } = req.body;

    // Find or create user preferences
    let preferences = await db.userPreference.findOne({
      where: { user_id: req.user.id }
    });

    if (!preferences) {
      preferences = await db.userPreference.create({
        user_id: req.user.id
      });
    }

    // Update preferences
    await preferences.update({
      notifyByEmail: notifyByEmail !== undefined ? notifyByEmail : preferences.notifyByEmail,
      notifyBySms: notifyBySms !== undefined ? notifyBySms : preferences.notifyBySms,
      notifyByPush: notifyByPush !== undefined ? notifyByPush : preferences.notifyByPush,
      fireIncidentAlert: fireIncidentAlert !== undefined ? fireIncidentAlert : preferences.fireIncidentAlert,
      waterIncidentAlert: waterIncidentAlert !== undefined ? waterIncidentAlert : preferences.waterIncidentAlert,
      otherIncidentAlert: otherIncidentAlert !== undefined ? otherIncidentAlert : preferences.otherIncidentAlert,
      dailyDigest: dailyDigest !== undefined ? dailyDigest : preferences.dailyDigest,
      alertRadius: alertRadius !== undefined ? alertRadius : preferences.alertRadius,
      quietHoursStart: quietHoursStart !== undefined ? quietHoursStart : preferences.quietHoursStart,
      quietHoursEnd: quietHoursEnd !== undefined ? quietHoursEnd : preferences.quietHoursEnd
    });

    // Log the activity
    await db.activity.create({
      action: 'preferences_update',
      details: JSON.stringify({
        updatedFields: Object.keys(req.body),
        userId: req.user.id
      }),
      userId: req.user.id,
      ip: req.ip || req.connection.remoteAddress,
      module: 'account',
      severity: 'info'
    });

    res.json({
      success: true,
      message: 'Preferences updated successfully',
      data: preferences
    });
  } catch (err) {
    console.error('Error updating user preferences:', err);
    res.status(500).json({ success: false, message: 'Server error updating preferences' });
  }
});

module.exports = router;
