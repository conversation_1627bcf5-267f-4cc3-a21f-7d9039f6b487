/**
 * Google Maps Integration for FireAlerts911
 */

// Initialize Google Maps Autocomplete
function initializeGoogleAddressAutocomplete() {
  // Check if Google Maps API is loaded
  if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
    console.warn('Google Maps API or Places library not loaded - address inputs will work normally without autocomplete');
    // Schedule a retry after a short delay - the API might still be loading
    setTimeout(() => {
      if (typeof google !== 'undefined' && typeof google.maps !== 'undefined' && typeof google.maps.places !== 'undefined') {
        console.log('Google Maps API now available, initializing autocomplete');
        initializeGoogleAddressAutocomplete();
      } else {
        console.log('Google Maps API still not available after retry - continuing without autocomplete');
      }
    }, 2000);
    return;
  }

  // Get all address input fields that should have autocomplete
  const addressInputs = document.querySelectorAll('#google_address');

  if (!addressInputs || addressInputs.length === 0) {
    return; // No inputs to enhance
  }

  addressInputs.forEach(input => {
    // Create a div to hold the Place Autocomplete Element
    const autocompleteWrapper = document.createElement('div');
    autocompleteWrapper.className = 'place-autocomplete-wrapper';
    autocompleteWrapper.style.position = 'relative';
    autocompleteWrapper.style.width = '100%';

    // Replace the input with the wrapper and put the input inside it
    input.parentNode.insertBefore(autocompleteWrapper, input);
    autocompleteWrapper.appendChild(input);

    // Use the new PlaceAutocompleteElement instead of Autocomplete
    const autocompleteOptions = {
      types: ['address'],
      componentRestrictions: { country: 'us' }
    };

    // Create the autocomplete element
    try {
      // For browsers with the new Place Autocomplete Element support
      if (google.maps.places.PlaceAutocompleteElement) {
        const autocompleteElement = new google.maps.places.PlaceAutocompleteElement({
          inputElement: input,
          types: ['address'],
          componentRestrictions: { country: 'us' },
          fields: ['geometry.location', 'address_components', 'formatted_address']
        });

        // Listen for place selection
        autocompleteElement.addListener('place_changed', async () => {
          const place = await autocompleteElement.getPlace();
          handlePlaceSelection(place, input);
        });
      } else {
        throw new Error('PlaceAutocompleteElement not available');
      }
    } catch (e) {
      // Fallback to the older Autocomplete if PlaceAutocompleteElement is not available
      console.warn('PlaceAutocompleteElement not available, falling back to Autocomplete');
      try {
        const autocomplete = new google.maps.places.Autocomplete(input, autocompleteOptions);

        // Add listener for place selection
        autocomplete.addListener('place_changed', function() {
          const place = autocomplete.getPlace();
          handlePlaceSelection(place, input);
        });
      } catch (error) {
        console.error('Failed to initialize any autocomplete:', error);
      }
    }
  });
}

// Handle a selected place from either autocomplete version
function handlePlaceSelection(place, input) {
  if (!place || !place.geometry) {
    console.log("No details available for this place");
    return;
  }

  // Extract address components
  let addressData = {
    latitude: place.geometry.location.lat(),
    longitude: place.geometry.location.lng(),
    fullAddress: input.value
  };

  // Process address components
  place.address_components.forEach(component => {
    const types = component.types;

    if (types.includes('street_number')) {
      addressData.streetNumber = component.long_name;
    } else if (types.includes('route')) {
      addressData.street = component.long_name;
    } else if (types.includes('locality')) {
      addressData.city = component.long_name;
    } else if (types.includes('administrative_area_level_1')) {
      addressData.state = component.short_name;
    } else if (types.includes('postal_code')) {
      addressData.zip = component.long_name;
    } else if (types.includes('administrative_area_level_2')) {
      addressData.county = component.long_name.replace(' County', '');
    }
  });

  // Find related inputs to fill
  const formGroup = findParentForm(input);
  if (formGroup) {
    // Fill address field with street number and name if available
    if (addressData.streetNumber && addressData.street) {
      document.getElementById('address').value = `${addressData.streetNumber} ${addressData.street}`;
    } else {
      // Otherwise use the formatted address as a fallback
      const addressParts = place.formatted_address.split(',');
      document.getElementById('address').value = addressParts[0];
    }

    // Fill city field
    if (addressData.city && document.getElementById('city')) {
      document.getElementById('city').value = addressData.city;
    }

    // Handle the state dropdown - set both the dropdown and hidden field
    if (addressData.state) {
      const stateDropdown = document.getElementById('state_select');
      const stateHidden = document.getElementById('state');

      if (stateDropdown && stateHidden) {
        // State dropdown needs to be set by value, and we need to find the matching option
        setDropdownByStateCode(stateDropdown, addressData.state);
        stateHidden.value = addressData.state;

        // Trigger a change event to load counties
        const event = new Event('change');
        stateDropdown.dispatchEvent(event);

        // After state is selected and counties are loaded, we need to set the county
        setTimeout(() => {
          if (addressData.county) {
            const countyDropdown = document.getElementById('county_select');
            const countyHidden = document.getElementById('county');

            if (countyDropdown && countyHidden) {
              setDropdownByText(countyDropdown, addressData.county);
              countyHidden.value = addressData.county;

              // Trigger a change event to load zip codes
              const event = new Event('change');
              countyDropdown.dispatchEvent(event);

              // After county is selected and zip codes are loaded, we need to set the zip
              setTimeout(() => {
                if (addressData.zip) {
                  const zipDropdown = document.getElementById('zip_select');
                  if (zipDropdown) {
                    setDropdownByValue(zipDropdown, addressData.zip);

                    // Trigger a change event to load any zip-related fields
                    const event = new Event('change');
                    zipDropdown.dispatchEvent(event);
                  }
                }
              }, 1000); // Wait for zip codes to load
            }
          }
        }, 1000); // Wait for counties to load
      } else {
        // Fallback to the old text input if dropdown isn't available
        if (document.getElementById('state')) {
          document.getElementById('state').value = addressData.state;
        }
        if (document.getElementById('county')) {
          document.getElementById('county').value = addressData.county || '';
        }
        if (document.getElementById('zip_code')) {
          document.getElementById('zip_code').value = addressData.zip || '';
        }
      }
    }

    // Store lat/lng in hidden fields if they exist
    if (document.getElementById('latitude')) {
      document.getElementById('latitude').value = addressData.latitude;
    }
    if (document.getElementById('longitude')) {
      document.getElementById('longitude').value = addressData.longitude;
    }

    // Trigger owner lookup if button exists
    const lookupBtn = document.getElementById('refreshOwnerDataBtn');
    if (lookupBtn && addressData.city && addressData.state) {
      lookupBtn.removeAttribute('disabled');
      // Auto-fetch property data
      fetchPropertyData();
    }
  }
}

// Helper function to set dropdown by state code (abbreviation)
function setDropdownByStateCode(dropdown, stateCode) {
  if (!dropdown) return false;

  for (let i = 0; i < dropdown.options.length; i++) {
    const option = dropdown.options[i];
    if (option.dataset.abbr === stateCode) {
      dropdown.selectedIndex = i;
      return true;
    }
  }
  return false;
}

// Helper function to set dropdown by option value
function setDropdownByValue(dropdown, value) {
  if (!dropdown) return false;

  for (let i = 0; i < dropdown.options.length; i++) {
    if (dropdown.options[i].value === value) {
      dropdown.selectedIndex = i;
      return true;
    }
  }
  return false;
}

// Helper function to set dropdown by option text
function setDropdownByText(dropdown, text) {
  if (!dropdown) return false;

  for (let i = 0; i < dropdown.options.length; i++) {
    if (dropdown.options[i].text.indexOf(text) !== -1) {
      dropdown.selectedIndex = i;
      return true;
    }
  }
  return false;
}

// Find the parent form of an element
function findParentForm(element) {
  let parent = element.parentElement;
  while (parent && parent.tagName !== 'FORM') {
    parent = parent.parentElement;
  }
  return parent;
}

// Handle owner data lookup via API
function fetchPropertyData() {
  const address = document.getElementById('address')?.value;
  const city = document.getElementById('city')?.value;
  const state = document.getElementById('state')?.value;
  const zip = document.getElementById('zip_code')?.value;

  if (!address || !city || !state) {
    showNotification('Please complete the address fields first', 'warning');
    return;
  }

  // Show loading state
  const loadingOverlay = document.getElementById('loadingOverlay');
  if (loadingOverlay) {
    loadingOverlay.classList.add('active');
    document.getElementById('loadingMessage').textContent = 'Retrieving property information...';
  }

  const btn = document.getElementById('refreshOwnerDataBtn');
  if (btn) {
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    btn.disabled = true;
  }

  // Make API call to our backend - use relative URL instead of hardcoded localhost
  fetch('/api/locations/property', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    },
    body: JSON.stringify({
      address,
      city,
      state,
      zip
    })
  })
  .then(response => {
    if (!response.ok) {
      // Try to parse error message from response
      return response.json().then(errorData => {
        throw new Error(errorData.error || 'Failed to retrieve owner data');
      }).catch(() => {
        throw new Error('Failed to retrieve owner data');
      });
    }
    return response.json();
  })
  .then(data => {
    if (data.success) {
      // Populate owner fields with null checks
      const owner1Field = document.getElementById('owner1');
      if (owner1Field) owner1Field.value = data.property.owner1 || '';

      const owner2Field = document.getElementById('owner2');
      if (owner2Field) owner2Field.value = data.property.owner2 || '';

      const ownerAddrField = document.getElementById('ownerAddr');
      if (ownerAddrField) ownerAddrField.value = data.property.ownerAddress || '';

      const ownerPhoneField = document.getElementById('ownerPhone');
      if (ownerPhoneField) ownerPhoneField.value = data.property.ownerPhone || '';

      const ownerEmailField = document.getElementById('ownerEmail');
      if (ownerEmailField) ownerEmailField.value = data.property.ownerEmail || '';

      const dwellingTypeField = document.getElementById('dwellingType');
      if (dwellingTypeField) dwellingTypeField.value = data.property.dwellingType || '';

      // Display the owner info card (with null checks)
      const ownerInfoCard = document.getElementById('ownerInfoCard');
      if (ownerInfoCard) ownerInfoCard.style.display = 'block';

      const ownerInfoDescription = document.querySelector('.owner-info-description');
      if (ownerInfoDescription) ownerInfoDescription.style.display = 'none';

      const ownerInfoDetails = document.querySelector('.owner-info-details');
      if (ownerInfoDetails) ownerInfoDetails.style.display = 'block';

      showNotification('Owner information loaded successfully', 'success');
    } else {
      showNotification('Failed to load owner information', 'error');
    }
  })
  .catch(error => {
    console.error('Error fetching owner data:', error);

    // Parse the error response to show appropriate message
    let errorMessage = 'Failed to retrieve property data';

    if (error.message) {
      errorMessage = error.message;
    }

    showNotification(errorMessage, 'error');

    // Do not populate fields with mock data - leave them empty
    // This ensures users only see real property information or clear error messages
  })
  .finally(() => {
    // Reset UI
    if (loadingOverlay) {
      loadingOverlay.classList.remove('active');
    }

    if (btn) {
      btn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Owner Data';
      btn.disabled = false;
    }
  });
}

// Get street view image for an address
function getPropertyStreetView(address, city, state, callback) {
  // Make actual API call to get street view image - use relative URL instead of hardcoded localhost
  fetch('/api/locations/streetview', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    },
    body: JSON.stringify({
      address,
      city,
      state
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Failed to retrieve street view image');
    }
    return response.json();
  })
  .then(data => {
    if (data.success) {
      callback(data.imageUrl);
    } else {
      throw new Error('No image returned');
    }
  })
  .catch(error => {
    console.error('Error fetching street view:', error);
    // Fallback to placeholder image if API fails
    const encodedAddress = encodeURIComponent(`${address}, ${city}, ${state}`);
    callback(`https://picsum.photos/800/600?random=${encodedAddress}`);
  });
}

/**
 * Initialize Google Map for displaying incidents
 * This function initializes a Google Map and displays incidents on it
 */
function initMap() {
  console.log('Initializing map...');
  const mapElement = document.getElementById('map');
  const mapPlaceholder = document.getElementById('map-placeholder');

  if (!mapElement) {
    console.error('Map element not found');
    return;
  }

  // Create map instance with default options
  const map = new google.maps.Map(mapElement, {
    center: { lat: 39.8283, lng: -98.5795 }, // Center of US by default
    zoom: 4,
    mapTypeId: google.maps.MapTypeId.ROADMAP,
    mapTypeControl: true,
    mapTypeControlOptions: {
      style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
      position: google.maps.ControlPosition.TOP_RIGHT
    },
    streetViewControl: true,
    fullscreenControl: true
  });

  // Hide the placeholder once map has loaded
  if (mapPlaceholder) {
    google.maps.event.addListenerOnce(map, 'idle', function() {
      mapPlaceholder.style.display = 'none';
    });
  }

  // Fetch and display incidents
  fetchAndDisplayIncidents(map);
}

/**
 * Fetch incidents from the API and display them as markers on the map
 * @param {google.maps.Map} map - The Google Map instance
 */
function fetchAndDisplayIncidents(map) {
  // Check if API object exists
  if (typeof API === 'undefined' || !API.incidents) {
    console.error('API object not found or incidents API not available');
    showNotification('Error loading incident data for map.', 'error');
    return;
  }

  // Fetch incidents with location data for map display
  API.incidents.getAll({ includeLocation: true })
    .then(response => {
      console.log('Map incidents data received:', response);
      let incidents = [];

      // Handle standardized API response format
      if (response && response.success && response.data && Array.isArray(response.data)) {
        incidents = response.data;
      } else if (response && response.data && Array.isArray(response.data)) {
        incidents = response.data;
      } else if (Array.isArray(response)) {
        incidents = response;
      } else if (response && response.incidents && Array.isArray(response.incidents)) {
        incidents = response.incidents;
      } else {
        console.error('Unexpected API response format:', response);
        return;
      }

      // Filter out incidents without location data
      const geoIncidents = incidents.filter(incident => {
        return incident.latitude && incident.longitude &&
               !isNaN(parseFloat(incident.latitude)) &&
               !isNaN(parseFloat(incident.longitude));
      });

      if (geoIncidents.length === 0) {
        console.warn('No incidents with valid coordinates found');
        showNotification('No incidents with location data available.', 'info');
        return;
      }

      // Create markers for each incident with location data
      const bounds = new google.maps.LatLngBounds();
      const markers = geoIncidents.map(incident => {
        const position = {
          lat: parseFloat(incident.latitude),
          lng: parseFloat(incident.longitude)
        };

        // Extend map bounds to include this point
        bounds.extend(position);

        // Determine marker icon based on incident type
        const isFireIncident = incident.incidentType &&
                              (incident.incidentType.category === 'fire' ||
                               incident.incidentType.name?.toLowerCase().includes('fire'));

        const markerIcon = {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: isFireIncident ? '#e53935' : '#2196f3', // Red for fire, blue for water
          fillOpacity: 0.8,
          strokeWeight: 2,
          strokeColor: isFireIncident ? '#b71c1c' : '#0d47a1',
          scale: 10
        };

        // Create the marker
        const marker = new google.maps.Marker({
          position: position,
          map: map,
          icon: markerIcon,
          title: incident.title || 'Incident',
          animation: google.maps.Animation.DROP,
          zIndex: isFireIncident ? 10 : 5 // Fire incidents shown on top
        });

        // Generate info window content
        const infoContent = `
          <div class="marker-info-content">
            <h3>${incident.title || 'Untitled Incident'}</h3>
            <p>${incident.incidentType?.name || 'Unknown Type'}</p>
            <p>${incident.address || ''}${incident.address && incident.city ? ', ' : ''}${incident.city || ''}</p>
            <p>${incident.status?.name || 'Unknown Status'}</p>
            <div class="marker-actions">
              <a href="view-incident.html?id=${incident.id}" class="info-btn">View Details</a>
            </div>
          </div>
        `;

        // Create info window
        const infoWindow = new google.maps.InfoWindow({
          content: infoContent,
          maxWidth: 300
        });

        // Add click listener to open info window
        marker.addListener('click', () => {
          // Close any open info windows first
          if (window.currentOpenInfoWindow) {
            window.currentOpenInfoWindow.close();
          }

          infoWindow.open(map, marker);
          window.currentOpenInfoWindow = infoWindow;
        });

        return marker;
      });

      // Adjust map to fit all markers
      if (markers.length > 0) {
        map.fitBounds(bounds);

        // If only one marker, zoom in a bit more
        if (markers.length === 1) {
          map.setZoom(15);
        } else if (map.getZoom() > 15) {
          map.setZoom(15); // Don't zoom in too far if bounds are small
        }
      }
    })
    .catch(error => {
      console.error('Error fetching incidents for map:', error);
      showNotification('Failed to load incidents on map.', 'error');
    });
}

// Initialize all Google Maps integrations
document.addEventListener('DOMContentLoaded', function() {
  // Wait for Google Maps API to load
  if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
    initializeGoogleAddressAutocomplete();
  } else {
    // If Google Maps isn't loaded yet, wait for the maps-loaded event
    window.addEventListener('maps-loaded', initializeGoogleAddressAutocomplete);
  }

  // Set up owner info card
  const ownerInfoCard = document.getElementById('ownerInfoCard');
  if (ownerInfoCard) {
    // Initially hide the details section
    const detailsSection = ownerInfoCard.querySelector('.owner-info-details');
    if (detailsSection) {
      detailsSection.style.display = 'none';
    }
  }

  // Hook up refresh button
  const refreshBtn = document.getElementById('refreshOwnerDataBtn');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', fetchPropertyData);
  }
});

// Function to show success/error message (backup in case modern-dispatch.js isn't loaded)
function showNotification(message, type = 'success') {
  // IMPORTANT: Prevent recursive calls that would cause maximum stack size exceeded
  // Only use the global function if it's not THIS function
  if (typeof window.modernDispatchShowNotification === 'function' &&
      window.modernDispatchShowNotification !== showNotification) {
    window.modernDispatchShowNotification(message, type);
    return;
  }

  // Simple default implementation if no global function exists
  const notificationContainer = document.getElementById('notificationContainer') ||
    (() => {
      const container = document.createElement('div');
      container.id = 'notificationContainer';
      container.style.position = 'fixed';
      container.style.top = '20px';
      container.style.right = '20px';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
      return container;
    })();

  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.backgroundColor = type === 'success' ? '#4caf50' :
                                     type === 'error' ? '#f44336' :
                                     type === 'warning' ? '#ff9800' : '#2196f3';
  notification.style.color = 'white';
  notification.style.padding = '15px';
  notification.style.marginBottom = '10px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
  notification.style.minWidth = '250px';
  notification.textContent = message;

  notificationContainer.appendChild(notification);

  // Remove notification after 5 seconds
  setTimeout(() => {
    if (notification.parentNode === notificationContainer) {
      notificationContainer.removeChild(notification);
    }
  }, 5000);
}

// Export functions for use in other scripts
window.fireAlerts = window.fireAlerts || {};
window.fireAlerts.maps = {
  initializeAutocomplete: initializeGoogleAddressAutocomplete,
  fetchPropertyData: fetchPropertyData,
  getPropertyStreetView: getPropertyStreetView,
  initMap: initMap,
  fetchAndDisplayIncidents: fetchAndDisplayIncidents
};
